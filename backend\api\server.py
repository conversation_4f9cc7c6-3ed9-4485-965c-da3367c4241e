"""
API服务器模块，负责创建和配置FastAPI应用
"""
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import asyncio
from contextlib import asynccontextmanager
import json
import sys

from fastapi import FastAPI, APIRouter, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, FileResponse
import uvicorn
from dotenv import load_dotenv

if sys.platform.startswith('darwin'):
    if '/usr/local/bin' not in os.environ.get('PATH', ''):
        os.environ['PATH'] = '/usr/local/bin:' + os.environ.get('PATH', '')
# 应用MCP错误过滤器（必须在导入其他模块之前）
if sys.platform == "win32":
    try:
        from backend.mcp.async_error_filter import install_async_error_filter, suppress_mcp_async_errors
        install_async_error_filter()
        # 设置自定义异常处理器
        def setup_exception_handler():
            try:
                loop = asyncio.get_event_loop()
                suppress_mcp_async_errors()
            except:
                pass
        # 延迟设置，确保事件循环已创建
        asyncio.get_event_loop().call_soon(setup_exception_handler)
    except Exception as e:
        print(f"无法安装MCP错误过滤器: {e}")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取应用根目录
ROOT_DIR = Path(__file__).parent.parent.parent
STATIC_DIR = ROOT_DIR / "view" / "build"

# 检查前端构建目录是否存在
if not STATIC_DIR.exists():
    # 开发模式下可能没有build目录，尝试使用public目录
    STATIC_DIR = ROOT_DIR / "view" / "public"
    logger.warning(f"build目录不存在，使用public目录: {STATIC_DIR}")

# 生产环境下，也检查static目录
STATIC_DIR_PROD = ROOT_DIR / "backend" / "static"
if STATIC_DIR_PROD.exists():
    STATIC_DIR = STATIC_DIR_PROD
    logger.info(f"使用生产环境静态目录: {STATIC_DIR}")

logger.info(f"静态文件目录: {STATIC_DIR}")

# 全局插件适配器实例
plugin_adapter = None

# 插件系统将通过独立的路由模块管理

# --- Lifespan Context Manager ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时执行
    logger.info("应用启动 (lifespan)，初始化资源...")
    
    # 初始化插件系统
    global plugin_adapter
    try:
        from backend.plugin_system.jimu_adapter import JimuPluginAdapter
        plugin_adapter = JimuPluginAdapter(app)
        
        logger.info("正在初始化插件系统...")
        await plugin_adapter.initialize()
        
        logger.info("正在发现和加载插件...")
        await plugin_adapter.discover_and_load_plugins()
        
        logger.info("正在注册插件API路由...")
        await plugin_adapter.register_all_plugin_routes()
        
        # 获取已加载的插件信息
        loaded_plugins = list(plugin_adapter.plugin_manager.loaded_plugins.keys())
        logger.info(f"插件系统初始化完成，已加载插件: {loaded_plugins}")
        
        # 如果没有插件自动加载，尝试手动加载research_agent
        if not loaded_plugins:
            logger.info("未检测到自动加载的插件，尝试手动加载research_agent...")
            try:
                success = await plugin_adapter.plugin_manager.load_plugin("research_agent")
                if success:
                    logger.info("research_agent插件手动加载成功")
                    await plugin_adapter.register_all_plugin_routes()
                else:
                    logger.warning("research_agent插件手动加载失败")
            except Exception as load_error:
                logger.error(f"手动加载插件失败: {load_error}")
        
        # 将插件适配器引用传递给插件路由模块
        plugin_routes.set_plugin_adapter(plugin_adapter)
        logger.info("插件路由系统已完成初始化")
        
    except Exception as e:
        logger.error(f"插件系统初始化失败: {e}")
        import traceback
        traceback.print_exc()
        # 不要因为插件系统失败而阻止应用启动
    
    # 在所有插件路由注册完成后，注册前端catch-all路由
    # 这样确保插件路由有更高的优先级
    register_frontend_routes()
    
    logger.info("应用资源初始化完成。")
    
    yield # 应用运行
    
    # 应用关闭时执行
    logger.info("应用关闭 (lifespan)，清理资源...")
    
    # 清理插件系统
    if plugin_adapter:
        try:
            logger.info("正在清理插件系统...")
            await plugin_adapter.cleanup()
            logger.info("插件系统清理完成")
        except Exception as e:
            logger.error(f"清理插件系统时出错: {e}")
    
    # 关闭所有WebSocket连接
    try:
        from backend.api.websocket import manager
        await manager.close_all()
        logger.info("WebSocket 连接已关闭。")
    except Exception as e:
        logger.error(f"关闭WebSocket连接时出错: {str(e)}")
    logger.info("资源清理完成")
# --- Lifespan End ---

# 创建FastAPI应用, 使用 lifespan 参数
app = FastAPI(title="AI Chat Desktop API", lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*", "http://localhost:3000"],  # 添加前端开发服务器地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 从routes模块导入路由
# 注意: 这里的导入位置很重要，必须在创建app之后
from backend.api.routes import (
    chat_routes,
    tool_routes,
    conversation_routes,
    mcp_routes,
    model_routes,
    setting_routes,
    proxy_routes,
    marketplace_routes,
    role_routes,
    team_routes,
    research_routes,
    plugin_routes,
    translation_routes,
    search_routes,
    svg_ppt_routes,
    dxt_routes
)

# 注册路由
app.include_router(chat_routes.router, prefix="/ws")
app.include_router(tool_routes.router, prefix="/api")
app.include_router(conversation_routes.router, prefix="/api")
app.include_router(mcp_routes.router, prefix="/api")
app.include_router(model_routes.router, prefix="/api")
app.include_router(setting_routes.router, prefix="/api")
app.include_router(proxy_routes.router, prefix="/api")
app.include_router(marketplace_routes.router, prefix="/api")
app.include_router(role_routes.router, prefix="/api")
app.include_router(team_routes.router, prefix="/api")
app.include_router(research_routes.router, prefix="/api")
app.include_router(plugin_routes.router, prefix="/api")
app.include_router(translation_routes.router, prefix="/api")
app.include_router(search_routes.router, prefix="/api", tags=["search"])
app.include_router(svg_ppt_routes.router, prefix="/api/svg-ppt", tags=["svg-ppt"])
app.include_router(dxt_routes.router, prefix="/api", tags=["dxt"])

# 插件路由已移动到独立的路由模块中

# 插件自定义路由通过静态路由器处理

# 重要：先挂载静态文件，确保静态资源请求不被下面的路由捕获
# 挂载静态文件目录，用于提供JS、CSS等静态资源
static_dir_path = STATIC_DIR / "static"
if not static_dir_path.exists():
    logger.info(f"静态目录不存在，创建目录: {static_dir_path}")
    static_dir_path.mkdir(parents=True, exist_ok=True)
app.mount("/static", StaticFiles(directory=str(static_dir_path)), name="static")

# 挂载其他静态资源目录
images_dir = STATIC_DIR / "images"
if not images_dir.exists():
    logger.info(f"图片目录不存在，创建目录: {images_dir}")
    images_dir.mkdir(parents=True, exist_ok=True)
app.mount("/images", StaticFiles(directory=str(images_dir)), name="images")

videos_dir = STATIC_DIR / "videos"
if not videos_dir.exists():
    logger.info(f"视频目录不存在，创建目录: {videos_dir}")
    videos_dir.mkdir(parents=True, exist_ok=True)
app.mount("/videos", StaticFiles(directory=str(videos_dir)), name="videos")
# 不再挂载根目录，改为由catch-all路由处理所有请求
# app.mount("/", StaticFiles(directory=str(STATIC_DIR), html=True), name="root")

# 定义前端路由处理函数，但不立即注册为路由
async def serve_front_end(full_path: str):
    # 排除API和WebSocket请求
    logger.info(f"请求路径: '{full_path}'")
    if full_path.startswith("api/") or full_path.startswith("ws/"):
        logger.info(f"API或WebSocket请求，交由相应路由处理: {full_path}")
        raise HTTPException(status_code=404, detail="Not Found")
    
    # 处理根路径
    if full_path == "":
        index_path = STATIC_DIR / "index.html"
        logger.info(f"返回根路径的index.html")
        return FileResponse(index_path)
    
    # 检查是否为前端路由路径（如/chat, /settings等）
    if "/" in full_path or full_path in ["chat", "settings", "image"]:
        logger.info(f"检测到前端路由路径: {full_path}")
        index_path = STATIC_DIR / "index.html"
        if not index_path.exists():
            raise HTTPException(status_code=404, detail="Frontend not built")
        logger.info(f"返回index.html用于前端路由: {full_path}")
        return FileResponse(index_path)
    
    # 检查请求的路径是否是静态文件
    static_file_path = STATIC_DIR / full_path
    if static_file_path.exists() and static_file_path.is_file():
        logger.info(f"提供静态文件: {static_file_path}")
        return FileResponse(static_file_path)
    
    # 对于所有其他请求，返回index.html，让前端路由处理
    index_path = STATIC_DIR / "index.html"
    if not index_path.exists():
        raise HTTPException(status_code=404, detail="Frontend not built")
    
    # 前端路由请求，返回index.html
    logger.info(f"默认返回index.html: {full_path}")
    return FileResponse(index_path)

# 注册catch-all路由的函数，将在插件路由注册完成后调用
def register_frontend_routes():
    """注册前端路由处理器，必须在所有API路由注册完成后调用"""
    app.get("/{full_path:path}")(serve_front_end)
    logger.info("前端catch-all路由已注册")

def start_api_server():
    """启动API服务器"""
    # 注意：这里使用127.0.0.1而不是localhost，避免某些环境下的DNS解析问题
    # 设置 WebSocket 最大消息大小，例如 16MB (单位：字节)
    ws_max_size_bytes = 16 * 1024 * 1024 
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=8080,
        ws_max_size=ws_max_size_bytes, # 添加 WebSocket 最大消息大小限制
        access_log=False,  # 禁用访问日志
        log_level="warning"  # 设置日志级别为警告，减少info日志输出
    )