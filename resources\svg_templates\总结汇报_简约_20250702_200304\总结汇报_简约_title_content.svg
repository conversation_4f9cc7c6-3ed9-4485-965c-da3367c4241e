<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 定义颜色和渐变 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradientTransparent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#BAE6FD" stop-opacity="0.2" />
      <stop offset="100%" stop-color="#BAE6FD" stop-opacity="0.8" />
    </linearGradient>
    <linearGradient id="accentGradientTransparentRight" x1="100%" y1="0%" x2="0%" y2="0%">
      <stop offset="0%" stop-color="#BAE6FD" stop-opacity="0.2" />
      <stop offset="100%" stop-color="#BAE6FD" stop-opacity="0.8" />
    </linearGradient>

    <!-- 定义字体样式 -->
    <style type="text/css"><![CDATA[
      /* 通用样式 */
      .bg-color {
        fill: url(#backgroundGradient);
      }
      .text-primary {
        fill: #1E293B;
      }
      .text-secondary {
        fill: #64748B;
      }
      .text-light {
        fill: #94A3B8;
      }
      .primary-color-fill {
        fill: #3B82F6;
      }
      .primary-color-stroke {
        stroke: #3B82F6;
      }
      .accent-color-fill {
        fill: #BAE6FD;
      }
      .accent-color-stroke {
        stroke: #BAE6FD;
      }
      .secondary-color-fill {
        fill: #7DD3FC;
      }
      .card-background {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1;
      }

      /* 字体样式 */
      .font-inter {
        font-family: "Inter", "Helvetica", "Arial", sans-serif;
      }
      .font-poppins {
        font-family: "Poppins", sans-serif;
      }
      .font-sf-pro {
        font-family: "SF Pro Display", system-ui, sans-serif;
      }

      .main-title {
        font-size: 56px;
        font-weight: 700; /* bold */
        line-height: 1.1; /* tight */
        text-anchor: middle;
      }
      .section-title {
        font-size: 36px;
        font-weight: 600; /* semibold */
        line-height: 1.4; /* normal */
      }
      .content-title {
        font-size: 28px;
        font-weight: 600; /* semibold */
        line-height: 1.4; /* normal */
      }
      .body-text {
        font-size: 22px;
        font-weight: 400; /* normal */
        line-height: 1.6; /* relaxed */
      }
      .small-text {
        font-size: 16px;
        font-weight: 400; /* normal */
        line-height: 1.4; /* normal */
      }
      .caption-text {
        font-size: 14px;
        font-weight: 400; /* normal */
        line-height: 1.4; /* normal */
      }

      /* 超大数字样式 */
      .super-large-number {
        font-size: 180px; /* 自定义大尺寸 */
        font-weight: 900; /* black */
        fill: url(#primaryGradient);
        text-anchor: middle;
      }
      .super-large-caption {
        font-size: 32px;
        font-weight: 600;
        fill: #1E293B;
        text-anchor: middle;
      }

      /* 列表项圆点样式 */
      .bullet-marker {
        fill: #3B82F6; /* 主色 */
      }
    ]]></style>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- 页码 (右上角) -->
  <text x="1840" y="50" text-anchor="end" class="small-text text-light font-inter">4/10</text>

  <!-- Logo 占位符 (左上角) -->
  <image x="80" y="40" width="120" height="auto" href="{logo_url}" />

  <!-- 主标题 -->
  <text x="960" y="150" class="main-title text-primary font-inter">{title}</text>

  <!-- 副标题 -->
  <text x="960" y="220" class="section-title text-secondary font-inter">{subtitle}</text>

  <!-- 内容区域 - Bento Grid 风格 - 主卡片 -->
  <!-- 卡片尺寸: 1920 - 2*80 = 1760 宽度。高度根据内容调整。 -->
  <rect x="100" y="300" width="1720" height="700" class="card-background" />

  <!-- 超大视觉元素 (卡片右侧) -->
  <text x="1400" y="550" class="super-large-number font-poppins">85%</text>
  <text x="1400" y="600" class="super-large-caption font-inter text-secondary">
    <tspan x="1400" dy="0">项目完成度</tspan>
    <tspan x="1400" dy="40" class="small-text text-light">Project Completion</tspan>
  </text>
  
  <!-- 简洁勾线图形 (超大数字左侧) -->
  <g transform="translate(1100, 480)">
    <rect x="0" y="0" width="160" height="160" fill="none" stroke="#7DD3FC" stroke-width="4" />
    <line x1="0" y1="160" x2="160" y2="0" stroke="#7DD3FC" stroke-width="4" />
    <circle cx="80" cy="80" r="40" fill="none" stroke="#3B82F6" stroke-width="4" />
  </g>

  <!-- 主要正文内容 (卡片左侧) -->
  <text x="140" y="360" class="content-title text-primary font-inter">核心执行情况</text>
  <text x="140" y="400" class="body-text text-secondary font-inter">
    <tspan x="140" dy="0">{content}</tspan>
    <tspan x="140" dy="30">本阶段项目进展顺利，我们按照既定计划完成了大部分核心任务。</tspan>
    <tspan x="140" dy="30">团队协作效率高，技术攻关取得突破，为后续工作奠定坚实基础。</tspan>
  </text>

  <!-- 要点列表 (卡片左侧，正文下方) -->
  <text x="140" y="530" class="content-title text-primary font-inter">要点列表</text>

  <!-- 列表项 1 -->
  <circle cx="150" cy="590" r="6" class="bullet-marker" />
  <text x="170" y="590" class="body-text text-primary font-inter">
    <tspan x="170" dy="0">功能模块开发：</tspan>
    <tspan x="170" dy="35" class="body-text text-secondary">完成了核心模块的开发和初步测试，符合预期设计。</tspan>
  </text>

  <!-- 列表项 2 -->
  <circle cx="150" cy="670" r="6" class="bullet-marker" />
  <text x="170" y="670" class="body-text text-primary font-inter">
    <tspan x="170" dy="0">数据分析优化：</tspan>
    <tspan x="170" dy="35" class="body-text text-secondary">引入了新的数据处理算法，提升了数据分析的准确性和效率。</tspan>
  </text>

  <!-- 列表项 3 -->
  <circle cx="150" cy="750" r="6" class="bullet-marker" />
  <text x="170" y="750" class="body-text text-primary font-inter">
    <tspan x="170" dy="0">用户反馈收集：</tspan>
    <tspan x="170" dy="35" class="body-text text-secondary">通过多渠道收集用户反馈，为下一阶段迭代提供重要依据。</tspan>
  </text>
  
  <!-- 列表项 4 -->
  <circle cx="150" cy="830" r="6" class="bullet-marker" />
  <text x="170" y="830" class="body-text text-primary font-inter">
    <tspan x="170" dy="0">团队协作效率：</tspan>
    <tspan x="170" dy="35" class="body-text text-secondary">优化了内部沟通流程，团队协作更加紧密和高效。</tspan>
  </text>

  <!-- 装饰元素 - 卡片底部细微渐变线 -->
  <rect x="100" y="990" width="1720" height="10" fill="url(#accentGradientTransparent)" />

  <!-- 装饰元素 - 角落点缀 -->
  <rect x="80" y="60" width="4" height="60" class="primary-color-fill" />
  <rect x="80" y="60" width="60" height="4" class="primary-color-fill" />

  <rect x="1836" y="960" width="4" height="60" class="primary-color-fill" />
  <rect x="1776" y="1016" width="60" height="4" class="primary-color-fill" />

</svg>