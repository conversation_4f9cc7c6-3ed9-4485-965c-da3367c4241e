<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <title>年中总结汇报 - 图文混排页</title>
  <desc>
    高质量SVG模板，适用于年中总结汇报，采用商务风格。
    布局为图文左右分栏，确保视觉平衡，无内容重叠，无“和”符号。
  </desc>

  <defs>
    <!-- 统一蓝色系配色方案 -->
    <style type="text/css">
      .background-color { fill: #F8FAFC; }
      .primary-color-fill { fill: #1E40AF; }
      .secondary-color-fill { fill: #475569; }
      .accent-color-fill { fill: #3B82F6; }
      .text-primary-fill { fill: #1E293B; }
      .text-secondary-fill { fill: #64748B; }
      .text-light-fill { fill: #94A3B8; }
      .card-background-fill { fill: #FFFFFF; }
      .card-border-stroke { stroke: #BAE6FD; }
      .container-background-fill { fill: #E0F2FE; }

      /* 字体系统 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* 字体大小和粗细 */
      .main-title { font-size: 56px; font-weight: 700; fill: #1E293B; } /* text-primary */
      .section-title { font-size: 36px; font-weight: 700; fill: #1E293B; } /* text-primary */
      .content-title { font-size: 28px; font-weight: 600; fill: #1E293B; } /* text-primary */
      .body-text { font-size: 22px; font-weight: 400; fill: #64748B; line-height: 1.4; } /* text-secondary */
      .small-text { font-size: 16px; font-weight: 400; fill: #94A3B8; } /* text-light */
      .caption-text { font-size: 14px; font-weight: 400; fill: #94A3B8; } /* text-light */
      .large-number { font-size: 72px; font-weight: 700; fill: #1E40AF; } /* primary-color */

      /* 阴影效果 */
      .card-shadow {
        filter: url(#shadowFilter);
      }
    </style>

    <!-- 渐变背景定义 -->
    <linearGradient id="gradientPrimary" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="gradientAccent" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- 高亮色自身透明度渐变制造科技感（使用强调色） -->
    <linearGradient id="techAccentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4" />
      <feColorMatrix result="shadowColor" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0" />
      <feBlend in="SourceGraphic" in2="shadowColor" mode="normal" />
    </filter>
  </defs>

  <!-- 页面背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />

  <!-- 顶部装饰条 -->
  <rect x="0" y="0" width="1920" height="10" fill="url(#techAccentGradient)" />

  <!-- Logo占位符 -->
  <g id="logo-section">
    <text x="80" y="60" class="small-text font-primary primary-color-fill" font-weight="700">
      <tspan x="80" y="60">{logo_url}</tspan>
    </text>
    <!-- 实际使用时可替换为图片：<image x="80" y="30" width="150" height="50" href="{logo_url}" /> -->
  </g>

  <!-- 页码和日期 -->
  <g id="page-info">
    <text x="1760" y="60" text-anchor="end" class="small-text font-primary text-light-fill">
      <tspan x="1760" y="60">5/10</tspan>
    </text>
    <text x="1760" y="85" text-anchor="end" class="caption-text font-primary text-light-fill">
      <tspan x="1760" y="85">{date}</tspan>
    </text>
  </g>

  <!-- 主内容区域 - 模仿Bento Grid布局 -->
  <!-- 左侧栏：图片展示区域 (宽度约40%) -->
  <g id="image-section">
    <!-- 图片外框背景卡片 -->
    <rect x="80" y="140" width="800" height="600" rx="12" ry="12" class="card-background-fill card-border-stroke card-shadow" />
    
    <!-- 图片占位符区域 -->
    <!-- 实际使用时替换为：<image x="100" y="160" width="760" height="560" href="{image_url}" preserveAspectRatio="xMidYMid slice" /> -->
    <rect x="100" y="160" width="760" height="560" class="container-background-fill" rx="8" ry="8" /> 
    <text x="480" y="440" text-anchor="middle" class="section-title font-primary primary-color-fill">
        <tspan x="480" y="440">{image_url}</tspan>
        <tspan x="480" y="480" dy="30" class="small-text secondary-color-fill">图片占位符</tspan>
    </text>

    <!-- 图片说明文字 -->
    <text x="80" y="770" class="caption-text font-primary text-secondary-fill">
      <tspan x="80" y="770">图：关键成果可视化展示</tspan>
    </text>
  </g>

  <!-- 右侧栏：文字内容区域 (宽度约50%) -->
  <g id="text-content-section">
    <!-- 主标题 (中文大字体粗体，英文小字点缀) -->
    <text x="960" y="170" class="main-title font-primary text-primary-fill">
      <tspan x="960" y="170">{title}</tspan>
    </text>
    <text x="960" y="220" class="content-title font-primary secondary-color-fill">
      <tspan x="960" y="220">Mid-Year Performance Review</tspan>
    </text>

    <!-- 副标题 -->
    <text x="960" y="280" class="section-title font-primary primary-color-fill">
      <tspan x="960" y="280">{subtitle}</tspan>
    </text>

    <!-- 正文内容 (多行文本，确保行间距) -->
    <text x="960" y="350" class="body-text font-primary text-secondary-fill">
      <tspan x="960" y="350">本年度上半年，我们在各项业务指标上取得了显著进展和突破。</tspan>
      <tspan x="960" y="390" dy="30">尤其在市场拓展方面，成功进入了三个新区域，并建立了稳固的客户基础。</tspan>
      <tspan x="960" y="430" dy="30">产品创新方面，推出了两款备受市场好评的新品，极大地提升了品牌影响力。</tspan>
      <tspan x="960" y="470" dy="30">团队协作效率显著提升，内部流程优化带来了更高的生产力。</tspan>
      <tspan x="960" y="510" dy="30">这些成就的取得离不开全体成员的共同努力和奉献。</tspan>
      <tspan x="960" y="550" dy="30">我们将继续深化改革，以应对未来挑战和机遇。</tspan>
    </text>

    <!-- 超大字体或数字突出核心要点 -->
    <g id="key-metric-highlight">
      <text x="960" y="660" class="large-number font-primary primary-color-fill">
        <tspan x="960" y="660">25%</tspan>
      </text>
      <text x="960" y="700" class="content-title font-primary secondary-color-fill">
        <tspan x="960" y="700">Revenue Growth</tspan>
      </text>
      <text x="960" y="730" class="small-text font-primary text-light-fill">
        <tspan x="960" y="730">上半年营收增长率</tspan>
      </text>
    </g>

    <!-- 简洁的勾线图形化作为数据可视化元素（进度条示例） -->
    <g id="outline-graphic">
      <rect x="960" y="800" width="700" height="8" rx="4" ry="4" class="container-background-fill" />
      <rect x="960" y="800" width="550" height="8" rx="4" ry="4" class="accent-color-fill" />
      <text x="960" y="830" class="small-text font-primary primary-color-fill">
        <tspan x="960" y="830">目标完成度 78%</tspan>
      </text>
      <text x="1660" y="830" text-anchor="end" class="small-text font-primary secondary-color-fill">
        <tspan x="1660" y="830">Progress Towards Goals</tspan>
      </text>
    </g>
  </g>

  <!-- 底部装饰条 -->
  <rect x="0" y="1070" width="1920" height="10" fill="url(#techAccentGradient)" transform="scale(1, -1) translate(0, -1080)" />

  <!-- 作者占位符 -->
  <text x="80" y="1020" class="caption-text font-primary text-light-fill">
    <tspan x="80" y="1020">Presented by: {author}</tspan>
  </text>
</svg>