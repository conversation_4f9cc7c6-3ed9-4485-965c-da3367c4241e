{"set_name": "营销推广_潮流_20250702_201730", "scenario": "营销推广", "style": "潮流", "created_at": "2025-07-02T20:17:30.757277", "template_count": 10, "templates": [{"template_id": "营销推广_潮流_cover", "type": "封面页", "filename": "营销推广_潮流_cover.svg", "page_number": 1}, {"template_id": "营销推广_潮流_agenda", "type": "目录页", "filename": "营销推广_潮流_agenda.svg", "page_number": 2}, {"template_id": "营销推广_潮流_section_divider", "type": "章节分隔页", "filename": "营销推广_潮流_section_divider.svg", "page_number": 3}, {"template_id": "营销推广_潮流_title_content", "type": "标题内容页", "filename": "营销推广_潮流_title_content.svg", "page_number": 4}, {"template_id": "营销推广_潮流_image_text", "type": "图文混排页", "filename": "营销推广_潮流_image_text.svg", "page_number": 5}, {"template_id": "营销推广_潮流_data_display", "type": "数据展示页", "filename": "营销推广_潮流_data_display.svg", "page_number": 6}, {"template_id": "营销推广_潮流_comparison", "type": "对比分析页", "filename": "营销推广_潮流_comparison.svg", "page_number": 7}, {"template_id": "营销推广_潮流_timeline", "type": "时间线页", "filename": "营销推广_潮流_timeline.svg", "page_number": 8}, {"template_id": "营销推广_潮流_quote", "type": "引用页", "filename": "营销推广_潮流_quote.svg", "page_number": 9}, {"template_id": "营销推广_潮流_conclusion", "type": "总结页", "filename": "营销推广_潮流_conclusion.svg", "page_number": 10}], "combination_config": {"scenario": {"scenario_type": "营销推广", "display_name": "营销推广", "description": "产品推广、市场营销、品牌宣传", "visual_characteristics": {"emphasis_on": "吸引眼球、情感共鸣", "layout_style": "创意灵活", "decorative_elements": "品牌元素、吸引图形、强调标记"}, "content_focus": ["产品特色", "市场优势", "用户价值"], "target_audience": "潜在客户、市场", "tone": "persuasive"}, "style": {"style_type": "潮流", "display_name": "潮流", "description": "时尚前卫，跟随最新设计趋势", "design_principles": {"layout": "动态布局、创新排版", "elements": "时尚元素、流行图形", "emphasis": "时尚性、创新性"}, "visual_elements": {"shapes": "不规则形状、动态图形", "lines": "动感线条、流行元素", "decorations": "时尚图标、流行装饰"}, "typography": {"font_style": "时尚字体", "weight": "变化丰富", "spacing": "动态间距"}}, "colors": {"primary": "#4A86E8", "secondary": "#3B82F6", "accent": "#0EA5E9", "background": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B"}, "fusion_rules": {"layout_priority": "style", "color_adaptation": "balanced", "typography_blend": "harmonious", "visual_emphasis": ["视觉冲击", "品牌突出", "创意表达"]}, "aesthetic_enhancements": {"typography_refinements": {"font_pairing": "complementary", "hierarchy_enhancement": true, "readability_optimization": true}, "layout_improvements": {"golden_ratio_application": true, "visual_balance_optimization": true, "spacing_harmonization": true}, "color_harmony": {"contrast_optimization": true, "accessibility_compliance": true, "emotional_resonance": true}, "visual_elements": {"icon_style_consistency": true, "decorative_element_coordination": true, "brand_element_integration": true}}}, "design_specification": {"theme_selection": {"primary_style": "潮流风格", "scenario_adaptation": "营销推广场景优化", "visual_theme": "黑底特斯拉红高亮的潮流风格营销推广模板", "design_philosophy": "结合潜在客户、市场需求的时尚前卫，跟随最新设计趋势设计理念，通过高对比度、模块化布局和动态视觉元素激发用户兴趣与行动。", "fusion_strategy": "style优先的场景风格融合"}, "color_palette": {"primary_color": "#4A86E8", "secondary_color": "#3B82F6", "accent_color": "#0EA5E9", "background_color": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B", "text_light": "#94A3B8", "success_color": "#10B981", "warning_color": "#F59E0B", "error_color": "#EF4444", "info_color": "#0EA5E9", "gradient_primary": "linear-gradient(135deg, #4A86E8, #3B82F6)", "gradient_accent": "linear-gradient(45deg, #0EA5E9, #4A86E8)", "card_background": "#FFFFFF", "card_border": "#BAE6FD", "container_background": "#E0F2FE", "hover_color": "#7DD3FC", "active_color": "#4A86E8", "disabled_color": "#64748B"}, "layout_principles": {"canvas_size": {"width": 1920, "height": 1080}, "golden_ratio": 1.618, "grid_system": {"columns": 12, "gutter": 24, "margin": 80}, "spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "2xl": 48, "3xl": 64, "4xl": 96}, "page_margins": {"horizontal": 80, "vertical": 60, "content_max_width": 1760}, "content_spacing": {"module_gap": 32, "section_gap": 48, "element_gap": 16, "text_gap": 12}, "visual_hierarchy": {"z_index_background": 0, "z_index_content": 10, "z_index_overlay": 20, "z_index_modal": 30}, "alignment": {"text_align": "left", "content_align": "center", "title_align": "center"}}, "typography_system": {"primary_font": "Microsoft YaHei, Segoe UI, sans-serif", "secondary_font": "Source <PERSON> CN, Noto Sans CJK SC, sans-serif", "accent_font": "Times New Roman, serif", "font_sizes": {"hero_title": 72, "main_title": 56, "section_title": 36, "content_title": 28, "body_text": 22, "small_text": 16, "caption": 14}, "font_weights": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "black": 900}, "line_heights": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "letter_spacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em"}}, "visual_elements": {"card_style": {"background": "#FFFFFF", "border_radius": 12, "border": "1px solid #BAE6FD", "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "decorative_elements": ["几何图形装饰", "渐变背景", "图标点缀", "分割线", "阴影效果"], "gradients": {"primary_gradient": "linear-gradient(135deg, #4A86E8, #3B82F6)", "accent_gradient": "linear-gradient(45deg, #0EA5E9, #06B6D4)", "background_gradient": "linear-gradient(180deg, #F8FAFC, #E0F2FE)", "text_gradient": "linear-gradient(135deg, #1E3A8A, #1E40AF)"}, "image_effects": {"border_radius": 8, "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)", "overlay_style": "linear-gradient(135deg, #4A86E820, #3B82F620)", "hover_transform": "scale(1.02)", "filter_effects": "brightness(1.05) contrast(1.05)"}, "logo_effects": {"positioning": "左上角或页面顶部中央", "size_guidelines": "最大高度80px，宽度自适应", "background_treatment": "透明背景或浅色背景", "shadow": "subtle drop shadow", "hover_effect": "slight glow"}, "icon_system": {"style": "outline", "stroke_width": 2, "size_sm": 16, "size_md": 24, "size_lg": 32, "color": "#4A86E8"}, "dividers": {"horizontal_line": "1px solid #BAE6FD", "vertical_line": "1px solid #BAE6FD", "decorative_divider": "波浪线或装饰性分割", "gradient_divider": "linear-gradient(90deg, transparent, #7DD3FC, transparent)"}}, "template_consistency": {"scenario_coherence": "确保所有模板符合营销推广场景特点，以转化、价值和品牌为核心。", "style_unity": "保持潮流风格的一致性，通过统一的视觉语言和设计元素。", "color_harmony": "黑底红高亮的现代对比配色体系，贯穿所有页面，确保视觉统一性。", "visual_rhythm": "协调的视觉节奏和层次，引导用户视线，提升信息吸收效率。", "brand_consistency": "一致的品牌形象和视觉识别，强化品牌记忆点和专业度。"}, "created_at": "2025-07-02T20:15:26.648935", "scenario": "营销推广", "style": "潮流", "canvas_size": {"width": 1920, "height": 1080}, "raw_response": "```json\n{\n    \"theme_selection\": {\n        \"primary_style\": \"潮流风格\",\n        \"scenario_adaptation\": \"营销推广场景优化\",\n        \"visual_theme\": \"黑底特斯拉红高亮的潮流风格营销推广模板\",\n        \"design_philosophy\": \"结合潜在客户、市场需求的时尚前卫，跟随最新设计趋势设计理念，通过高对比度、模块化布局和动态视觉元素激发用户兴趣与行动。\",\n        \"fusion_strategy\": \"style优先的场景风格融合\"\n    },\n    \"color_palette\": {\n        \"background_color\": \"#000000\",\n        \"highlight_color\": \"#E31937\",\n        \"highlight_transparent\": \"rgba(227, 25, 55, 0.7)\",\n        \"highlight_gradient_start\": \"#E31937\",\n        \"highlight_gradient_end\": \"rgba(227, 25, 55, 0.3)\",\n        \"primary_color\": \"#4A86E8\",\n        \"secondary_color\": \"#3B82F6\",\n        \"text_primary\": \"#FFFFFF\",\n        \"text_secondary\": \"#86868B\",\n        \"text_subtle\": \"#F5F5F7\",\n        \"success_color\": \"#10B981\",\n        \"warning_color\": \"#F59E0B\",\n        \"error_color\": \"#EF4444\",\n        \"card_background\": \"#0D0D0D\",\n        \"card_border\": \"#E31937\",\n        \"hover_color\": \"#E31937\"\n    },\n    \"layout_principles\": {\n        \"canvas_size\": {\"width\": 1920, \"height\": 1080},\n        \"golden_ratio_application\": true,\n        \"grid_system\": {\"columns\": 12, \"gutter\": 24, \"margin\": 80},\n        \"page_margins\": {\"horizontal\": 80, \"vertical\": 60},\n        \"content_spacing\": {\"module_gap\": 32, \"section_gap\": 48, \"element_gap\": 16},\n        \"visual_hierarchy\": \"清晰的层次结构，通过Bento Grid和超大字体突出营销重点，符合营销推广场景需求\",\n        \"alignment_system\": \"基于潮流风格的动态不对称对齐原则，追求视觉冲击力与平衡\"\n    },\n    \"typography_system\": {\n        \"primary_font\": \"系统UI字体栈 (system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol')\",\n        \"font_sizes\": {\n            \"hero_title\": 120,\n            \"main_title\": 72,\n            \"section_title\": 48,\n            \"content_title\": 32,\n            \"body_text\": 24,\n            \"small_text\": 16,\n            \"accent_number\": 180\n        },\n        \"font_weights\": {\n            \"title\": \"bold\",\n            \"content\": \"normal\",\n            \"emphasis\": \"600\",\n            \"chinese\": \"bold\",\n            \"english\": \"300\"\n        },\n        \"line_heights\": {\n            \"title\": 1.8,\n            \"content\": 2.0,\n            \"dense\": 1.8\n        },\n        \"mixed_typography\": {\n            \"chinese_style\": \"大号粗体，作为主要视觉焦点\",\n            \"english_style\": \"小号细体，作为辅助或点缀信息\",\n            \"number_style\": \"超大号突出，采用高亮色，强化数据冲击力\"\n        },\n        \"readability_optimization\": \"针对潜在客户、市场优化的可读性，确保信息清晰传递，同时保持潮流感\"\n    },\n    \"visual_elements\": {\n        \"scenario_specific_elements\": \"适合营销推广的视觉元素，如增长曲线、趋势箭头、产品示意图、用户价值图标等，以简洁线条和高亮色呈现。\",\n        \"style_characteristics\": \"体现潮流风格的设计特征，包括Bento Grid布局、超大字体、高对比度、特斯拉红色透明度渐变、简洁线条图形。\",\n        \"bento_grid_layout\": \"Apple风格的Bento Grid网格布局，采用不同大小的模块组合，创造动态且有吸引力的信息呈现。\",\n        \"black_red_theme\": \"纯黑色背景(#000000)与特斯拉红色(#E31937)高亮色的现代对比配色方案，营造高端科技感。\",\n        \"oversized_typography\": \"超大字体与数字设计，作为页面的核心视觉焦点，快速吸引注意力并传递关键信息。\",\n        \"decorative_elements\": [\n            \"简洁线条图形元素：用于数据可视化、流程图或抽象装饰，以高亮色勾勒。\",\n            \"特斯拉红色透明度渐变元素：仅使用特斯拉红色自身进行透明度渐变，避免多色渐变，创造科技感和深度。\",\n            \"中英文混排排版元素：中文大号粗体，英文小号细体，形成独特的国际化视觉效果。\",\n            \"符合潮流风格的抽象几何图形或波浪线，以低透明度（0.05-0.15）作为背景纹理或卡片内部装饰。\",\n            \"适合营销推广场景的图标和符号，采用线条或填充样式，颜色与高亮色或主色保持一致。\"\n        ],\n        \"card_style\": {\n            \"background\": \"#0D0D0D\",\n            \"border_radius\": \"24px (大圆角，符合潮流风格)\",\n            \"shadow\": \"现代化的柔和阴影效果（如：0px 8px 24px rgba(0, 0, 0, 0.4)），增强层次感。\",\n            \"border\": \"特斯拉红色细边框 (2px solid #E31937)，部分卡片可采用渐变边框效果。\",\n            \"transparency\": \"主要内容卡片 opacity: 0.90-0.95，次要信息卡片 opacity: 0.85-0.90。\"\n        },\n        \"image_integration\": {\n            \"border_radius\": 0,\n            \"shadow_style\": \"无阴影或极简阴影（如：0px 4px 8px rgba(0, 0, 0, 0.2)）\",\n            \"overlay_style\": \"特斯拉红色半透明遮罩（opacity 0.2-0.4），用于图片背景或强调。\",\n            \"bento_grid_placement\": \"根据网格系统和内容重要性，灵活放置图片，可作为大区块填充或小卡片内容。\"\n        },\n        \"logo_treatment\": {\n            \"positioning\": \"符合营销推广场景的Logo位置，通常在顶部左侧或右侧，或封面页中心突出。\",\n            \"size_guidelines\": \"适合潮流风格的Logo尺寸，确保清晰可见且不喧宾夺主。\",\n            \"integration_style\": \"与黑底红高亮主题和谐的Logo处理，优先使用白色或特斯拉红色版本。\",\n            \"animation_hint\": \"考虑滚动时Logo的微动效或透明度变化，增加交互感。\"\n        }\n    },\n    \"template_consistency\": {\n        \"scenario_coherence\": \"确保所有模板符合营销推广场景特点，以转化、价值和品牌为核心。\",\n        \"style_unity\": \"保持潮流风格的一致性，通过统一的视觉语言和设计元素。\",\n        \"color_harmony\": \"黑底红高亮的现代对比配色体系，贯穿所有页面，确保视觉统一性。\",\n        \"visual_rhythm\": \"协调的视觉节奏和层次，引导用户视线，提升信息吸收效率。\",\n        \"brand_consistency\": \"一致的品牌形象和视觉识别，强化品牌记忆点和专业度。\"\n    }\n}\n```"}}