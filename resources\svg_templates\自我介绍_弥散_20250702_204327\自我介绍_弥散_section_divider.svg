<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变 (弥散风格) -->
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主标题文字渐变 -->
    <linearGradient id="textGradientPrimary" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 强调色渐变，用于装饰元素 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#0EA5E9"/>
      <stop offset="100%" stop-color="#06B6D4"/>
    </linearGradient>

    <!-- 柔和主蓝色渐变，用于弥散背景形状 -->
    <linearGradient id="softPrimaryBlueGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.2"/>
    </linearGradient>

    <!-- 柔和强调青色渐变，用于弥散背景形状 -->
    <linearGradient id="softAccentCyanGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#06B6D4" stop-opacity="0.4"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2"/>
    </linearGradient>

    <!-- 模糊滤镜，用于弥散效果 -->
    <filter id="blurFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="80"/>
    </filter>

    <!-- 文字阴影 -->
    <filter id="textShadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>

  </defs>

  <style>
    /* 全局字体样式 */
    .font-primary {
      font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    }
    .font-secondary {
      font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif;
    }
    .font-accent {
      font-family: "Times New Roman", serif;
    }

    /* 颜色定义 */
    .color-text-primary { fill: #1E293B; }
    .color-text-secondary { fill: #64748B; }
    .color-accent { fill: #06B6D4; }

    /* 字体大小和粗细 */
    .hero-title {
      font-size: 72px;
      font-weight: 700; /* bold */
    }
    .main-title {
      font-size: 56px;
      font-weight: 700; /* bold */
    }
    .section-title {
      font-size: 36px;
      font-weight: 700; /* bold */
    }
    .content-title {
      font-size: 28px;
      font-weight: 600; /* semibold */
    }
    .body-text {
      font-size: 22px;
      font-weight: 400; /* normal */
      line-height: 1.4;
    }
    .small-text {
      font-size: 16px;
      font-weight: 400; /* normal */
    }
    .caption {
      font-size: 14px;
      font-weight: 400; /* normal */
    }

    /* 文本对齐 */
    .text-center {
      text-anchor: middle;
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 弥散背景形状 (柔光效果) -->
  <circle cx="200" cy="250" r="300" fill="url(#softPrimaryBlueGradient)" filter="url(#blurFilter)"/>
  <circle cx="1700" cy="800" r="350" fill="url(#softAccentCyanGradient)" filter="url(#blurFilter)"/>
  <rect x="800" y="100" width="400" height="400" rx="200" ry="200" fill="url(#softPrimaryBlueGradient)" filter="url(#blurFilter)"/>
  <rect x="0" y="600" width="500" height="500" rx="250" ry="250" fill="url(#softAccentCyanGradient)" filter="url(#blurFilter)"/>

  <!-- 主内容区域 - 居中 -->
  <g transform="translate(960 540)">
    <!-- 章节标题 - 突出显示 -->
    <text class="font-primary hero-title color-text-primary text-center" x="0" y="-80" fill="url(#textGradientPrimary)" filter="url(#textShadow)">
      <tspan x="0" y="-80">
        {title}
      </tspan>
      <tspan x="0" y="0" class="font-secondary section-title color-text-secondary">
        {subtitle}
      </tspan>
    </text>

    <!-- 副标题 / 章节描述 -->
    <text class="font-secondary body-text color-text-secondary text-center" x="0" y="100">
      <tspan x="0" y="100">
        {content}
      </tspan>
      <tspan x="0" y="140">
        Personal Journey, Skills and Strengths
      </tspan>
    </text>

    <!-- 装饰元素 / 过渡标记 -->
    <!-- 简洁的勾线图形 / 抽象波浪线 -->
    <path d="M-400 250 C -200 280, 200 280, 400 250" stroke="url(#accentGradient)" stroke-width="3" fill="none" stroke-linecap="round"/>
    <circle cx="-400" cy="250" r="10" fill="#06B6D4"/>
    <circle cx="400" cy="250" r="10" fill="#06B6D4"/>

    <!-- 页面序号 / 进度指示器 -->
    <text class="font-secondary small-text color-text-secondary text-center" x="0" y="300">
      <tspan x="0" y="300">页面 3/10</tspan>
      <tspan x="0" y="325" class="caption">Page 3 of 10</tspan>
    </text>

  </g>

  <!-- 顶部左侧装饰线 -->
  <rect x="80" y="60" width="100" height="5" rx="2.5" fill="#3B82F6" opacity="0.6"/>
  <!-- 底部右侧装饰线 -->
  <rect x="1740" y="1015" width="100" height="5" rx="2.5" fill="#06B6D4" opacity="0.6"/>

</svg>