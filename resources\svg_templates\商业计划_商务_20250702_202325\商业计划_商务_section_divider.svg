<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
  <defs>
    <!-- Color definitions as CSS variables for readability -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --card-border: #BAE6FD;
      }

      /* Font Styles */
      .font-primary {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      }
      .font-secondary {
        font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif;
      }
      .font-accent {
        font-family: 'Times New Roman', serif;
      }

      /* Text Sizes and Weights */
      .hero-title {
        font-size: 72px; /* From font_sizes.hero_title */
        font-weight: 700; /* bold */
        fill: var(--text-primary);
      }
      .main-title {
        font-size: 56px; /* From font_sizes.main_title */
        font-weight: 700; /* bold */
        fill: var(--text-primary);
      }
      .section-title {
        font-size: 36px; /* From font_sizes.section_title */
        font-weight: 700; /* bold */
        fill: var(--text-primary);
      }
      .content-title {
        font-size: 28px; /* From font_sizes.content_title */
        font-weight: 500; /* medium */
        fill: var(--text-primary);
      }
      .body-text {
        font-size: 22px; /* From font_sizes.body_text */
        font-weight: 400; /* normal */
        fill: var(--text-secondary);
      }
      .small-text {
        font-size: 16px; /* From font_sizes.small_text */
        font-weight: 400; /* normal */
        fill: var(--text-secondary);
      }
      .caption-text {
        font-size: 14px; /* From font_sizes.caption */
        font-weight: 400; /* normal */
        fill: var(--text-secondary);
      }

      /* Specific element styles */
      .page-number {
        font-size: 22px; /* body_text size */
        font-weight: 400;
        fill: var(--text-secondary);
      }
      .logo-placeholder {
        font-size: 32px;
        font-weight: 700;
        fill: var(--primary-color);
      }
    </style>

    <!-- Linear Gradients for decorative elements -->
    <!-- Gradient 1: Primary to Secondary Blue/Gray -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <!-- Gradient 2: Accent Blue to Primary Blue -->
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Gradient 3: Semi-transparent Accent Blue for subtle overlay -->
    <linearGradient id="gradientAccentTransparent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.1" />
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="#F8FAFC" />

  <!-- Decorative Elements: Abstract shapes for transition and professionalism -->
  <!-- Large flowing shape 1 (Primary to Secondary Blue/Gray) -->
  <path d="M0 0 H1920 V450 L1400 650 L0 850 Z" fill="url(#gradientPrimary)" opacity="0.8" transform="translate(0, -200) rotate(-5 960 540)" />

  <!-- Large flowing shape 2 (Accent Blue to Primary Blue, slightly transparent) -->
  <path d="M1920 1080 H0 V630 L520 430 L1920 230 Z" fill="url(#gradientAccentTransparent)" opacity="0.6" transform="translate(0, 200) rotate(3 960 540)" />

  <!-- Subtle rectangle elements with accent color -->
  <rect x="150" y="800" width="180" height="12" rx="6" ry="6" fill="#3B82F6" opacity="0.7" />
  <rect x="150" y="830" width="120" height="12" rx="6" ry="6" fill="#3B82F6" opacity="0.5" />

  <rect x="1600" y="150" width="180" height="12" rx="6" ry="6" fill="#3B82F6" opacity="0.7" />
  <rect x="1660" y="180" width="120" height="12" rx="6" ry="6" fill="#3B82F6" opacity="0.5" />

  <!-- Main Content Area -->
  <g class="font-primary">
    <!-- Logo Placeholder (Top Left) -->
    <text x="80" y="100" class="logo-placeholder">
      <tspan>和#123;logo_url和#125;</tspan>
    </text>

    <!-- Chapter Title -->
    <!-- Positioned centrally with a slight offset for visual interest -->
    <text x="960" y="500" text-anchor="middle" class="main-title">
      <tspan x="960" y="500">{title}</tspan>
    </text>

    <!-- Chapter Subtitle/Description -->
    <!-- Ensure sufficient dy for line spacing, min 30px, here 40px -->
    <text x="960" y="580" text-anchor="middle" class="content-title">
      <tspan x="960" y="580">{subtitle}</tspan>
      <tspan x="960" y="620" class="body-text">市场分析 和 财务预测</tspan>
    </text>

    <!-- Page Number (Bottom Right) -->
    <text x="1840" y="1020" text-anchor="end" class="page-number">
      <tspan x="1840" y="1020">{date} 3/10</tspan>
    </text>
  </g>

  <!-- Example of a simple icon/graphic element, outline style, representing data/growth -->
  <!-- Elements are spaced at least 50px apart if not part of a text block -->
  <path d="M400 700 L500 600 L600 700 L700 600 L800 700" stroke="#3B82F6" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round" />
  <circle cx="800" cy="700" r="10" fill="#3B82F6" />

  <!-- Another abstract shape, for visual balance -->
  <path d="M1120 400 C1200 300, 1300 300, 1380 400 S1480 500, 1560 400" stroke="#1E40AF" stroke-width="2" fill="none" opacity="0.5" />

</svg>