<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Color Palette and Typography Definitions -->
    <style type="text/css">
      /* Colors */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-bg-color { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .container-bg-color { fill: #E0F2FE; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title-style { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title-style { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title-style { font-size: 36px; font-weight: 700; line-height: 1.4; } /* bold */
      .content-title-style { font-size: 28px; font-weight: 600; line-height: 1.4; } /* semibold */
      .body-text-style { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text-style { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text-style { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Card Style */
      .card-base-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
      }
      .card-shadow-filter {
        filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06));
      }

      /* Gradients */
      .gradient-primary-fill { fill: url(#primaryGradient); }
      .gradient-accent-fill { fill: url(#accentGradient); }
      .gradient-background-fill { fill: url(#backgroundGradient); }
      .gradient-text-fill { fill: url(#textGradient); }

      /* Icon Styles */
      .icon-stroke-style { stroke: #4A86E8; stroke-width: 2; fill: none; }
      .icon-fill-style { fill: #4A86E8; }

      /* Progress bar */
      .progress-bg-style { fill: #E0F2FE; }
      .progress-fill-style { fill: #3B82F6; }

    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <linearGradient id="textGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Reusable Icons (Outline style with stroke) -->
    <symbol id="icon-check" viewBox="0 0 24 24">
      <path class="icon-stroke-style" stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
    </symbol>
    <symbol id="icon-arrow-right" viewBox="0 0 24 24">
      <path class="icon-stroke-style" stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
    </symbol>
    <!-- Reusable Icon (Filled style for current item) -->
    <symbol id="icon-dot" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="4" class="accent-color" />
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="gradient-background-fill" />

  <!-- Decorative elements - subtle geometric shapes for tech feel -->
  <circle cx="1700" cy="100" r="80" class="primary-color" opacity="0.05" />
  <rect x="1600" y="900" width="200" height="100" rx="20" class="accent-color" opacity="0.05" />
  <path d="M-50 800 C 150 950, 400 900, 500 1080 L 0 1080 L -50 800 Z" class="secondary-color" opacity="0.03" />

  <!-- Header Section -->
  <g id="header-section">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="200" height="60" rx="10" class="container-bg-color" />
    <text x="180" y="98" text-anchor="middle" class="content-title-style text-primary-color font-primary">
      {logo_url}
      <tspan x="180" y="98" class="content-title-style text-primary-color font-primary" text-anchor="middle">企业标志</tspan>
    </text>

    <!-- Page Progress Indicator -->
    <text x="1760" y="90" text-anchor="end" class="section-title-style text-primary-color font-primary">2/10</text>
    <text x="1760" y="125" text-anchor="end" class="small-text-style text-secondary-color font-primary">页面进度</text>

    <!-- Progress Bar -->
    <rect x="1560" y="135" width="200" height="8" rx="4" class="progress-bg-style" />
    <rect x="1560" y="135" width="40" height="8" rx="4" class="progress-fill-style" /> <!-- 20% progress for 2/10 -->
  </g>

  <!-- Main Content Area - Catalog Structure -->
  <g id="main-content-area">
    <!-- Left Section: Catalog Title and Description -->
    <g id="catalog-overview">
      <text x="80" y="300" class="hero-title-style text-primary-color font-primary">
        <tspan x="80" y="300">产品发布会</tspan>
        <tspan x="80" y="380" class="hero-title-style gradient-text-fill">目录概览</tspan>
      </text>

      <text x="80" y="480" class="body-text-style text-secondary-color font-primary">
        <tspan x="80" y="480">
          本目录页旨在提供本次产品发布会的完整内容概览，
        </tspan>
        <tspan x="80" dy="30">
          帮助您快速定位感兴趣的章节和重要信息。
        </tspan>
        <tspan x="80" dy="30">
          请按照导航指引，探索我们的创新产品和未来愿景。
        </tspan>
      </text>

      <!-- Decorative Divider Line -->
      <line x1="80" y1="620" x2="680" y2="620" stroke="#BAE6FD" stroke-width="1" />

      <!-- Date and Author Placeholder -->
      <text x="80" y="660" class="small-text-style text-light-color font-primary">
        <tspan x="80" y="660">发布日期: {date}</tspan>
        <tspan x="80" dy="25">发布者: {author}</tspan>
      </text>
    </g>

    <!-- Right Section: Chapter Navigation with Bento Grid style layout -->
    <g id="chapter-navigation">
      <text x="800" y="240" class="section-title-style text-primary-color font-primary">章节导航</text>

      <!-- Chapter List Items (Modular cards with shadows) -->

      <!-- Chapter 1: Completed -->
      <g class="card-shadow-filter">
        <rect x="800" y="300" width="960" height="80" rx="12" class="card-base-style" />
        <use href="#icon-check" x="830" y="326" width="28" height="28" class="icon-fill-style" />
        <text x="880" y="350" class="content-title-style text-primary-color font-primary">
          <tspan x="880" y="350">01. 欢迎致辞和开场</tspan>
        </text>
        <text x="1710" y="350" text-anchor="end" class="small-text-style text-secondary-color font-primary">已完成</text>
      </g>

      <!-- Chapter 2: Current Chapter (Highlighted) -->
      <g class="card-shadow-filter">
        <rect x="800" y="400" width="960" height="80" rx="12" class="card-base-style" />
        <use href="#icon-dot" x="830" y="426" width="28" height="28" class="accent-color" />
        <text x="880" y="450" class="content-title-style accent-color font-primary">
          <tspan x="880" y="450">02. 目录概览和导航</tspan>
        </text>
        <text x="1710" y="450" text-anchor="end" class="small-text-style accent-color font-primary">当前章节</text>
      </g>

      <!-- Chapter 3: Upcoming -->
      <g class="card-shadow-filter">
        <rect x="800" y="500" width="960" height="80" rx="12" class="card-base-style" />
        <use href="#icon-arrow-right" x="830" y="526" width="28" height="28" class="icon-stroke-style" />
        <text x="880" y="550" class="content-title-style text-primary-color font-primary">
          <tspan x="880" y="550">03. 核心产品发布</tspan>
        </text>
        <text x="1710" y="550" text-anchor="end" class="small-text-style text-secondary-color font-primary">即将开始</text>
      </g>

      <!-- Chapter 4: Upcoming -->
      <g class="card-shadow-filter">
        <rect x="800" y="600" width="960" height="80" rx="12" class="card-base-style" />
        <use href="#icon-arrow-right" x="830" y="626" width="28" height="28" class="icon-stroke-style" />
        <text x="880" y="650" class="content-title-style text-primary-color font-primary">
          <tspan x="880" y="650">04. 技术创新和突破</tspan>
        </text>
        <text x="1710" y="650" text-anchor="end" class="small-text-style text-secondary-color font-primary">待进行</text>
      </g>

      <!-- Chapter 5: Upcoming -->
      <g class="card-shadow-filter">
        <rect x="800" y="700" width="960" height="80" rx="12" class="card-base-style" />
        <use href="#icon-arrow-right" x="830" y="726" width="28" height="28" class="icon-stroke-style" />
        <text x="880" y="750" class="content-title-style text-primary-color font-primary">
          <tspan x="880" y="750">05. 市场策略和展望</tspan>
        </text>
        <text x="1710" y="750" text-anchor="end" class="small-text-style text-secondary-color font-primary">待进行</text>
      </g>

      <!-- More Chapters Placeholder -->
      <text x="1280" y="830" text-anchor="middle" class="small-text-style text-light-color font-primary">更多章节内容...</text>
    </g>
  </g>

  <!-- Footer Section -->
  <g id="footer-section">
    <text x="960" y="1020" text-anchor="middle" class="caption-text-style text-secondary-color font-primary">
      © 2024 {title} 版权所有。保留所有权利。
    </text>
  </g>

</svg>