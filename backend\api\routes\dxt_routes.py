"""
DXT扩展管理API路由
提供dxt文件上传、安装、卸载、列表等功能的REST接口
"""

import os
import tempfile
import logging
from pathlib import Path
from typing import List, Dict, Any
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from backend.core.dxt_manager import DXTManager
from backend.core.storage_manager import storage_manager

# 用户配置数据模型
class UserConfigRequest(BaseModel):
    file_path: str
    user_config: Dict[str, str]
    force_update: bool = False

# {{CHENGQI:
# Action: Added
# Timestamp: 2025-01-17 08:45:00 +08:00
# Reason: 创建dxt扩展管理API路由，支持文件上传、安装、卸载等功能
# Principle_Applied: KISS - 保持API接口简洁明了，单一职责 - 每个端点专注单一功能
# Optimization: 复用现有错误处理模式，采用标准REST API设计
# Architectural_Note (AR): 遵循现有路由文件结构，确保与系统架构一致
# Documentation_Note (DW): 提供完整的API文档注释，便于前端开发
# }}

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/dxt", tags=["DXT扩展管理"])

# Pydantic模型定义
class ExtensionInfo(BaseModel):
    """扩展信息模型"""
    name: str
    version: str
    description: str
    author: str = "未知"
    installed_at: str
    install_path: str
    enabled: bool = True
    file_size: int = 0
    file_hash: str = ""

class ExtensionAction(BaseModel):
    """扩展操作模型"""
    extension_name: str
    enabled: bool = True

class APIResponse(BaseModel):
    """统一API响应模型"""
    success: bool
    message: str
    data: Any = None

# 依赖注入函数
def get_dxt_manager() -> DXTManager:
    """获取DXT管理器实例"""
    try:
        # 获取MCP配置路径
        mcp_config_path = storage_manager.get_config_path("mcp.json")
        
        # 使用应用数据目录下的dxt子目录作为安装目录
        install_base_dir = storage_manager.app_data_dir / "dxt"
        
        return DXTManager(
            config_path=str(mcp_config_path),
            install_base_dir=str(install_base_dir)
        )
        
    except Exception as e:
        logger.error(f"初始化DXT管理器失败: {e}")
        raise HTTPException(status_code=500, detail="DXT管理器初始化失败")

@router.post("/upload", response_model=APIResponse)
async def upload_dxt_file(
    file: UploadFile = File(..., description="DXT扩展文件"),
    force_update: bool = Form(False, description="是否强制更新已存在的扩展"),
    dxt_manager: DXTManager = Depends(get_dxt_manager)
):
    """
    上传并安装DXT扩展文件
    
    Args:
        file: DXT文件（.dxt格式）
        force_update: 是否强制更新已存在的扩展
        
    Returns:
        安装结果和扩展信息
    """
    try:
        # 验证文件扩展名
        if not file.filename or not file.filename.lower().endswith('.dxt'):
            raise HTTPException(status_code=400, detail="只支持.dxt文件格式")
        
        # 验证文件大小
        if file.size and file.size > dxt_manager.MAX_DXT_SIZE:
            max_size_mb = dxt_manager.MAX_DXT_SIZE // 1024 // 1024
            raise HTTPException(status_code=400, detail=f"文件大小超过限制({max_size_mb}MB)")
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.dxt')
        temp_file_path = None
        try:
            # 写入文件内容
            content = await file.read()
            temp_file.write(content)
            temp_file.close()  # 显式关闭文件句柄
            temp_file_path = Path(temp_file.name)

            # 安装扩展
            success, message, extension_info = dxt_manager.install_extension(
                temp_file_path,
                force_update=force_update
            )

            if success:
                logger.info(f"DXT扩展安装成功: {file.filename}")
                return APIResponse(
                    success=True,
                    message=message,
                    data=extension_info
                )
            elif message == "NEED_USER_CONFIG":
                # 需要用户配置，返回临时文件路径和必需变量
                logger.info(f"DXT扩展需要用户配置: {file.filename}")
                extension_info['temp_file_path'] = str(temp_file_path)
                return APIResponse(
                    success=False,
                    message=message,
                    data=extension_info
                )
            else:
                logger.warning(f"DXT扩展安装失败: {message}")
                raise HTTPException(status_code=400, detail=message)

        finally:
            # 清理临时文件
            if temp_file_path and temp_file_path.exists():
                # 使用DXT管理器的安全删除方法
                if not dxt_manager._safe_remove_file(temp_file_path):
                    logger.warning(f"无法删除临时文件: {temp_file_path}")
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传DXT文件时出错: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.get("/list", response_model=APIResponse)
async def list_installed_extensions(
    dxt_manager: DXTManager = Depends(get_dxt_manager)
):
    """
    获取已安装的扩展列表
    
    Returns:
        已安装扩展的详细信息列表
    """
    try:
        extensions = dxt_manager.list_installed_extensions()
        
        logger.info(f"获取扩展列表成功，共{len(extensions)}个扩展")
        return APIResponse(
            success=True,
            message=f"获取到{len(extensions)}个已安装扩展",
            data=extensions
        )
        
    except Exception as e:
        logger.error(f"获取扩展列表时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取扩展列表失败: {str(e)}")

@router.delete("/uninstall/{extension_name}", response_model=APIResponse)
async def uninstall_extension(
    extension_name: str,
    dxt_manager: DXTManager = Depends(get_dxt_manager)
):
    """
    卸载指定的扩展
    
    Args:
        extension_name: 扩展名称
        
    Returns:
        卸载结果
    """
    try:
        success, message = dxt_manager.uninstall_extension(extension_name)
        
        if success:
            logger.info(f"扩展卸载成功: {extension_name}")
            return APIResponse(
                success=True,
                message=message
            )
        else:
            logger.warning(f"扩展卸载失败: {message}")
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"卸载扩展时出错: {e}")
        raise HTTPException(status_code=500, detail=f"卸载失败: {str(e)}")

@router.put("/toggle", response_model=APIResponse)
async def toggle_extension(
    action: ExtensionAction,
    dxt_manager: DXTManager = Depends(get_dxt_manager)
):
    """
    启用或禁用扩展
    
    Args:
        action: 扩展操作信息（名称和启用状态）
        
    Returns:
        操作结果
    """
    try:
        success, message = dxt_manager.enable_extension(
            action.extension_name, 
            action.enabled
        )
        
        if success:
            status = "启用" if action.enabled else "禁用"
            logger.info(f"扩展{status}成功: {action.extension_name}")
            return APIResponse(
                success=True,
                message=message
            )
        else:
            logger.warning(f"扩展状态切换失败: {message}")
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换扩展状态时出错: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")

@router.get("/info/{extension_name}", response_model=APIResponse)
async def get_extension_info(
    extension_name: str,
    dxt_manager: DXTManager = Depends(get_dxt_manager)
):
    """
    获取指定扩展的详细信息
    
    Args:
        extension_name: 扩展名称
        
    Returns:
        扩展详细信息
    """
    try:
        extensions = dxt_manager.list_installed_extensions()
        extension_info = next(
            (ext for ext in extensions if ext['name'] == extension_name), 
            None
        )
        
        if extension_info:
            logger.info(f"获取扩展信息成功: {extension_name}")
            return APIResponse(
                success=True,
                message="获取扩展信息成功",
                data=extension_info
            )
        else:
            raise HTTPException(status_code=404, detail=f"扩展 {extension_name} 不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取扩展信息时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取信息失败: {str(e)}")

@router.post("/validate", response_model=APIResponse)
async def validate_dxt_file(
    file: UploadFile = File(..., description="待验证的DXT文件"),
    dxt_manager: DXTManager = Depends(get_dxt_manager)
):
    """
    验证DXT文件的有效性（不安装）
    
    Args:
        file: DXT文件
        
    Returns:
        验证结果和扩展信息
    """
    try:
        # 验证文件扩展名
        if not file.filename or not file.filename.lower().endswith('.dxt'):
            raise HTTPException(status_code=400, detail="只支持.dxt文件格式")
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.dxt')
        temp_file_path = None
        try:
            # 写入文件内容
            content = await file.read()
            temp_file.write(content)
            temp_file.close()  # 显式关闭文件句柄
            temp_file_path = Path(temp_file.name)

            # 验证文件
            is_valid, error_msg = dxt_manager.validate_dxt_file(temp_file_path)

            if is_valid:
                # 提取manifest信息
                manifest = dxt_manager.extract_manifest(temp_file_path)
                logger.info(f"DXT文件验证成功: {file.filename}")
                return APIResponse(
                    success=True,
                    message="文件验证通过",
                    data=manifest
                )
            else:
                logger.warning(f"DXT文件验证失败: {error_msg}")
                raise HTTPException(status_code=400, detail=error_msg)

        finally:
            # 清理临时文件
            if temp_file_path and temp_file_path.exists():
                # 使用DXT管理器的安全删除方法
                if not dxt_manager._safe_remove_file(temp_file_path):
                    logger.warning(f"无法删除临时文件: {temp_file_path}")
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证DXT文件时出错: {e}")
        raise HTTPException(status_code=500, detail=f"验证失败: {str(e)}")

# 健康检查端点
@router.get("/health", response_model=APIResponse)
async def health_check():
    """DXT管理服务健康检查"""
    try:
        return APIResponse(
            success=True,
            message="DXT管理服务运行正常"
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="服务异常")


@router.get("/check/{extension_name}")
async def check_extension_exists(extension_name: str):
    """
    检查扩展是否已存在
    """
    try:
        mcp_config_path = storage_manager.get_config_path("mcp.json")
        install_base_dir = storage_manager.app_data_dir / "dxt"
        dxt_manager = DXTManager(
            config_path=str(mcp_config_path),
            install_base_dir=str(install_base_dir)
        )

        exists = dxt_manager.check_extension_exists(extension_name)

        return {
            "success": True,
            "data": {
                "exists": exists,
                "extension_name": extension_name
            }
        }

    except Exception as e:
        logger.error(f"检查扩展是否存在时出错: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.delete("/uninstall-from-mcp/{mcp_server_name}")
async def uninstall_extension_from_mcp(mcp_server_name: str):
    """
    通过MCP服务器名称卸载DXT扩展
    """
    try:
        mcp_config_path = storage_manager.get_config_path("mcp.json")
        install_base_dir = storage_manager.app_data_dir / "dxt"

        dxt_manager = DXTManager(
            config_path=str(mcp_config_path),
            install_base_dir=str(install_base_dir)
        )

        success, message = dxt_manager.uninstall_extension_by_mcp_name(mcp_server_name)

        if success:
            logger.info(f"通过MCP名称卸载DXT扩展成功: {mcp_server_name}")
            return {
                "success": True,
                "message": message
            }
        else:
            logger.warning(f"通过MCP名称卸载DXT扩展失败: {message}")
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"通过MCP名称卸载DXT扩展时出错: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/install-with-config")
async def install_extension_with_config(request: UserConfigRequest):
    """
    使用用户配置安装DXT扩展
    """
    try:
        mcp_config_path = storage_manager.get_config_path("mcp.json")
        install_base_dir = storage_manager.app_data_dir / "dxt"

        dxt_manager = DXTManager(
            config_path=str(mcp_config_path),
            install_base_dir=str(install_base_dir)
        )

        # 使用用户配置安装扩展
        success, message, extension_info = dxt_manager.install_extension(
            Path(request.file_path),
            force_update=request.force_update,
            user_config=request.user_config
        )

        if success:
            logger.info(f"DXT扩展安装成功（带用户配置）: {extension_info['name']}")
            return {
                "success": True,
                "message": message,
                "data": extension_info
            }
        else:
            logger.warning(f"DXT扩展安装失败（带用户配置）: {message}")
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"安装DXT扩展（带用户配置）时出错: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")