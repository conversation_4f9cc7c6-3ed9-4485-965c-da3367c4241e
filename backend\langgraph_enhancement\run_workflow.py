# -*- coding: utf-8 -*-
"""
LangGraph 增强系统 - 工作流执行器

用于启动和测试一个完整的多智能体协作工作流。
"""

import asyncio
import logging
from pprint import pprint

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)


async def run_workflow():
    """启动一个 LangGraph 工作流"""
    
    print("\n" + "="*60)
    print("🚀 准备启动 LangGraph 多智能体工作流...")
    print("="*60 + "\n")
    
    try:
        # 1. 获取工作流引擎
        from backend.langgraph_enhancement.core.workflow_engine import DynamicWorkflowEngine
        workflow_engine = DynamicWorkflowEngine()
        logger.info("✅ 工作流引擎获取成功")
        
        # 2. 定义要使用的团队和初始输入
        # team_id 对应于 config/teams.yaml 中定义的团队
        team_id_to_run = "market_analysis_team" 
        
        # 初始状态，至少需要一个 messages 列表
        initial_state = {
            "messages": [
                {
                    "role": "user", 
                    "content": "请帮我分析一下自动驾驶汽车行业的最新市场趋势，并总结成一份简报。"
                }
            ],
            "core_context": {
                "user_id": "test_user_123",
                "request_source": "programmatic_run"
            },
            "working_memory": {}
        }
        
        print(f"🔧 正在使用团队 '{team_id_to_run}'...")
        print("📝 初始输入:")
        pprint(initial_state)
        
        # 3. 执行工作流
        # 可选的配置，例如设置递归限制
        execution_config = {"recursion_limit": 5}
        
        logger.info("⏳ 开始执行工作流...")
        result_state = await workflow_engine.execute_workflow(
            team_name=team_id_to_run,
            input_data=initial_state,
            config=execution_config
        )
        logger.info("✅ 工作流执行成功")
        
        print("\n" + "="*60)
        print("🎉 工作流执行完成!")
        print("="*60)
        
        # 打印最终结果
        final_state = result_state.get("final_state", {})
        if final_state:
            print("\n📊 最终状态:")
            pprint(final_state)
            
            final_message = final_state.get("messages", [])[-1]
            print("\n💬 最终智能体回复内容:")
            pprint(final_message.get("content"))
        else:
            print("\n❌ 工作流未返回最终状态。")
            pprint(result_state)
        
    except ImportError as e:
        logger.error(f"❌ 导入模块失败: {e}")
        print("\n错误：无法导入所需模块。请确保在项目根目录下运行此脚本。")
        print("命令示例: python -m backend.langgraph_enhancement.run_workflow")
    except Exception as e:
        logger.error(f"❌ 工作流执行过程中发生错误: {e}")
        print(f"\n错误：{e}")


if __name__ == "__main__":
    # 提示：为了确保所有模块能够正确导入，请从项目根目录运行此脚本
    # 正确的运行方式: python -m backend.langgraph_enhancement.run_workflow
    asyncio.run(run_workflow()) 