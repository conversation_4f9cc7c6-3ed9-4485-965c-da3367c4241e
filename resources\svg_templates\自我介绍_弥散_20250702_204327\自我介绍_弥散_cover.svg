<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 背景渐变 - 弥散风格 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- 主要装饰元素渐变 -->
    <linearGradient id="primaryDecorativeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- 强调色装饰元素渐变 -->
    <linearGradient id="accentDecorativeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#06B6D4" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>

    <!-- 柔和的径向渐变1，用于弥散效果 -->
    <radialGradient id="softRadial1" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0" />
    </radialGradient>

    <!-- 柔和的径向渐变2，用于弥散效果 -->
    <radialGradient id="softRadial2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#06B6D4" stop-opacity="0.25" />
      <stop offset="100%" stop-color="#06B6D4" stop-opacity="0" />
    </radialGradient>
  </defs>

  <style>
    /* 字体定义 - 确保兼容性，使用系统本地字体 */
    @font-face {
      font-family: 'Microsoft YaHei';
      src: local('Microsoft YaHei'), local('MSYH');
      font-weight: normal;
    }
    @font-face {
      font-family: 'Microsoft YaHei';
      src: local('Microsoft YaHei Bold'), local('MSYHB');
      font-weight: bold;
    }
    @font-face {
      font-family: 'Segoe UI';
      src: local('Segoe UI');
      font-weight: normal;
    }
    @font-face {
      font-family: 'Source Han Sans CN';
      src: local('Source Han Sans CN'), local('Noto Sans CJK SC');
      font-weight: normal;
    }

    /* 通用样式 */
    .background-rect {
      fill: url(#backgroundGradient);
    }

    .main-title {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 72px; /* hero_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      text-anchor: middle; /* 居中对齐 */
    }

    .sub-title {
      font-family: 'Source Han Sans CN', 'Segoe UI', sans-serif;
      font-size: 36px; /* section_title */
      font-weight: 400; /* normal */
      fill: #64748B; /* text_secondary */
      text-anchor: middle; /* 居中对齐 */
    }

    .decorative-shape {
      opacity: 0.15; /* 柔和、弥散的效果 */
    }

    .small-text {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: 16px; /* small_text */
      fill: #94A3B8; /* text_light */
      text-anchor: middle;
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="background-rect" />

  <!-- 弥散装饰元素 (柔和、流动的形状) -->
  <circle cx="1500" cy="200" r="250" fill="url(#softRadial1)" class="decorative-shape" />
  <ellipse cx="400" cy="900" rx="300" ry="150" fill="url(#softRadial2)" class="decorative-shape" />
  <!-- 旋转的圆形矩形，形成柔和的几何感 -->
  <rect x="1600" y="800" width="400" height="400" rx="200" ry="200" fill="url(#primaryDecorativeGradient)" class="decorative-shape" transform="rotate(45 1800 1000)" />
  <!-- 波浪形底部装饰，增加流动感 -->
  <path d="M0 600 Q 480 500, 960 600 T 1920 600 L 1920 1080 L 0 1080 Z" fill="url(#accentDecorativeGradient)" opacity="0.08" />


  <!-- Logo - 放置在左上角，留出页边距 -->
  <!-- JIMU品牌Logo -->
  <g transform="translate(80, 60)">
    <!-- Logo背景 -->
    <rect x="0" y="0" width="180" height="60" rx="8" ry="8" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="1"/>
    <!-- Logo图形：两个重叠圆形代表"积木"概念 -->
    <g transform="translate(10, 15)">
      <circle cx="15" cy="15" r="12" fill="#1E40AF" opacity="0.8"/>
      <circle cx="25" cy="15" r="12" fill="#3B82F6" opacity="0.9"/>
    </g>
    <!-- Logo文字 -->
    <text x="55" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#1E40AF">JIMU</text>
    <text x="55" y="40" font-family="Arial, sans-serif" font-size="10" font-weight="400" fill="#64748B">智能演示</text>
  </g>

  <!-- 主要内容区域 - 居中显示 -->
  <!-- 计算中心点: 1920 / 2 = 960, 1080 / 2 = 540 -->
  <!-- 调整Y轴位置，确保标题和副标题堆叠且不重叠 -->
  <g>
    <!-- 主标题 - 中文大字体粗体 -->
    <text x="960" y="470" class="main-title">
      <tspan x="960" y="470">{title}</tspan>
    </text>

    <!-- 副标题 - 英文小字作为点缀 -->
    <!-- dy值确保行间距足够，避免重叠 (主标题字体72px, 副标题36px，间距80px足够) -->
    <text x="960" y="550" class="sub-title">
      <tspan x="960" y="550">{subtitle}</tspan>
    </text>
  </g>

  <!-- 装饰性分割线和点缀元素 (柔和且弥散) -->
  <line x1="800" y1="700" x2="1120" y2="700" stroke="#BAE6FD" stroke-width="2" stroke-opacity="0.5" stroke-linecap="round" />
  <circle cx="960" cy="700" r="8" fill="#BAE6FD" opacity="0.6" />

  <!-- 页面底部信息 (日期和作者) -->
  <text x="960" y="980" class="small-text">
    <tspan x="960" y="980">更新日期: {date} | 作者: {author}</tspan>
  </text>

</svg>