<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变：从背景色到容器背景色 -->
    <linearGradient id="bgGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 强调色渐变：用于装饰性元素，由强调色自身透明度变化 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" spreadMethod="pad" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
    </linearGradient>
    
    <!-- 主色渐变：用于微妙的装饰性元素，由主色自身透明度变化 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" spreadMethod="pad" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0"/>
    </linearGradient>

    <!-- 引号符号渐变：强调色和主色的混合 -->
    <linearGradient id="quoteMarkGradient" x1="0" y1="0" x2="1" y2="0" spreadMethod="pad" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 用于卡片或突出元素的阴影滤镜 -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="6"/>
      <feOffset dx="0" dy="4"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.1"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <style>
    /* 全局字体设置，兼容中英文 */
    svg {
      font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
      overflow: hidden; /* 确保内容不超出视图框 */
    }

    /* 颜色定义 */
    .bg-color { fill: url(#bgGradient); }
    .primary-color { fill: #1E40AF; }
    .secondary-color { fill: #475569; }
    .accent-color { fill: #3B82F6; }
    .text-primary-color { fill: #1E293B; }
    .text-secondary-color { fill: #64748B; }
    .text-light-color { fill: #94A3B8; }
    .card-background-color { fill: #FFFFFF; }
    .card-border-color { stroke: #BAE6FD; }

    /* 字体大小和字重 */
    .font-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
    .font-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
    .font-section-title { font-size: 36px; font-weight: 700; line-height: 1.4; } /* bold */
    .font-content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* semibold */
    .font-body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
    .font-small-text { font-size: 16px; font-weight: 400; line-height: 1.4; } /* normal */
    .font-caption { font-size: 14px; font-weight: 400; line-height: 1.4; } /* normal */

    /* 引用页特定样式 */
    .quote-mark {
      font-size: 300px; /* 超大尺寸，强烈视觉冲击 */
      font-weight: 900; /* 最粗字重 */
      fill: url(#quoteMarkGradient);
      opacity: 0.2; /* 作为背景装饰，半透明 */
    }

    .quote-text {
      font-size: 56px; /* 引用内容字体大小，使用main_title */
      font-weight: 700; /* 粗体 */
      fill: #1E293B; /* 使用text_primary颜色 */
      text-anchor: middle; /* 文本居中对齐 */
    }

    .quote-author {
      font-size: 28px; /* 作者/来源字体大小，使用content_title */
      font-weight: 600; /* 半粗体 */
      fill: #64748B; /* 使用text_secondary颜色 */
      text-anchor: middle; /* 文本居中对齐 */
    }

    /* 装饰性元素样式 */
    .decorative-shape {
      fill: #3B82F6; /* 强调色 */
      opacity: 0.1; /* 增加透明度，使其更具背景感 */
    }
    .subtle-shape {
      fill: #1E40AF; /* 主色 */
      opacity: 0.05; /* 更加透明，作为微妙的背景点缀 */
    }

    /* 卡片样式（如果需要，此处为参考） */
    .card {
      fill: #FFFFFF; /* card_background */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1px;
      rx: 12px; /* border_radius */
      filter: url(#drop-shadow); /* 应用阴影滤镜 */
    }
  </style>

  <!-- 背景层：使用渐变背景色 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- 装饰性背景图形：几何图形，模仿Bento Grid的结构感和科技感 -->
  <!-- 左上角微妙形状 -->
  <rect x="-100" y="-100" width="400" height="400" rx="40" class="subtle-shape" transform="rotate(20 -100 -100)"/>
  <!-- 右下角微妙圆形 -->
  <circle cx="1900" cy="1000" r="300" class="decorative-shape"/>
  <!-- 左侧中部渐变线条 -->
  <rect x="0" y="400" width="200" height="10" rx="5" fill="url(#primaryGradient)" opacity="0.3"/>
  <!-- 右侧中部渐变线条 -->
  <rect x="1720" y="700" width="200" height="10" rx="5" fill="url(#accentGradient)" opacity="0.3"/>


  <!-- 主内容区域 - 居中对齐 -->
  <!-- 整个引用区域通过g标签平移到画布中心 (960, 540) -->
  <g transform="translate(960 540)">
    <!-- 大型装饰性开引号 -->
    <!-- x, y 坐标相对于 g 标签的原点 (960, 540) -->
    <text x="-400" y="-150" class="quote-mark" text-anchor="start">
      “
    </text>

    <!-- 引用内容 -->
    <!-- 中文大字体粗体，英文小字作为点缀 -->
    <!-- 使用tspan和dy属性控制多行文本的行距，确保间距充足，避免重叠 -->
    <text x="0" y="0" class="quote-text">
      <tspan x="0" dy="-80">在持续创新和追求卓越中</tspan>
      <tspan x="0" dy="80">我们不断超越自我和实现价值</tspan>
      <tspan x="0" dy="80">Innovation and Excellence Drive Us Forward</tspan>
    </text>

    <!-- 大型装饰性闭引号 -->
    <text x="400" y="150" class="quote-mark" text-anchor="end">
      ”
    </text>

    <!-- 作者 / 来源信息 -->
    <!-- 确保与引用内容有足够间距 -->
    <text x="0" y="250" class="quote-author">
      <tspan x="0" dy="0">— {author}</tspan>
      <tspan x="0" dy="40">企业发展研究中心</tspan>
    </text>
  </g>

  <!-- 页面信息 (右下角) -->
  <!-- x, y 坐标直接在画布上定位 -->
  <text x="1840" y="1020" class="font-caption text-light-color" text-anchor="end">
    9/10
  </text>

  <!-- Logo 占位符 (左上角) -->
  <!-- 通过g标签平移到指定位置，内部元素坐标相对g标签原点 -->
  <g transform="translate(80 60)">
    <rect x="0" y="0" width="150" height="50" rx="8" fill="#1E40AF" opacity="0.8" filter="url(#drop-shadow)"/>
    <text x="75" y="30" class="font-small-text" fill="#FFFFFF" text-anchor="middle">
      {logo_url}
    </text>
  </g>

</svg>