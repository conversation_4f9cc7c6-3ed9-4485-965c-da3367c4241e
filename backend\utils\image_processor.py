"""
图片处理工具模块
统一处理SVG生成中的图片验证、下载、转换等功能
"""
import os
import re
import base64
import time
import urllib.request
import urllib.parse
import aiohttp
import logging
from pathlib import Path
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

# 图片下载和转换设置
MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB
DOWNLOAD_TIMEOUT = 10  # 10秒
VALIDATION_TIMEOUT = 5  # 验证超时5秒
# 缓存目录
CACHE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "cache", "images")

# 确保缓存目录存在
os.makedirs(CACHE_DIR, exist_ok=True)

class ImageProcessor:
    """图片处理器类"""

    def __init__(self):
        self.cache_dir = CACHE_DIR
        # 图片验证缓存：URL -> 验证结果
        self._validation_cache = {}
        # 验证缓存的时间戳，用于过期检查
        self._validation_cache_time = {}
        
    async def validate_and_filter_images(self, images: List[Dict[str, str]], use_cache: bool = True) -> List[Dict[str, str]]:
        """验证并过滤有效的图片

        Args:
            images: 原始图片列表，每个图片包含url和alt字段
            use_cache: 是否使用验证缓存，默认True

        Returns:
            过滤后的有效图片列表
        """
        if not images:
            return []

        valid_images = []
        cache_hits = 0
        new_validations = 0

        for i, img in enumerate(images):
            img_url = img.get('url', '')

            if not img_url:
                logger.warning(f"图片{i+1}缺少URL，跳过")
                continue

            try:
                # 检查缓存
                is_valid = None
                if use_cache and img_url in self._validation_cache:
                    # 检查缓存是否过期（1小时过期）
                    cache_time = self._validation_cache_time.get(img_url, 0)
                    current_time = time.time()
                    if current_time - cache_time < 3600:  # 1小时内有效
                        is_valid = self._validation_cache[img_url]
                        cache_hits += 1
                        logger.debug(f"🔄 图片{i+1}使用缓存结果: {img_url[:50]}...")

                # 如果没有缓存或缓存过期，进行验证
                if is_valid is None:
                    if img_url.startswith(("http://", "https://")):
                        # 验证网络图片
                        is_valid = await self._validate_remote_image(img_url)
                    else:
                        # 验证本地图片
                        is_valid = self._validate_local_image(img_url)

                    # 更新缓存
                    if use_cache:
                        self._validation_cache[img_url] = is_valid
                        self._validation_cache_time[img_url] = time.time()

                    new_validations += 1

                # 处理验证结果
                if is_valid:
                    valid_images.append(img)
                    if new_validations > 0:  # 只在新验证时记录日志
                        logger.info(f"✅ 图片{i+1}验证通过: {img_url[:100]}...")
                else:
                    if new_validations > 0:  # 只在新验证时记录日志
                        logger.warning(f"❌ 图片{i+1}验证失败，已过滤: {img_url[:100]}...")

            except Exception as e:
                logger.error(f"❌ 图片{i+1}验证异常，已过滤: {img_url[:100]}..., 错误: {e}")
                # 缓存失败结果
                if use_cache:
                    self._validation_cache[img_url] = False
                    self._validation_cache_time[img_url] = time.time()
                continue

        if cache_hits > 0:
            logger.info(f"📊 图片验证完成: {len(valid_images)}/{len(images)} 张图片有效 (缓存命中: {cache_hits}, 新验证: {new_validations})")
        else:
            logger.info(f"📊 图片验证完成: {len(valid_images)}/{len(images)} 张图片有效")

        return valid_images

    async def _validate_remote_image(self, url: str) -> bool:
        """验证远程图片是否有效"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            timeout = aiohttp.ClientTimeout(total=VALIDATION_TIMEOUT)
            
            async with aiohttp.ClientSession() as session:
                async with session.head(url, headers=headers, timeout=timeout) as response:
                    # 检查状态码
                    if response.status != 200:
                        logger.debug(f"图片URL状态码异常: {url}, 状态码: {response.status}")
                        return False
                    
                    # 检查内容类型
                    content_type = response.headers.get('Content-Type', '').lower()
                    if not content_type.startswith('image/'):
                        logger.debug(f"URL不是图片: {url}, 内容类型: {content_type}")
                        return False
                    
                    # 检查内容长度（如果有的话）
                    content_length = response.headers.get('Content-Length')
                    if content_length:
                        try:
                            size = int(content_length)
                            if size > MAX_IMAGE_SIZE:
                                logger.debug(f"图片太大: {url}, 大小: {size} 字节")
                                return False
                        except ValueError:
                            pass  # 忽略无法解析的Content-Length
                    
                    return True
                    
        except Exception as e:
            logger.debug(f"验证远程图片失败: {url}, 错误: {e}")
            return False

    def _validate_local_image(self, path: str) -> bool:
        """验证本地图片是否有效"""
        try:
            # 检查文件是否存在
            if not os.path.exists(path):
                # 尝试相对路径
                if not os.path.isabs(path):
                    # 尝试在当前工作目录查找
                    current_dir_path = os.path.join(os.getcwd(), path)
                    if os.path.exists(current_dir_path):
                        path = current_dir_path
                    else:
                        logger.debug(f"本地图片文件不存在: {path}")
                        return False
                else:
                    logger.debug(f"本地图片文件不存在: {path}")
                    return False
            
            # 检查文件扩展名
            valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.bmp'}
            file_ext = Path(path).suffix.lower()
            if file_ext not in valid_extensions:
                logger.debug(f"本地文件不是支持的图片格式: {path}, 扩展名: {file_ext}")
                return False
            
            # 检查文件大小
            file_size = os.path.getsize(path)
            if file_size > MAX_IMAGE_SIZE:
                logger.debug(f"本地图片太大: {path}, 大小: {file_size} 字节")
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"验证本地图片失败: {path}, 错误: {e}")
            return False

    def download_and_convert_to_base64(self, url: str) -> str:
        """同步下载网络图片并转换为Base64数据URI"""
        # 检查缓存
        cache_key = base64.b64encode(url.encode()).decode().replace('/', '_')
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.txt")
        
        # 如果缓存存在，直接返回
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    return f.read()
            except:
                pass  # 如果读取缓存失败，继续下载
        
        try:
            # 设置请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 创建请求对象
            req = urllib.request.Request(url, headers=headers)
            
            # 打开URL
            with urllib.request.urlopen(req, timeout=DOWNLOAD_TIMEOUT) as response:
                # 检查内容类型
                content_type = response.info().get_content_type()
                if not content_type.startswith('image/'):
                    logger.warning(f"URL不是图片: {url}, 内容类型: {content_type}")
                    return ""
                
                # 检查内容长度
                content_length = response.info().get('Content-Length')
                if content_length and int(content_length) > MAX_IMAGE_SIZE:
                    logger.warning(f"图片太大: {url}, 大小: {content_length} 字节")
                    return ""
                
                # 读取图片数据
                img_data = response.read()
                
                # 检查实际大小
                if len(img_data) > MAX_IMAGE_SIZE:
                    logger.warning(f"图片太大: {url}, 实际大小: {len(img_data)} 字节")
                    return ""
                
                # 转换为Base64数据URI
                base64_data = base64.b64encode(img_data).decode("utf-8")
                data_uri = f"data:{content_type};base64,{base64_data}"
                
                # 缓存结果
                try:
                    with open(cache_file, 'w') as f:
                        f.write(data_uri)
                except:
                    pass  # 缓存失败不影响返回结果
                
                return data_uri
                
        except Exception as e:
            logger.error(f"下载图片失败: {url}, 错误: {e}")
            return ""

    async def download_and_convert_to_base64_async(self, url: str) -> str:
        """异步下载网络图片并转换为Base64数据URI"""
        # 检查缓存
        cache_key = base64.b64encode(url.encode()).decode().replace('/', '_')
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.txt")
        
        # 如果缓存存在，直接返回
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    return f.read()
            except:
                pass  # 如果读取缓存失败，继续下载
        
        try:
            # 设置请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 使用aiohttp异步下载
            async with aiohttp.ClientSession() as session:
                timeout = aiohttp.ClientTimeout(total=DOWNLOAD_TIMEOUT)
                async with session.get(url, headers=headers, timeout=timeout) as response:
                    # 检查状态码
                    if response.status != 200:
                        logger.warning(f"下载图片失败: {url}, 状态码: {response.status}")
                        return ""
                    
                    # 检查内容类型
                    content_type = response.headers.get('Content-Type', '')
                    if not content_type.startswith('image/'):
                        logger.warning(f"URL不是图片: {url}, 内容类型: {content_type}")
                        return ""
                    
                    # 检查内容长度
                    content_length = response.headers.get('Content-Length')
                    if content_length and int(content_length) > MAX_IMAGE_SIZE:
                        logger.warning(f"图片太大: {url}, 大小: {content_length} 字节")
                        return ""
                    
                    # 读取图片数据
                    img_data = await response.read()
                    
                    # 检查实际大小
                    if len(img_data) > MAX_IMAGE_SIZE:
                        logger.warning(f"图片太大: {url}, 实际大小: {len(img_data)} 字节")
                        return ""
                    
                    # 转换为Base64数据URI
                    base64_data = base64.b64encode(img_data).decode("utf-8")
                    data_uri = f"data:{content_type};base64,{base64_data}"
                    
                    # 缓存结果
                    try:
                        with open(cache_file, 'w') as f:
                            f.write(data_uri)
                    except:
                        pass  # 缓存失败不影响返回结果
                    
                    return data_uri
                    
        except Exception as e:
            logger.error(f"异步下载图片失败: {url}, 错误: {e}")
            return ""

    def convert_local_image_to_base64(self, image_path: str) -> str:
        """将本地图片转换为Base64数据URI"""
        try:
            # 尝试读取本地文件
            path = Path(image_path)
            if not path.exists():
                # 尝试在当前工作目录查找
                cwd_path = Path.cwd() / image_path
                if cwd_path.exists():
                    path = cwd_path
                else:
                    # 尝试在资源目录查找
                    resource_path = Path(__file__).parent.parent.parent / "resources" / path.name
                    if resource_path.exists():
                        path = resource_path
                    else:
                        # 尝试在多个可能的位置查找
                        possible_paths = [
                            Path.cwd() / "resources" / path.name,
                            Path.cwd().parent / "resources" / path.name,
                            Path(__file__).parent.parent / "resources" / path.name,
                            Path(os.path.abspath(os.path.dirname(os.path.dirname(__file__)))) / "resources" / path.name
                        ]

                        found = False
                        for p in possible_paths:
                            if p.exists():
                                path = p
                                found = True
                                logger.info(f"找到图片: {p}")
                                break

                        if not found:
                            logger.warning(f"本地图片不存在: {image_path}")
                            return ""

            # 读取文件并转换为Base64
            with open(path, "rb") as img_file:
                img_data = img_file.read()

            # 根据文件扩展名确定MIME类型
            mime_types = {
                ".png": "image/png",
                ".jpg": "image/jpeg",
                ".jpeg": "image/jpeg",
                ".gif": "image/gif",
                ".svg": "image/svg+xml",
                ".webp": "image/webp",
                ".bmp": "image/bmp"
            }
            ext = path.suffix.lower()
            mime_type = mime_types.get(ext, "image/png")

            # 转换为Base64数据URI
            base64_data = base64.b64encode(img_data).decode("utf-8")
            data_uri = f"data:{mime_type};base64,{base64_data}"

            return data_uri

        except Exception as e:
            logger.error(f"处理本地图片失败: {image_path}, 错误: {e}")
            return ""

    def process_svg_images(self, svg_content: str) -> str:
        """处理SVG中的图片链接

        处理SVG中的<image>元素，支持：
        1. 外部URL下载转换为Base64
        2. 本地图片路径转换为Base64数据URI
        3. 背景图片处理

        Args:
            svg_content: 原始SVG内容

        Returns:
            处理后的SVG内容
        """
        # 处理背景图片
        svg_content = self._process_background_images(svg_content)

        # 查找所有<image>元素
        image_pattern = r'<image[^>]*href="([^"]+)"[^>]*/?>'

        def replace_image(match):
            full_tag = match.group(0)
            image_url = match.group(1)

            # 如果已经是数据URI，直接返回
            if image_url.startswith("data:"):
                return full_tag

            # 处理外部URL
            if image_url.startswith(("http://", "https://")):
                try:
                    # 下载并转换为Base64
                    data_uri = self.download_and_convert_to_base64(image_url)
                    if data_uri:
                        return full_tag.replace(f'href="{image_url}"', f'href="{data_uri}"')
                    else:
                        # 如果下载失败，移除该图片元素
                        logger.warning(f"图片下载失败，已移除: {image_url}")
                        return ""  # 返回空字符串，移除该图片元素
                except Exception as e:
                    logger.error(f"下载并转换图片失败: {image_url}, 错误: {e}")
                    # 如果下载失败，移除该图片元素
                    logger.warning(f"图片处理异常，已移除: {image_url}")
                    return ""  # 返回空字符串，移除该图片元素

            # 处理本地图片路径
            try:
                data_uri = self.convert_local_image_to_base64(image_url)
                if data_uri:
                    # 替换href属性
                    return full_tag.replace(f'href="{image_url}"', f'href="{data_uri}"')
                else:
                    logger.warning(f"本地图片不存在，已移除: {image_url}")
                    return ""  # 如果找不到文件，移除该图片元素

            except Exception as e:
                logger.error(f"处理本地图片失败: {image_url}, 错误: {e}")
                logger.warning(f"本地图片处理异常，已移除: {image_url}")
                return ""  # 出错时移除该图片元素

        # 替换所有图片链接
        return re.sub(image_pattern, replace_image, svg_content)

    def _process_background_images(self, svg_content: str) -> str:
        """处理SVG中的背景图片

        处理使用背景图片的情况，包括:
        1. style="background-image: url(...)"
        2. fill="url(#pattern1)" 与 <pattern><image href="..."></pattern>

        Args:
            svg_content: 原始SVG内容

        Returns:
            处理后的SVG内容
        """
        # 处理style中的背景图片
        bg_style_pattern = r'style="([^"]*background-image:\s*url\(([^)]+)\)[^"]*)"'

        def replace_bg_style(match):
            full_style = match.group(1)
            image_url = match.group(2).strip('\'"')

            # 如果已经是数据URI，直接返回
            if image_url.startswith("data:"):
                return f'style="{full_style}"'

            try:
                # 获取数据URI（本地或远程）
                data_uri = ""
                if image_url.startswith(("http://", "https://")):
                    data_uri = self.download_and_convert_to_base64(image_url)
                else:
                    data_uri = self.convert_local_image_to_base64(image_url)

                if data_uri:
                    return f'style="{full_style.replace(f"url({image_url})", f"url({data_uri})")}"'
                else:
                    # 如果下载失败，移除背景图片样式
                    logger.warning(f"背景图片处理失败，已移除背景图片样式: {image_url}")
                    cleaned_style = re.sub(r'background-image:\s*url\([^)]+\);?', '', full_style)
                    return f'style="{cleaned_style}"'

            except Exception as e:
                logger.error(f"处理背景图片失败: {image_url}, 错误: {e}")
                logger.warning(f"背景图片处理异常，已移除背景图片样式: {image_url}")
                # 出错时移除背景图片样式
                cleaned_style = re.sub(r'background-image:\s*url\([^)]+\);?', '', full_style)
                return f'style="{cleaned_style}"'

        # 替换背景图片样式
        svg_content = re.sub(bg_style_pattern, replace_bg_style, svg_content)

        # 处理pattern中的图片
        pattern_image_pattern = r'(<pattern[^>]*>)(.*?)(<image[^>]*href="([^"]+)"[^>]*/>)(.*?</pattern>)'

        def replace_pattern_image(match):
            pattern_start = match.group(1)
            before_image = match.group(2)
            image_tag = match.group(3)
            image_url = match.group(4)
            pattern_end = match.group(5)

            # 如果已经是数据URI，直接返回
            if image_url.startswith("data:"):
                return pattern_start + before_image + image_tag + pattern_end

            try:
                # 获取数据URI（本地或远程）
                data_uri = ""
                if image_url.startswith(("http://", "https://")):
                    data_uri = self.download_and_convert_to_base64(image_url)
                else:
                    data_uri = self.convert_local_image_to_base64(image_url)

                if data_uri:
                    modified_image_tag = image_tag.replace(f'href="{image_url}"', f'href="{data_uri}"')
                    return pattern_start + before_image + modified_image_tag + pattern_end
                else:
                    # 如果下载失败，移除图片元素
                    logger.warning(f"Pattern中的图片处理失败，已移除图片元素: {image_url}")
                    return pattern_start + before_image + pattern_end

            except Exception as e:
                logger.error(f"处理pattern中的图片失败: {image_url}, 错误: {e}")
                logger.warning(f"Pattern中的图片处理异常，已移除图片元素: {image_url}")
                # 出错时移除图片元素
                return pattern_start + before_image + pattern_end

        # 替换pattern中的图片
        return re.sub(pattern_image_pattern, replace_pattern_image, svg_content, flags=re.DOTALL)

    def fix_svg_syntax_errors(self, svg_content: str) -> str:
        """修复SVG中的语法错误

        Args:
            svg_content: 原始SVG内容

        Returns:
            修复后的SVG内容
        """
        try:
            logger.info("🔧 开始修复SVG语法错误...")

            # 1. 修复数学表达式（如 cx="180 + 312" 应该是 cx="492"）
            svg_content = self._fix_math_expressions(svg_content)

            # 2. 修复无效的属性值（如 width="auto" 应该移除或设置默认值）
            svg_content = self._fix_invalid_attributes(svg_content)

            # 3. 修复重复属性
            svg_content = self._fix_duplicate_attributes(svg_content)

            # 4. 修复其他常见语法错误
            svg_content = self._fix_common_syntax_errors(svg_content)

            logger.info("✅ SVG语法错误修复完成")
            return svg_content

        except Exception as e:
            logger.error(f"修复SVG语法错误失败: {e}")
            return svg_content  # 返回原始内容，避免阻塞流程

    def _fix_math_expressions(self, svg_content: str) -> str:
        """修复SVG属性中的数学表达式"""
        try:
            # 匹配包含数学表达式的属性值
            math_pattern = r'(\w+)="([^"]*(?:\d+\s*[+\-*/]\s*\d+[^"]*)+)"'

            def evaluate_expression(match):
                attr_name = match.group(1)
                attr_value = match.group(2)

                try:
                    # 尝试计算简单的数学表达式
                    # 只处理安全的数学运算（数字和基本运算符）
                    if re.match(r'^[\d\s+\-*/().]+$', attr_value):
                        # 安全地计算表达式
                        result = eval(attr_value)
                        if isinstance(result, (int, float)):
                            logger.info(f"修复数学表达式: {attr_name}=\"{attr_value}\" -> {attr_name}=\"{result}\"")
                            return f'{attr_name}="{result}"'
                except:
                    pass

                # 如果无法计算，尝试提取第一个数字
                numbers = re.findall(r'\d+', attr_value)
                if numbers:
                    first_number = numbers[0]
                    logger.warning(f"无法计算表达式，使用第一个数字: {attr_name}=\"{attr_value}\" -> {attr_name}=\"{first_number}\"")
                    return f'{attr_name}="{first_number}"'

                # 如果都失败了，返回原始值
                return match.group(0)

            return re.sub(math_pattern, evaluate_expression, svg_content)

        except Exception as e:
            logger.error(f"修复数学表达式失败: {e}")
            return svg_content

    def _fix_invalid_attributes(self, svg_content: str) -> str:
        """修复无效的属性值"""
        try:
            # 修复 width="auto" 和 height="auto" - 保持标签结构
            svg_content = re.sub(r'\s+width="auto"', '', svg_content)
            svg_content = re.sub(r'\s+height="auto"', '', svg_content)

            # 修复空的属性值 - 保持标签结构
            svg_content = re.sub(r'\s+\w+=""', '', svg_content)

            # 修复包含非法字符的属性值
            # 例如：fill="url(#gradient" 缺少右括号
            svg_content = re.sub(r'fill="url\(#([^")]+)"', r'fill="url(#\1)"', svg_content)

            logger.info("✅ 无效属性修复完成")
            return svg_content

        except Exception as e:
            logger.error(f"修复无效属性失败: {e}")
            return svg_content

    def _fix_duplicate_attributes(self, svg_content: str) -> str:
        """修复重复的属性"""
        try:
            # 匹配所有标签
            def fix_tag_attributes(match):
                tag_start = match.group(1)  # 标签名和开始部分
                tag_content = match.group(2)  # 标签内容
                tag_end = match.group(3)  # 标签结束部分

                # 提取所有属性
                attrs = {}
                attr_pattern = r'(\w+)="([^"]*)"'

                # 找到所有属性，后面的覆盖前面的
                for attr_match in re.finditer(attr_pattern, tag_content):
                    attr_name = attr_match.group(1)
                    attr_value = attr_match.group(2)
                    attrs[attr_name] = attr_value

                # 重建属性字符串
                if attrs:
                    new_attrs = ' ' + ' '.join([f'{name}="{value}"' for name, value in attrs.items()])
                else:
                    new_attrs = ''

                return f'{tag_start}{new_attrs}{tag_end}'

            # 应用到所有标签（包括自闭合标签）
            svg_content = re.sub(r'(<\w+)([^>]*?)(/?>\s*)', fix_tag_attributes, svg_content)

            logger.info("✅ 重复属性修复完成")
            return svg_content

        except Exception as e:
            logger.error(f"修复重复属性失败: {e}")
            return svg_content

    def _fix_common_syntax_errors(self, svg_content: str) -> str:
        """修复其他常见的SVG语法错误"""
        try:
            # 1. 修复错误的标签结束（如 </tspan> 应该是 </text>）
            svg_content = self._fix_mismatched_tags(svg_content)

            # 2. 修复多余的空格（但保持基本结构）
            svg_content = re.sub(r'\s+', ' ', svg_content)
            svg_content = re.sub(r'>\s+<', '><', svg_content)

            # 3. 确保SVG根元素有正确的命名空间
            if '<svg' in svg_content and 'xmlns=' not in svg_content:
                svg_content = svg_content.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"', 1)

            # 4. 修复缺失的viewBox
            if '<svg' in svg_content and 'viewBox=' not in svg_content:
                width_match = re.search(r'width="(\d+)"', svg_content)
                height_match = re.search(r'height="(\d+)"', svg_content)
                if width_match and height_match:
                    width = width_match.group(1)
                    height = height_match.group(1)
                    svg_content = svg_content.replace('<svg', f'<svg viewBox="0 0 {width} {height}"', 1)

            logger.info("✅ 常见语法错误修复完成")
            return svg_content

        except Exception as e:
            logger.error(f"修复常见语法错误失败: {e}")
            return svg_content

    def _fix_mismatched_tags(self, svg_content: str) -> str:
        """修复不匹配的标签"""
        try:
            # 修复常见的标签不匹配问题
            fixes = [
                # text标签内容以tspan结束的问题
                (r'(<text[^>]*>[^<]*)</tspan>', r'\1</text>'),
                # tspan标签内容以text结束的问题
                (r'(<tspan[^>]*>[^<]*)</text>', r'\1</tspan>'),
                # 其他常见的标签不匹配
                (r'(<rect[^>]*>[^<]*)</rect>', r'\1/>'),  # rect应该是自闭合的
                (r'(<circle[^>]*>[^<]*)</circle>', r'\1/>'),  # circle应该是自闭合的
                (r'(<line[^>]*>[^<]*)</line>', r'\1/>'),  # line应该是自闭合的
            ]

            for pattern, replacement in fixes:
                old_content = svg_content
                svg_content = re.sub(pattern, replacement, svg_content)
                if svg_content != old_content:
                    logger.info(f"修复了标签不匹配: {pattern}")

            return svg_content

        except Exception as e:
            logger.error(f"修复标签不匹配失败: {e}")
            return svg_content

# 创建全局实例
image_processor = ImageProcessor()
