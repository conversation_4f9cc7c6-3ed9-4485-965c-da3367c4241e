<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      /* Basic Colors */
      .bg-primary { fill: #1E40AF; }
      .bg-secondary { fill: #475569; }
      .bg-accent { fill: #3B82F6; }
      .bg-background { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .text-white { fill: #FFFFFF; } /* Added for contrast */
      .card-bg { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-bg { fill: #E0F2FE; }

      /* Gradients */
      .gradient-primary-fill { fill: url(#primaryGradient); }
      .gradient-accent-fill { fill: url(#accentGradient); }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 700; }
      .content-title { font-size: 28px; font-weight: 600; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
      .small-text { font-size: 16px; font-weight: 400; }
      .caption-text { font-size: 14px; font-weight: 400; }

      /* Card Styles */
      .card {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        rx: 12px;
        ry: 12px;
        filter: url(#cardShadow);
        transition: transform 0.3s ease-in-out, filter 0.3s ease-in-out; /* Added transition for hover */
      }
      .card:hover {
        transform: translateY(-8px); /* Increased transform for more noticeable effect */
        filter: url(#cardHoverShadow);
      }

      /* Icon Styles */
      .icon-style {
        stroke: #1E40AF; /* Primary color for icons */
        stroke-width: 2px;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
      }
      .icon-fill {
        fill: #1E40AF; /* Primary color for filled icons */
      }
    </style>

    <!-- Linear Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Filters for Shadows -->
    <filter id="cardShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
    <filter id="cardHoverShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="10" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="7.5" />
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feMerge>
        <feMergeNode in="matrixOut" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- Icons -->
    <!-- Icon for Clinical Data (Bar Chart) -->
    <symbol id="icon-data" viewBox="0 0 24 24">
      <path d="M4 10h16M4 14h16M4 18h16M4 6h16" class="icon-style" />
      <path d="M9 20v-4M15 20v-8M12 20v-6" class="icon-style" />
    </symbol>
    <!-- Icon for Research Results (Magnifying Glass with Atom) -->
    <symbol id="icon-research" viewBox="0 0 24 24">
      <circle cx="11" cy="11" r="8" class="icon-style" />
      <line x1="21" y1="21" x2="16.65" y2="16.65" class="icon-style" />
      <circle cx="11" cy="8" r="1.5" class="icon-style" />
      <circle cx="14" cy="11" r="1.5" class="icon-style" />
      <circle cx="8" cy="14" r="1.5" class="icon-style" />
    </symbol>
    <!-- Icon for Treatment Plans (Medical Cross with Heartbeat) -->
    <symbol id="icon-treatment" viewBox="0 0 24 24">
      <path d="M12 2L12 22M2 12L22 12" class="icon-style" />
      <path d="M7 12h-2v4h2v-4zM17 12h2v4h-2v-4zM12 7v-2h4v2h-4zM12 17v2h4v-2h-4z" class="icon-style" />
      <path d="M3 8L10 15L14 9L21 16" class="icon-style"/>
    </symbol>
    <!-- Icon for Patient Care (Human Figure with Shield) -->
    <symbol id="icon-patient" viewBox="0 0 24 24">
      <path d="M12 2c-3.31 0-6 2.69-6 6v4h12V8c0-3.31-2.69-6-6-6z" class="icon-style" />
      <path d="M4 14h16v8H4z" class="icon-style" />
      <path d="M10 18h4" class="icon-style" />
      <path d="M12 14v4" class="icon-style" />
    </symbol>
    <!-- Icon for Innovation (Lightbulb with Gear) -->
    <symbol id="icon-innovation" viewBox="0 0 24 24">
      <path d="M9 21c.5 1 1.5 2 3 2s2.5-1 3-2M12 18v3M10 9l-1.5-1.5M14 9l1.5-1.5M10 15l-1.5 1.5M14 15l1.5 1.5M12 2c-2.76 0-5 2.24-5 5h10c0-2.76-2.24-5-5-5z" class="icon-style" />
      <circle cx="12" cy="12" r="3" class="icon-style" />
      <path d="M17.8 17.8l-1.4 1.4M6.2 17.8l1.4 1.4M17.8 6.2l1.4-1.4M6.2 6.2l-1.4-1.4" class="icon-style" />
    </symbol>
    <!-- Icon for Education (Book with Graduation Cap) -->
    <symbol id="icon-education" viewBox="0 0 24 24">
      <path d="M4 19.5V14.5M4 14.5L12 9.5L20 14.5M20 19.5V14.5M12 4.5L20 9.5L12 14.5L4 9.5L12 4.5" class="icon-style" />
      <path d="M10 12L14 12M12 10L12 14" class="icon-style" />
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-background" />

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="180" height="40" rx="6" ry="6" class="container-bg" />
    <text x="170" y="87" text-anchor="middle" class="content-title text-primary font-primary">
      <tspan>医研智库</tspan>
    </text>
    <text x="170" y="105" text-anchor="middle" class="small-text text-secondary font-primary">
      <tspan>Med Research Hub</tspan>
    </text>

    <!-- Page Number -->
    <text x="1760" y="87" text-anchor="end" class="section-title text-primary font-primary">
      <tspan>2</tspan>
    </text>
    <text x="1790" y="87" text-anchor="end" class="body-text text-secondary font-primary">
      <tspan>/ 10</tspan>
    </text>
  </g>

  <!-- Main Content Area -->
  <g id="main-content">
    <!-- Main Title -->
    <text x="960" y="200" text-anchor="middle" class="main-title text-primary font-primary">
      <tspan>目录：研究成果和临床应用</tspan>
    </text>
    <text x="960" y="245" text-anchor="middle" class="body-text text-secondary font-primary">
      <tspan>Contents: Research Findings and Clinical Applications</tspan>
    </text>

    <!-- Directory Cards Grid -->
    <g id="directory-cards">
      <!-- Card 1: 临床数据分析 Clinical Data Analysis -->
      <rect x="160" y="320" width="500" height="250" class="card" />
      <use xlink:href="#icon-data" x="190" y="350" width="48" height="48" class="icon-style" />
      <text x="210" y="440" class="hero-title text-primary font-accent">
        <tspan>01</tspan>
      </text>
      <text x="210" y="480" class="content-title text-primary font-primary">
        <tspan>临床数据分析</tspan>
      </text>
      <text x="210" y="508" class="small-text text-secondary font-primary">
        <tspan>Clinical Data Analysis</tspan>
      </text>
      <text x="210" y="536" class="caption-text text-light font-primary">
        <tspan>深度解读患者数据和趋势</tspan>
      </text>

      <!-- Card 2: 基础医学研究 Basic Medical Research -->
      <rect x="710" y="320" width="500" height="250" class="card" />
      <use xlink:href="#icon-research" x="740" y="350" width="48" height="48" class="icon-style" />
      <text x="760" y="440" class="hero-title text-primary font-accent">
        <tspan>02</tspan>
      </text>
      <text x="760" y="480" class="content-title text-primary font-primary">
        <tspan>基础医学研究</tspan>
      </text>
      <text x="760" y="508" class="small-text text-secondary font-primary">
        <tspan>Fundamental Medical Research</tspan>
      </text>
      <text x="760" y="536" class="caption-text text-light font-primary">
        <tspan>探索生命科学前沿发现</tspan>
      </text>

      <!-- Card 3: 治疗方案优化 Treatment Protocol Optimization -->
      <rect x="1260" y="320" width="500" height="250" class="card" />
      <use xlink:href="#icon-treatment" x="1290" y="350" width="48" height="48" class="icon-style" />
      <text x="1310" y="440" class="hero-title text-primary font-accent">
        <tspan>03</tspan>
      </text>
      <text x="1310" y="480" class="content-title text-primary font-primary">
        <tspan>治疗方案优化</tspan>
      </text>
      <text x="1310" y="508" class="small-text text-secondary font-primary">
        <tspan>Treatment Protocol Optimization</tspan>
      </text>
      <text x="1310" y="536" class="caption-text text-light font-primary">
        <tspan>提升临床疗效和患者体验</tspan>
      </text>

      <!-- Card 4: 患者护理与康复 Patient Care and Rehabilitation -->
      <rect x="160" y="600" width="500" height="250" class="card" />
      <use xlink:href="#icon-patient" x="190" y="630" width="48" height="48" class="icon-style" />
      <text x="210" y="720" class="hero-title text-primary font-accent">
        <tspan>04</tspan>
      </text>
      <text x="210" y="760" class="content-title text-primary font-primary">
        <tspan>患者护理和康复</tspan>
      </text>
      <text x="210" y="788" class="small-text text-secondary font-primary">
        <tspan>Patient Care and Rehabilitation</tspan>
      </text>
      <text x="210" y="816" class="caption-text text-light font-primary">
        <tspan>全面关注患者生活质量</tspan>
      </text>

      <!-- Card 5: 医疗技术创新 Medical Technology Innovation -->
      <rect x="710" y="600" width="500" height="250" class="card" />
      <use xlink:href="#icon-innovation" x="740" y="630" width="48" height="48" class="icon-style" />
      <text x="760" y="720" class="hero-title text-primary font-accent">
        <tspan>05</tspan>
      </text>
      <text x="760" y="760" class="content-title text-primary font-primary">
        <tspan>医疗技术创新</tspan>
      </text>
      <text x="760" y="788" class="small-text text-secondary font-primary">
        <tspan>Medical Technology Innovation</tspan>
      </text>
      <text x="760" y="816" class="caption-text text-light font-primary">
        <tspan>驱动未来医疗发展</tspan>
      </text>

      <!-- Card 6: 医护教育与培训 Healthcare Education and Training -->
      <rect x="1260" y="600" width="500" height="250" class="card" />
      <use xlink:href="#icon-education" x="1290" y="630" width="48" height="48" class="icon-style" />
      <text x="1310" y="720" class="hero-title text-primary font-accent">
        <tspan>06</tspan>
      </text>
      <text x="1310" y="760" class="content-title text-primary font-primary">
        <tspan>医护教育和培训</tspan>
      </text>
      <text x="1310" y="788" class="small-text text-secondary font-primary">
        <tspan>Healthcare Education and Training</tspan>
      </text>
      <text x="1310" y="816" class="caption-text text-light font-primary">
        <tspan>培养高素质医疗人才</tspan>
      </text>
    </g>

  </g>

  <!-- Footer Section -->
  <g id="footer">
    <!-- Progress Indicator -->
    <rect x="860" y="940" width="200" height="8" rx="4" ry="4" class="bg-secondary" />
    <rect x="860" y="940" width="40" height="8" rx="4" ry="4" class="bg-accent" />
    <text x="960" y="975" text-anchor="middle" class="small-text text-secondary font-primary">
      <tspan>Page 2 of 10</tspan>
    </text>
  </g>

  <!-- Decorative Elements -->
  <g id="decorative-elements">
    <!-- Subtle geometric pattern in background -->
    <circle cx="100" cy="980" r="60" fill="#E0F2FE" opacity="0.5" />
    <rect x="1750" y="50" width="100" height="100" rx="12" ry="12" fill="#E0F2FE" opacity="0.3" />
    <rect x="50" y="880" width="120" height="120" rx="24" ry="24" fill="#E0F2FE" opacity="0.4" transform="rotate(15 110 940)" />

    <!-- Accent line under title -->
    <rect x="810" y="270" width="300" height="4" rx="2" ry="2" class="bg-accent" />
  </g>

</svg>