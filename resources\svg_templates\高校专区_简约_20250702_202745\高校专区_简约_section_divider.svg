<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Colors from the provided palette -->
    <style type="text/css">
      <![CDATA[
        .bg-color { fill: #F8FAFC; }
        .primary-color { fill: #3B82F6; }
        .secondary-color { fill: #7DD3FC; }
        .accent-color { fill: #BAE6FD; }
        .text-primary-color { fill: #1E293B; }
        .text-secondary-color { fill: #64748B; }

        .font-inter { font-family: "Inter", Helvetica, Arial, sans-serif; }
        .font-poppins { font-family: "Poppins", sans-serif; }

        .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
        .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* semibold */
        .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
        .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }

        .text-center { text-anchor: middle; }

        /* Gradients - ensure no '&' symbols */
        .gradient-primary-fill { fill: url(#primaryGradient); }
        .gradient-accent-fill { fill: url(#accentGradient); }
        .highlight-opacity-fill { fill: url(#highlightOpacityGradient); }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="rotate(135)">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="rotate(45)">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- High-light color transparency gradient (single color, varying opacity) -->
    <linearGradient id="highlightOpacityGradient" x1="0" y1="0" x2="0" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.0"/>
      <stop offset="50%" stop-color="#3B82F6" stop-opacity="0.2"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.0"/>
    </linearGradient>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative Elements - Abstract, flowing shapes suggesting academic progress and data flow -->
  <!-- Main large flowing shape with primary gradient -->
  <path d="M0 250 C 300 150, 600 350, 960 250 S 1500 100, 1920 200 V 0 H 0 Z" class="gradient-primary-fill" opacity="0.1"/>
  <path d="M0 850 C 300 950, 600 750, 960 850 S 1500 1000, 1920 900 V 1080 H 0 Z" class="gradient-primary-fill" opacity="0.1"/>

  <!-- Subtle accent lines/shapes -->
  <rect x="100" y="50" width="200" height="4" class="accent-color" rx="2" opacity="0.4"/>
  <rect x="1620" y="1030" width="200" height="4" class="accent-color" rx="2" opacity="0.4"/>

  <circle cx="150" cy="150" r="10" class="secondary-color" opacity="0.3"/>
  <circle cx="1770" cy="930" r="10" class="secondary-color" opacity="0.3"/>

  <!-- Central decorative elements using highlight opacity gradient and subtle lines -->
  <rect x="760" y="300" width="400" height="4" rx="2" class="highlight-opacity-fill"/>
  <rect x="760" y="770" width="400" height="4" rx="2" class="highlight-opacity-fill"/>

  <!-- Central stylized geometric shapes (e.g., abstract data flow or connection) -->
  <path d="M960 350 L880 430 L960 510 L1040 430 Z" fill="none" stroke="#3B82F6" stroke-width="6" opacity="0.15"/>
  <path d="M960 550 L880 630 L960 710 L1040 630 Z" fill="none" stroke="#3B82F6" stroke-width="6" opacity="0.15"/>
  <line x1="960" y1="510" x2="960" y2="550" stroke="#7DD3FC" stroke-width="4" opacity="0.2"/>


  <!-- Main Content - Chapter Title -->
  <g id="chapter-title-section">
    <!-- Main Chinese Title -->
    <text x="960" y="470" class="font-inter hero-title text-primary-color text-center">
      <tspan x="960" y="470">{title}</tspan>
      <tspan x="960" y="550" class="font-poppins section-title text-secondary-color">{subtitle}</tspan>
    </text>

    <!-- Subtitle/Context -->
    <text x="960" y="650" class="font-inter body-text text-secondary-color text-center">
      <tspan x="960" y="650">深入分析学术前沿和创新成果</tspan>
      <tspan x="960" y="690" class="font-inter small-text text-secondary-color">In-depth Analysis of Academic Frontiers and Innovative Achievements</tspan>
    </text>

    <!-- Page Number -->
    <text x="960" y="790" class="font-inter section-title text-primary-color text-center">
      <tspan x="960" y="790">章节 3 / 10</tspan>
    </text>
  </g>

  <!-- Placeholder for Logo (Top Left) -->
  <g id="logo-placeholder">
    <rect x="80" y="60" width="150" height="40" rx="5" class="accent-color" opacity="0.2"/>
    <text x="155" y="88" class="font-inter small-text text-primary-color text-center">
      <tspan x="155" y="88">{logo_url}</tspan>
    </text>
  </g>

  <!-- Placeholder for Date (Bottom Right) -->
  <g id="date-placeholder">
    <text x="1840" y="1020" class="font-inter small-text text-secondary-color text-center" text-anchor="end">
      <tspan x="1840" y="1020">{date}</tspan>
    </text>
  </g>

</svg>