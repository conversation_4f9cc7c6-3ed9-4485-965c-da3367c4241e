<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors -->
    <style type="text/css">
      /* Color Palette */
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes */
      .hero-title { font-size: 72px; }
      .main-title { font-size: 56px; }
      .section-title { font-size: 36px; }
      .content-title { font-size: 28px; }
      .body-text { font-size: 22px; }
      .small-text { font-size: 16px; }
      .caption { font-size: 14px; }

      /* Font Weights */
      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }

      /* General Styles */
      .no-select { user-select: none; } /* For text elements, prevent selection */

      /* Shadows */
      /* Note: Using feOffset dx/dy and stdDeviation to create the shadow effect */
      .card-shadow { filter: url(#drop-shadow-card); }
      .icon-shadow { filter: url(#drop-shadow-icon); }

      /* Timeline Specific */
      .timeline-line { stroke: #4A86E8; stroke-width: 8px; stroke-linecap: round; }
      .timeline-node-circle { fill: #4A86E8; stroke: #FFFFFF; stroke-width: 4px; }
      .milestone-node-circle { fill: #0EA5E9; stroke: #FFFFFF; stroke-width: 6px; }
      .timeline-card { stroke: #BAE6FD; stroke-width: 2px; fill: #FFFFFF; rx: 12px; ry: 12px; }
    </style>

    <!-- Gradients -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>

    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>

    <linearGradient id="gradientBackground" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Drop Shadows -->
    <filter id="drop-shadow-card" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4" result="offsetblur"/>
      <feGaussianBlur in="offsetblur" stdDeviation="3" result="blurred"/>
      <feFlood flood-color="rgba(0, 0, 0, 0.1)" result="floodcolor"/>
      <feComposite in="floodcolor" in2="blurred" operator="in" result="shadow"/>
      <feMerge>
        <feMergeNode in="shadow"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <filter id="drop-shadow-icon" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2" result="offsetblur"/>
      <feGaussianBlur in="offsetblur" stdDeviation="1" result="blurred"/>
      <feFlood flood-color="rgba(0, 0, 0, 0.05)" result="floodcolor"/>
      <feComposite in="floodcolor" in2="blurred" operator="in" result="shadow"/>
      <feMerge>
        <feMergeNode in="shadow"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icons (simple outline style) -->
    <g id="icon-rocket" fill="none" stroke="#0EA5E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M14.5 10.5l-9-9C5.2 1.2 4.7 1 4.2 1S3.2 1.2 3 1.5L1.5 3C1.2 3.2 1 3.7 1 4.2S1.2 5.2 1.5 5.5l9 9c.3.3.8.5 1.3.5s1-.2 1.3-.5l1.5-1.5c.3-.3.5-.8.5-1.3s-.2-1-.5-1.3z"/>
      <path d="M18 13l2.5-2.5M16 15l2.5-2.5M14 17l2.5-2.5"/>
    </g>

    <g id="icon-book" fill="none" stroke="#0EA5E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20V6.5A2.5 2.5 0 0 0 17.5 4H6.5A2.5 2.5 0 0 0 4 6.5V19.5z"/>
      <path d="M14 8H8M14 12H8M14 16H8"/>
    </g>

    <g id="icon-award" fill="none" stroke="#0EA5E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <circle cx="12" cy="8" r="7"/>
      <path d="M8.21 13.89L7 22l5-3 5 3-1.21-8.11"/>
    </g>

    <g id="icon-lightbulb" fill="none" stroke="#0EA5E9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M9 18h6M12 21v-3M12 2C7.029 2 3 6.029 3 11c0 3.98 2.5 6.33 4 8.5V22h10v-2.5c1.5-2.17 4-4.52 4-8.5C21 6.029 16.971 2 12 2z"/>
    </g>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#gradientBackground)" />

  <!-- Decorative elements (geometric shapes) -->
  <circle cx="1700" cy="100" r="80" fill="#4A86E8" opacity="0.1" />
  <rect x="1650" y="900" width="200" height="100" fill="#0EA5E9" opacity="0.08" rx="20" ry="20" />
  <polygon points="100 950, 250 900, 200 1050" fill="#3B82F6" opacity="0.05" />

  <!-- Header Section -->
  <g id="header-section">
    <image xlink:href="{logo_url}" x="80" y="60" width="150" height="auto" />
    <text x="1760" y="90" text-anchor="end" class="font-primary small-text text-light no-select">页面 8/10</text>
  </g>

  <!-- Main Title -->
  <text x="960" y="200" text-anchor="middle" class="font-primary main-title font-bold text-primary no-select">
    <tspan>我们的发展里程碑</tspan>
  </text>
  <text x="960" y="250" text-anchor="middle" class="font-secondary content-title text-secondary no-select">
    <tspan>Our Development Milestones and Journey</tspan>
  </text>

  <!-- Timeline Line -->
  <line x1="960" y1="350" x2="960" y2="950" class="timeline-line" />

  <!-- Timeline Nodes and Events -->

  <!-- Event 1: Foundation -->
  <g id="event-1-foundation">
    <circle cx="960" cy="350" r="15" class="timeline-node-circle" />
    <use xlink:href="#icon-rocket" x="936" y="310" width="48" height="48" class="icon-shadow" />
    <rect x="1000" y="320" width="450" height="120" class="timeline-card card-shadow" />
    <text x="1025" y="355" class="font-primary content-title font-bold text-primary no-select">
      <tspan>{date} 奠基成立</tspan>
    </text>
    <text x="1025" y="385" class="font-secondary body-text text-secondary no-select">
      <tspan>Foundation and Establishment</tspan>
      <tspan x="1025" dy="30">{content} 开启教育培训新篇章和创新。</tspan>
    </text>
  </g>

  <!-- Event 2: Curriculum Development -->
  <g id="event-2-curriculum">
    <circle cx="960" cy="500" r="15" class="timeline-node-circle" />
    <use xlink:href="#icon-book" x="936" y="460" width="48" height="48" class="icon-shadow" />
    <rect x="470" y="470" width="450" height="120" class="timeline-card card-shadow" />
    <text x="495" y="505" text-anchor="start" class="font-primary content-title font-bold text-primary no-select">
      <tspan>{date} 课程体系完善</tspan>
    </text>
    <text x="495" y="535" text-anchor="start" class="font-secondary body-text text-secondary no-select">
      <tspan>Curriculum System Optimization</tspan>
      <tspan x="495" dy="30">{content} 引入先进教学方法和互动模式。</tspan>
    </text>
  </g>

  <!-- Event 3: Major Milestone -->
  <g id="event-3-milestone">
    <circle cx="960" cy="650" r="20" class="milestone-node-circle" />
    <use xlink:href="#icon-award" x="930" y="605" width="60" height="60" class="icon-shadow" />
    <rect x="1000" y="620" width="450" height="120" class="timeline-card card-shadow" />
    <text x="1025" y="655" class="font-primary content-title font-bold text-primary no-select">
      <tspan>{date} 荣获行业大奖</tspan>
    </text>
    <text x="1025" y="685" class="font-secondary body-text text-secondary no-select">
      <tspan>Industry Award Recognition</tspan>
      <tspan x="1025" dy="30">{content} 教学质量和创新能力得到认可。</tspan>
    </text>
  </g>

  <!-- Event 4: Technology Integration -->
  <g id="event-4-tech">
    <circle cx="960" cy="800" r="15" class="timeline-node-circle" />
    <use xlink:href="#icon-lightbulb" x="936" y="760" width="48" height="48" class="icon-shadow" />
    <rect x="470" y="770" width="450" height="120" class="timeline-card card-shadow" />
    <text x="495" y="805" text-anchor="start" class="font-primary content-title font-bold text-primary no-select">
      <tspan>{date} 智慧教学升级</tspan>
    </text>
    <text x="495" y="835" text-anchor="start" class="font-secondary body-text text-secondary no-select">
      <tspan>Smart Teaching Upgrade</tspan>
      <tspan x="495" dy="30">{content} 引入AI辅助学习和在线平台。</tspan>
    </text>
  </g>

  <!-- Footer -->
  <text x="960" y="1030" text-anchor="middle" class="font-primary small-text text-light no-select">
    <tspan>© {date} {author} 版权所有和保留</tspan>
  </text>

</svg>