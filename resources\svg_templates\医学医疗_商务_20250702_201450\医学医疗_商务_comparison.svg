<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette and Font System Variables -->
    <style type="text/css">
      :root {
        --primary-color: #1E40AF;
        --secondary-color: #475569;
        --accent-color: #3B82F6;
        --background-color: #F8FAFC;
        --text-primary: #1E293B;
        --text-secondary: #64748B;
        --text-light: #94A3B8;
        --success-color: #10B981;
        --warning-color: #F59E0B;
        --error-color: #EF4444;
        --info-color: #3B82F6;
        --card-background: #FFFFFF;
        --card-border: #BAE6FD;
        --container-background: #E0F2FE;
        --hover-color: #7DD3FC;
        --active-color: #1E40AF;
        --disabled-color: #64748B;
        --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .text-hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; letter-spacing: -0.025em; fill: var(--text-primary); }
      .text-main-title { font-size: 56px; font-weight: 700; line-height: 1.1; fill: var(--text-primary); }
      .text-section-title { font-size: 36px; font-weight: 600; line-height: 1.4; fill: var(--text-primary); }
      .text-content-title { font-size: 28px; font-weight: 600; line-height: 1.4; fill: var(--text-primary); }
      .text-body { font-size: 22px; font-weight: 400; line-height: 1.6; fill: var(--text-secondary); }
      .text-small { font-size: 16px; font-weight: 400; line-height: 1.6; fill: var(--text-secondary); }
      .text-caption { font-size: 14px; font-weight: 400; line-height: 1.6; fill: var(--text-light); }

      /* General Styles */
      .bg-primary { fill: var(--primary-color); }
      .bg-secondary { fill: var(--secondary-color); }
      .bg-accent { fill: var(--accent-color); }
      .bg-background { fill: var(--background-color); }
      .bg-card { fill: var(--card-background); }
      .border-card { stroke: var(--card-border); stroke-width: 1px; }

      .icon-stroke { stroke: var(--info-color); stroke-width: 2px; }
      .icon-fill { fill: var(--info-color); }

      /* Card Style */
      .card {
        fill: var(--card-background);
        stroke: var(--card-border);
        stroke-width: 1px;
        filter: url(#cardShadow); /* Apply shadow filter */
      }
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- Filters for shadows -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)"/>
    </filter>
    <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0, 0, 0, 0.1)"/>
    </filter>

    <!-- Icons (Simplified for demonstration) -->
    <symbol id="icon-research" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="var(--accent-color)"/>
    </symbol>
    <symbol id="icon-clinical" viewBox="0 0 24 24">
      <path d="M19 13H5v-2h14v2zm-2-7H7v2h10V6zm4 5c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2v6z" fill="var(--accent-color)"/>
    </symbol>
    <symbol id="icon-data" viewBox="0 0 24 24">
      <path d="M16 11V5h-2v6h2zm0 8h2v-6h-2v6zm-4 0h2v-2h-2v2zm-4 0h2v-4h-2v4zm-3-5h2v-7h-2v7zm0 5h2v-2h-2v2zM21 2H3c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM3 20V4h18v16H3z" fill="var(--accent-color)"/>
    </symbol>
    <symbol id="icon-arrow-right" viewBox="0 0 24 24">
      <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" fill="var(--card-background)"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Header -->
  <g id="header">
    <image xlink:href="{logo_url}" x="80" y="60" width="160" height="40" preserveAspectRatio="xMidYMid meet"/>
    <text x="960" y="100" text-anchor="middle" class="font-primary text-main-title">
      <tspan fill="var(--primary-color)">临床研究</tspan>
      <tspan fill="var(--text-secondary)">对比分析</tspan>
    </text>
    <text x="960" y="150" text-anchor="middle" class="font-secondary text-content-title" fill="var(--text-secondary)">
      <tspan>Clinical Research Comparison 和#38; Analysis</tspan>
    </text>
  </g>

  <!-- Main Content - Comparison Section -->
  <g id="comparison-section">
    <!-- Left Card: Current Therapy -->
    <rect x="140" y="240" width="780" height="600" rx="12" class="card"/>
    <text x="530" y="290" text-anchor="middle" class="font-primary text-section-title" fill="var(--primary-color)">
      <tspan>现有治疗方案</tspan>
    </text>
    <text x="530" y="330" text-anchor="middle" class="font-secondary text-small" fill="var(--text-secondary)">
      <tspan>Current Therapy Protocol</tspan>
    </text>

    <!-- Left Card Content -->
    <text x="180" y="390" class="font-primary text-content-title" fill="var(--text-primary)">
      <tspan>优势</tspan>
    </text>
    <text x="180" y="430" class="font-secondary text-body" fill="var(--text-secondary)">
      <tspan>和#x2022; 广泛应用和认可</tspan>
      <tspan x="180" dy="35">和#x2022; 长期临床数据支持</tspan>
      <tspan x="180" dy="35">和#x2022; 成本效益较高</tspan>
    </text>

    <text x="180" y="580" class="font-primary text-content-title" fill="var(--text-primary)">
      <tspan>挑战</tspan>
    </text>
    <text x="180" y="620" class="font-secondary text-body" fill="var(--text-secondary)">
      <tspan>和#x2022; 特定患者响应率低</tspan>
      <tspan x="180" dy="35">和#x2022; 潜在副作用影响生活质量</tspan>
      <tspan x="180" dy="35">和#x2022; 治疗周期长</tspan>
    </text>

    <!-- Right Card: Novel Approach -->
    <rect x="1020" y="240" width="780" height="600" rx="12" class="card"/>
    <text x="1410" y="290" text-anchor="middle" class="font-primary text-section-title" fill="var(--accent-color)">
      <tspan>创新治疗方法</tspan>
    </text>
    <text x="1410" y="330" text-anchor="middle" class="font-secondary text-small" fill="var(--text-secondary)">
      <tspan>Novel Treatment Approach</tspan>
    </text>

    <!-- Right Card Content -->
    <text x="1060" y="390" class="font-primary text-content-title" fill="var(--text-primary)">
      <tspan>优势</tspan>
    </text>
    <text x="1060" y="430" class="font-secondary text-body" fill="var(--accent-color)">
      <tspan>和#x2022; 更高患者响应率</tspan>
      <tspan x="1060" dy="35">和#x2022; 显著降低副作用</tspan>
      <tspan x="1060" dy="35">和#x2022; 缩短治疗周期</tspan>
    </text>

    <text x="1060" y="580" class="font-primary text-content-title" fill="var(--text-primary)">
      <tspan>挑战</tspan>
    </text>
    <text x="1060" y="620" class="font-secondary text-body" fill="var(--text-secondary)">
      <tspan>和#x2022; 临床试验数据有限</tspan>
      <tspan x="1060" dy="35">和#x2022; 初始成本较高</tspan>
      <tspan x="1060" dy="35">和#x2022; 需进一步长期随访</tspan>
    </text>

    <!-- Middle Divider / Comparison Point -->
    <g id="comparison-indicator">
      <circle cx="960" cy="540" r="30" fill="url(#accentGradient)"/>
      <use xlink:href="#icon-arrow-right" x="948" y="528" width="24" height="24"/>
      <text x="960" y="590" text-anchor="middle" class="font-primary text-body" fill="var(--primary-color)">
        <tspan>核心差异</tspan>
      </text>
      <text x="960" y="620" text-anchor="middle" class="font-secondary text-small" fill="var(--text-secondary)">
        <tspan>Key Differences</tspan>
      </text>
    </g>
  </g>

  <!-- Conclusion Section -->
  <g id="conclusion-section">
    <rect x="140" y="880" width="1640" height="140" rx="12" fill="var(--container-background)" stroke="var(--card-border)" stroke-width="1px"/>
    <text x="180" y="930" class="font-primary text-content-title" fill="var(--primary-color)">
      <tspan>结论总结</tspan>
    </text>
    <text x="180" y="970" class="font-secondary text-body" fill="var(--text-secondary)">
      <tspan>创新方法在特定指标上表现出显著优势，但仍需更多长期临床数据支持其广泛应用和普及。</tspan>
      <tspan x="180" dy="35">Novel approaches show significant advantages in specific metrics, but require more long-term clinical data to support widespread adoption.</tspan>
    </text>
  </g>

  <!-- Decorative Elements -->
  <g id="decorative-elements">
    <!-- Top-left subtle geometric pattern -->
    <circle cx="50" cy="50" r="15" fill="var(--accent-color)" opacity="0.1"/>
    <rect x="70" y="30" width="30" height="30" fill="var(--primary-color)" opacity="0.08" rx="5"/>
    <path d="M 10 100 L 60 90 L 50 40 Z" fill="var(--accent-color)" opacity="0.05"/>

    <!-- Bottom-right subtle geometric pattern -->
    <circle cx="1870" cy="1030" r="20" fill="var(--primary-color)" opacity="0.1"/>
    <rect x="1840" y="1000" width="40" height="40" fill="var(--accent-color)" opacity="0.08" rx="8"/>
    <path d="M 1910 980 L 1860 990 L 1870 1040 Z" fill="var(--primary-color)" opacity="0.05"/>

    <!-- Icons for visual flair -->
    <use xlink:href="#icon-research" x="80" y="260" width="32" height="32" class="icon-fill" opacity="0.5"/>
    <use xlink:href="#icon-clinical" x="1780" y="260" width="32" height="32" class="icon-fill" opacity="0.5"/>
    <use xlink:href="#icon-data" x="80" y="890" width="32" height="32" class="icon-fill" opacity="0.5"/>

    <!-- Subtle gradient lines -->
    <rect x="0" y="220" width="1920" height="2" fill="url(#primaryGradient)" opacity="0.1"/>
    <rect x="0" y="860" width="1920" height="2" fill="url(#primaryGradient)" opacity="0.1"/>
  </g>

</svg>