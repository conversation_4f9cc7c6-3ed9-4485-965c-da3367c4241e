import React, { useState, useEffect, useCallback, memo } from 'react';
import { Radio, Space, Button, Input, Select, Segmented, Modal, Progress, Tooltip, Spin, message } from 'antd';
import { DownloadOutlined, FileImageOutlined, EyeOutlined, LeftOutlined, RightOutlined, ReloadOutlined } from '@ant-design/icons';

const { TextArea } = Input;

// 全局缓存模板数据，避免重复加载
let globalTemplatesCache = null;
let globalTemplatePreviewsCache = {};
let templateLoadPromise = null; // 用于确保同一时间只有一个加载请求

// SVG模板选择器组件（使用memo优化性能）
const SVGTemplateSelector = memo(({ selectedTemplate, onTemplateSelect }) => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [templatePreviews, setTemplatePreviews] = useState({});

  // 获取模板列表（使用缓存和Promise避免重复加载）
  useEffect(() => {
    const fetchTemplates = async () => {
      // 如果已有缓存，直接使用
      if (globalTemplatesCache) {
        console.log('✅ 使用缓存的模板数据，避免重复请求');
        setTemplates(globalTemplatesCache);
        setTemplatePreviews(globalTemplatePreviewsCache);
        return;
      }

      // 如果已有加载Promise，等待它完成
      if (templateLoadPromise) {
        console.log('⏳ 等待现有的模板加载请求完成...');
        try {
          await templateLoadPromise;
          setTemplates(globalTemplatesCache);
          setTemplatePreviews(globalTemplatePreviewsCache);
        } catch (error) {
          console.error('等待模板加载失败:', error);
        }
        return;
      }

      // 创建新的加载Promise
      console.log('🚀 开始首次加载模板数据...');
      setLoading(true);

      templateLoadPromise = (async () => {
        try {
          const response = await fetch('/api/svg-ppt/svg-templates');
          const data = await response.json();

          if (data.success) {
            // 缓存模板数据
            globalTemplatesCache = data.template_sets;
            console.log('✅ 模板数据加载完成，已缓存');

            // 获取每个模板的预览
            await fetchTemplatePreviews(data.template_sets);

            return data.template_sets;
          } else {
            throw new Error(data.error);
          }
        } catch (error) {
          console.error('❌ 获取模板失败:', error);
          message.error('获取模板失败: ' + error.message);
          throw error;
        }
      })();

      try {
        const templates = await templateLoadPromise;
        setTemplates(templates);
        setTemplatePreviews(globalTemplatePreviewsCache);
      } catch (error) {
        // 错误已在Promise中处理
      } finally {
        setLoading(false);
        templateLoadPromise = null; // 清除Promise引用
      }
    };

    fetchTemplates();
  }, []); // 空依赖数组，确保只执行一次

  // 获取模板预览（使用缓存）
  const fetchTemplatePreviews = async (templateSets) => {
    const previews = { ...globalTemplatePreviewsCache };

    for (const template of templateSets) {
      // 如果已有缓存，跳过
      if (previews[template.set_name]) {
        continue;
      }

      try {
        const response = await fetch(`/api/svg-ppt/template-preview/${template.set_name}`);
        const data = await response.json();

        if (data.success && data.preview_svg) {
          previews[template.set_name] = data.preview_svg;
        }
      } catch (error) {
        console.error(`获取模板预览失败 ${template.set_name}:`, error);
      }
    }

    // 更新全局缓存和本地状态
    globalTemplatePreviewsCache = previews;
    setTemplatePreviews(previews);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin size="small" />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
          加载模板中...
        </div>
      </div>
    );
  }

  return (
    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '12px',
      marginTop: '8px',
      maxHeight: '400px',
      overflowY: 'auto'
    }}>
      {templates.map(template => (
        <div
          key={template.set_name}
          style={{
            border: selectedTemplate === template.set_name ? '2px solid #1890ff' : '1px solid #d9d9d9',
            borderRadius: '8px',
            padding: '12px',
            cursor: 'pointer',
            backgroundColor: selectedTemplate === template.set_name ? '#f0f8ff' : '#fafafa',
            transition: 'all 0.3s ease'
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onTemplateSelect(template.set_name);
          }}
        >
          {/* SVG预览 */}
          <div style={{
            width: '100%',
            height: '120px',
            marginBottom: '8px',
            border: '1px solid #e8e8e8',
            borderRadius: '4px',
            overflow: 'hidden',
            backgroundColor: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {templatePreviews[template.set_name] ? (
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  overflow: 'hidden'
                }}
                dangerouslySetInnerHTML={{
                  __html: `<style>
                    svg {
                      max-width: 100% !important;
                      max-height: 100% !important;
                      width: auto !important;
                      height: auto !important;
                      display: block !important;
                      margin: auto !important;
                    }
                  </style>${templatePreviews[template.set_name]}`
                }}
              />
            ) : (
              <div style={{
                color: '#999',
                fontSize: '12px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '24px', marginBottom: '4px' }}>📄</div>
                <div>预览加载中...</div>
              </div>
            )}
          </div>

          {/* 模板信息 */}
          <div style={{
            fontSize: '13px',
            fontWeight: 'bold',
            color: selectedTemplate === template.set_name ? '#1890ff' : '#333',
            marginBottom: '4px'
          }}>
            {template.scenario} - {template.style}
          </div>
          <div style={{
            fontSize: '11px',
            color: '#666',
            lineHeight: '1.3',
            marginBottom: '6px'
          }}>
            {template.description}
          </div>
          <div style={{
            fontSize: '10px',
            color: '#999'
          }}>
            {template.template_count} 个模板
          </div>
        </div>
      ))}
      {templates.length === 0 && !loading && (
        <div style={{
          textAlign: 'center',
          color: '#999',
          padding: '20px',
          gridColumn: '1 / -1'
        }}>
          暂无可用模板
        </div>
      )}
    </div>
  );
});

const PPTContent = ({
  config, 
  updateConfig, 
  result, 
  handleDownload,
  // PPT大纲相关
  outlineContent,
  setOutlineContent,
  outlineGenerated,
  generatingOutline,
  handleGenerateOutline,
  sourceContent,
  // PPT模板相关
  pptTemplates,
  loadingTemplates,
  fetchPptTemplates,
  // SVG相关功能
  svgContent,
  svgGenerating,
  svgProgress,
  svgPreviewVisible,
  svgCurrentIndex,
  handleGenerateSvg,
  handleConvertToPpt,
  setSvgPreviewVisible,
  setSvgCurrentIndex,
  // 新增：单张SVG重新生成功能
  handleRegenerateSingleSvg,
  regeneratingSvgIndex
}) => {
  const currentConfig = config.ppt;

  // 优化模板选择回调，避免重复渲染
  const handleTemplateSelect = useCallback((templateSet) => {
    updateConfig('ppt', 'svg_template_set', templateSet);
  }, [updateConfig]);

  const ConfigPanel = () => (
    <Space direction="vertical" style={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>
      <div>
        <label>生成方式：</label>
        <Radio.Group 
          value={currentConfig.use_svg ? 'svg' : (currentConfig.use_template ? 'template' : 'simple')} 
          onChange={(e) => {
            const mode = e.target.value;
            updateConfig('ppt', 'use_template', mode === 'template');
            updateConfig('ppt', 'use_svg', mode === 'svg');
            if (mode === 'template' && pptTemplates.length === 0) {
              fetchPptTemplates();
            }
          }}
          style={{ 
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
            maxWidth: '100%'
          }}
        >
          <Radio 
            value="template"
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              width: '100%',
              maxWidth: '100%',
              overflow: 'hidden',
              whiteSpace: 'normal',
              padding: '4px 0'
            }}
          >
            <div style={{ 
              flex: 1,
              overflow: 'hidden',
              wordBreak: 'break-word',
              maxWidth: '100%'
            }}>
              <span style={{ 
                fontWeight: 'bold',
                display: 'block',
                marginBottom: '2px'
              }}>
                专业模板
              </span>
              <div style={{ 
                fontSize: '12px', 
                color: '#666',
                lineHeight: '1.3',
                wordBreak: 'break-word'
              }}>
                选择模板生成精美的专业PPT
              </div>
            </div>
          </Radio>
          <Radio 
            value="svg"
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              width: '100%',
              maxWidth: '100%',
              overflow: 'hidden',
              whiteSpace: 'normal',
              padding: '4px 0'
            }}
          >
            <div style={{ 
              flex: 1,
              overflow: 'hidden',
              wordBreak: 'break-word',
              maxWidth: '100%'
            }}>
              <span style={{ 
                fontWeight: 'bold',
                display: 'block',
                marginBottom: '2px'
              }}>
                SVG增强模式
              </span>
              <div style={{ 
                fontSize: '12px', 
                color: '#666',
                lineHeight: '1.3',
                wordBreak: 'break-word'
              }}>
                生成包含精美SVG图表和视觉元素的专业演示文稿
              </div>
            </div>
          </Radio>
        </Radio.Group>
      </div>

     
      {currentConfig.use_template && (
        <div>
          <label>选择模板：</label>
          {loadingTemplates ? (
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <span>正在加载模板...</span>
            </div>
          ) : pptTemplates.length > 0 ? (
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))', 
              gap: '12px',
              marginTop: '12px',
              maxHeight: '300px',
              overflowY: 'auto',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              padding: '12px'
            }}>
              {pptTemplates.map(template => (
                <div
                  key={template.id}
                  style={{
                    border: currentConfig.template_id === template.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    padding: '8px',
                    textAlign: 'center',
                    backgroundColor: currentConfig.template_id === template.id ? '#f0f8ff' : '#fafafa'
                  }}
                  onClick={() => updateConfig('ppt', 'template_id', template.id)}
                >
                  {template.preview_url && (
                    <img 
                      src={template.preview_url} 
                      alt={template.name}
                      style={{
                        width: '100%',
                        height: '80px',
                        objectFit: 'cover',
                        borderRadius: '4px',
                        marginBottom: '8px'
                      }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                  )}
                  <div style={{ 
                    display: template.preview_url ? 'none' : 'block',
                    height: '80px',
                    lineHeight: '80px',
                    backgroundColor: '#f0f0f0',
                    marginBottom: '8px',
                    borderRadius: '4px',
                    color: '#999'
                  }}>
                    暂无预览
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    fontWeight: 'bold',
                    color: currentConfig.template_id === template.id ? '#1890ff' : '#333'
                  }}>
                    {template.name}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{ 
              padding: '20px', 
              textAlign: 'center', 
              border: '1px dashed #d9d9d9',
              borderRadius: '6px',
              marginTop: '12px'
            }}>
              <p>暂无可用模板</p>
              <Button onClick={fetchPptTemplates} type="link">
                重新加载
              </Button>
            </div>
          )}
        </div>
      )}

      {currentConfig.use_svg && (
        <div>
          <label>SVG配置：</label>
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            padding: '16px',
            marginTop: '12px',
            backgroundColor: '#fafafa'
          }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {/* SVG生成方式选择 */}
              <div>
                <label style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '12px', display: 'block' }}>
                  🎨 SVG生成方式：
                </label>
                <Radio.Group
                  value={currentConfig.svg_generation_mode || 'free'}
                  onChange={(e) => updateConfig('ppt', 'svg_generation_mode', e.target.value)}
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '8px',
                    marginBottom: '16px'
                  }}
                >
                  <Radio value="free" style={{ fontSize: '13px' }}>
                    <span style={{ fontWeight: 'bold' }}>自由生成</span>
                    <div style={{ fontSize: '11px', color: '#666', marginLeft: '0px' }}>
                      AI根据内容自由创作SVG图表和视觉元素
                    </div>
                  </Radio>
                  <Radio value="template" style={{ fontSize: '13px' }}>
                    <span style={{ fontWeight: 'bold' }}>模板选择</span>
                    <div style={{ fontSize: '11px', color: '#666', marginLeft: '0px' }}>
                      基于预设模板生成专业演示文稿
                    </div>
                  </Radio>
                </Radio.Group>
              </div>

              {/* 根据选择的生成方式显示不同的配置 */}
              {currentConfig.svg_generation_mode === 'template' ? (
                // 模板选择模式
                <div>
                  <label style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '12px', display: 'block' }}>
                    📋 选择SVG模板集：
                  </label>
                  <SVGTemplateSelector
                    selectedTemplate={currentConfig.svg_template_set}
                    onTemplateSelect={handleTemplateSelect}
                  />
                </div>
              ) : (
                // 自由生成模式 - 显示原有的风格配置
                <div>
                  <div>
                    <label style={{ fontSize: '14px', fontWeight: 'bold' }}>SVG风格：</label>
                    <Select
                      value={currentConfig.svg_style || 'modern'}
                      onChange={(value) => updateConfig('ppt', 'svg_style', value)}
                      style={{ width: '100%', marginTop: '8px' }}
                      options={[
                        { value: 'modern', label: '现代简约' },
                        { value: 'future', label: '科技未来感' },
                        { value: 'business', label: '商务专业' },
                        { value: 'creative', label: '创意活泼' },
                        { value: 'academic', label: '学术严谨' }
                      ]}
                    />
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <label style={{ fontSize: '14px', fontWeight: 'bold' }}>页面布局：</label>
                    <Select
                      value={currentConfig.svg_layout || 'standard'}
                      onChange={(value) => updateConfig('ppt', 'svg_layout', value)}
                      style={{ width: '100%', marginTop: '8px' }}
                      options={[
                        { value: 'standard', label: '标准16:9' },
                        { value: 'wide', label: '宽屏21:9' },
                        { value: 'square', label: '方形1:1' }
                      ]}
                    />
                  </div>
                </div>
              )}

              {/* 颜色主题选择器 */}
              <div>
                <label style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '12px', display: 'block' }}>
                  🎨 选择颜色主题：
                </label>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '8px',
                  marginTop: '8px'
                }}>
                  {[
                    { value: 'golden', label: '金黄色', color: '#FFD700' },
                    { value: 'bright_yellow', label: '亮黄色', color: '#FFFF00' },
                    { value: 'light_green', label: '浅绿色', color: '#90EE90' },
                    { value: 'pink', label: '粉红色', color: '#FFC0CB' },
                    { value: 'cyan', label: '青蓝色', color: '#00CED1' },
                    { value: 'purple', label: '紫色', color: '#8A2BE2' },
                    { value: 'blue', label: '蓝色', color: '#4169E1' },
                    { value: 'brown', label: '棕色', color: '#A0522D' },
                    { value: 'red', label: '红色', color: '#DC143C' }
                  ].map(colorOption => (
                    <div
                      key={colorOption.value}
                      style={{
                        border: currentConfig.svg_color_theme === colorOption.value ? '2px solid #1890ff' : '1px solid #d9d9d9',
                        borderRadius: '6px',
                        padding: '8px',
                        cursor: 'pointer',
                        backgroundColor: currentConfig.svg_color_theme === colorOption.value ? '#f0f8ff' : '#fff',
                        transition: 'all 0.3s ease',
                        textAlign: 'center',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '4px'
                      }}
                      onClick={() => updateConfig('ppt', 'svg_color_theme', colorOption.value)}
                    >
                      <div style={{
                        width: '20px',
                        height: '20px',
                        borderRadius: '50%',
                        backgroundColor: colorOption.color,
                        border: '1px solid #ddd'
                      }} />
                      <div style={{
                        fontSize: '11px',
                        fontWeight: currentConfig.svg_color_theme === colorOption.value ? 'bold' : 'normal',
                        color: currentConfig.svg_color_theme === colorOption.value ? '#1890ff' : '#333'
                      }}>
                        {colorOption.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <label style={{ fontSize: '14px', fontWeight: 'bold' }}>页面布局：</label>
                <Select
                  value={currentConfig.svg_layout || 'standard'}
                  onChange={(value) => updateConfig('ppt', 'svg_layout', value)}
                  style={{ width: '100%', marginTop: '8px' }}
                  options={[
                    { value: 'standard', label: '标准16:9' },
                    { value: 'wide', label: '宽屏21:9' },
                    { value: 'square', label: '方形1:1' }
                  ]}
                />
              </div>
              {/* {{CHENGQI:
                Action: Removed
                Timestamp: [2025-01-16T18:35:00+08:00]
                Reason: 移除自定义需求输入框，已迁移至DeerFlowContent中，解决输入框跳动问题
                Principle_Applied: 状态管理 - 避免组件重新创建导致的焦点丢失; 用户体验 - 修复输入框交互问题
                Optimization: 将不稳定的输入框移至稳定的父组件中
                Architectural_Note (AR): 改进组件状态管理架构
                Documentation_Note (DW): 解决用户反馈的输入框跳动问题
              }} */}
            </Space>
          </div>
        </div>
      )}
    </Space>
  );

  const OutlineEditor = () => {
    //console.log("sourceContent",sourceContent);
    if (!sourceContent) return null;
    
    // SVG模式的生成面板
    if (currentConfig.use_svg) {
      return (
        <div style={{ 
          border: '1px solid #d9d9d9', 
          borderRadius: '6px', 
          padding: '16px',
          backgroundColor: '#fafafa',
          marginTop: '20px'
        }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '12px'
          }}>
            <label style={{ fontWeight: 'bold', fontSize: '16px' }}>
              🎨 SVG演示文稿生成
            </label>
            <Button 
              type="primary"
              loading={svgGenerating}
              onClick={handleGenerateSvg}
              disabled={!sourceContent.trim()}
              icon={<FileImageOutlined />}
            >
              {svgContent && svgContent.length > 0 ? '重新生成SVG' : '生成SVG演示'}
            </Button>
          </div>
          
          {/* 进度显示现在由DeerFlowContent直接管理 */}
          
          {/* 增量显示已生成的SVG页面，即使正在生成中也显示 */}
          {svgContent && svgContent.length > 0 && (
            <div>
              <div style={{
                marginBottom: '12px',
                padding: '12px 16px',
                backgroundColor: svgGenerating ? '#fff2e8' : '#e6f7ff',
                borderRadius: '6px',
                fontSize: '13px',
                color: svgGenerating ? '#fa8c16' : '#1890ff',
                border: svgGenerating ? '1px solid #ffec9e' : '1px solid #91d5ff'
              }}>
                {svgGenerating 
                  ? `⏳ 已生成 ${svgContent.length} 张SVG页面，继续生成中...` 
                  : `✨ 已生成 ${svgContent.length} 张SVG演示页面，点击预览可查看详细内容`
                }
              </div>
              
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                gap: '12px',
                maxHeight: '400px',
                overflowY: 'auto',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                padding: '12px',
                backgroundColor: '#fff'
              }}>
                {svgContent.map((svg, index) => (
                  <div
                    key={index}
                    style={{
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      padding: '8px',
                      textAlign: 'center',
                      backgroundColor: '#fafafa',
                      transition: 'all 0.3s ease'
                    }}
                    onClick={() => {
                      setSvgCurrentIndex(index);
                      setSvgPreviewVisible(true);
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f0f8ff';
                      e.currentTarget.style.borderColor = '#1890ff';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = '#fafafa';
                      e.currentTarget.style.borderColor = '#d9d9d9';
                    }}
                  >
                    {/* SVG缩略图 */}
                    <div style={{
                      width: '100%',
                      height: '120px',
                      border: '1px solid #e8e8e8',
                      borderRadius: '4px',
                      marginBottom: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#fff',
                      overflow: 'hidden',
                      position: 'relative'
                    }}>
                      <div
                        dangerouslySetInnerHTML={{
                          __html: `<style>
                            svg {
                              max-width: 100% !important;
                              max-height: 100% !important;
                              width: auto !important;
                              height: auto !important;
                              display: block !important;
                              margin: auto !important;
                            }
                          </style>${svg.content}`
                        }}
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          overflow: 'hidden'
                        }}
                      />
                      {/* 添加页面编号水印 */}
                      <div style={{
                        position: 'absolute',
                        top: '4px',
                        right: '4px',
                        backgroundColor: 'rgba(0,0,0,0.6)',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '8px',
                        fontWeight: 'bold'
                      }}>
                        {index + 1}
                      </div>
                    </div>
                    
                    <div style={{ 
                      fontSize: '12px', 
                      fontWeight: 'bold',
                      marginBottom: '4px'
                    }}>
                      第 {index + 1} 页
                    </div>
                    
                    <div style={{ 
                      fontSize: '11px', 
                      color: '#666',
                      marginBottom: '8px'
                    }}>
                      {svg.title || `幻灯片 ${index + 1}`}
                    </div>
                    
                    <div style={{ display: 'flex', gap: '4px' }}>
                      <Button 
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          setSvgCurrentIndex(index);
                          setSvgPreviewVisible(true);
                        }}
                      >
                        预览
                      </Button>
                      <Tooltip title="重新生成这一页">
                        <Button 
                          size="small"
                          icon={<ReloadOutlined />}
                          loading={regeneratingSvgIndex === index}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRegenerateSingleSvg && handleRegenerateSingleSvg(index, svg.title || `幻灯片 ${index + 1}`);
                          }}
                          type="dashed"
                          style={{ 
                            borderColor: '#1890ff',
                            color: '#1890ff'
                          }}
                        >
                          重生成
                        </Button>
                      </Tooltip>
                    </div>
                  </div>
                ))}
              </div>
              
              <div style={{ marginTop: '16px', textAlign: 'center' }}>
                <Button
                  type="primary"
                  size="large"
                  icon={<DownloadOutlined />}
                  onClick={() => handleConvertToPpt(svgContent)}
                  disabled={svgGenerating}
                  style={{ minWidth: '200px' }}
                >
                  {svgGenerating ? '生成完成后可转换' : '转换为PPT并下载'}
                </Button>
              </div>
            </div>
          )}
          
          {!svgContent && !svgGenerating && (
            <div style={{ 
              textAlign: 'center',
              color: '#999',
              padding: '60px 20px',
              border: '2px dashed #d9d9d9',
              borderRadius: '6px'
            }}>
              <FileImageOutlined style={{ fontSize: '32px', marginBottom: '16px', color: '#bfbfbf' }} />
              <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
                生成SVG演示文稿
              </div>
              <div style={{ fontSize: '13px', lineHeight: '1.5' }}>
                基于输入内容智能生成包含精美图表和视觉元素的SVG演示文稿<br />
                然后可转换为PPT格式下载
              </div>
            </div>
          )}
        </div>
      );
    }
    
    // 模板模式的大纲编辑器（保持原有逻辑）
    if (!currentConfig.use_template) return null;

    return (
      <div style={{ 
        border: '1px solid #d9d9d9', 
        borderRadius: '6px', 
        padding: '16px',
        backgroundColor: '#fafafa',
        marginTop: '20px'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '12px'
        }}>
          <label style={{ fontWeight: 'bold', fontSize: '16px' }}>
            📋 PPT大纲（专业模板）
          </label>
          <Button 
            type="primary"
            loading={generatingOutline}
            onClick={handleGenerateOutline}
            disabled={!sourceContent.trim()}
            icon={<Segmented />}
          >
            {outlineGenerated ? '重新生成大纲' : '生成PPT大纲'}
          </Button>
        </div>
        
        {outlineGenerated ? (
          <div>
            <div style={{ 
              marginBottom: '12px',
              padding: '12px 16px',
              backgroundColor: '#e6f7ff',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#1890ff',
              border: '1px solid #91d5ff'
            }}>
              💡 提示：您可以编辑下面的大纲内容，但请保持Markdown格式不变（如 #、##、### 等标记）
            </div>
            <TextArea
              value={outlineContent}
              onChange={(e) => setOutlineContent(e.target.value)}
              placeholder="PPT大纲将在这里显示，您可以编辑内容..."
              rows={10}
              style={{ 
                fontSize: '14px',
                lineHeight: '1.6',
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                backgroundColor: '#fff'
              }}
            />
            <div style={{ 
              marginTop: '8px',
              fontSize: '12px',
              color: '#666',
              textAlign: 'right'
            }}>
              字符数：{outlineContent.length}
            </div>
          </div>
        ) : (
          <div style={{ 
            textAlign: 'center',
            color: '#999',
            padding: '60px 20px',
            border: '2px dashed #d9d9d9',
            borderRadius: '6px'
          }}>
            <Segmented style={{ fontSize: '32px', marginBottom: '16px', color: '#bfbfbf' }} />
            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
              生成PPT大纲
            </div>
            <div style={{ fontSize: '13px', lineHeight: '1.5' }}>
              基于输入内容智能生成PPT大纲<br />
              您可以编辑大纲内容以获得更符合需求的PPT
            </div>
          </div>
        )}
      </div>
    );
  };

  const ResultDisplay = () => {
    if (!result || result.contentType !== 'ppt') return null;

    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <FileImageOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
        <h3>PPT演示文稿已生成</h3>
        <p style={{ color: '#666', marginBottom: '24px' }}>
          共 {currentConfig.slides === 'auto' ? '自动' : currentConfig.slides} 张幻灯片，主题：{currentConfig.theme}
        </p>
        <Space>
          <Button 
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(result.data, result.filename, result.content_type)}
          >
            下载PPT
          </Button>
        </Space>
      </div>
    );
  };

  // SVG预览模态框组件
  const SVGPreviewModal = () => {
    const [currentIndex, setCurrentIndex] = React.useState(svgCurrentIndex || 0);
    const [debugMode, setDebugMode] = React.useState(false);
    
    // 当svgCurrentIndex变化时，更新currentIndex
    React.useEffect(() => {
      setCurrentIndex(svgCurrentIndex || 0);
    }, [svgCurrentIndex]);
    
    const currentSvg = svgContent?.[currentIndex];
    
    // 调试信息
    React.useEffect(() => {
      if (currentSvg) {
        console.log('🔍 SVG预览调试信息:', {
          pageNumber: currentIndex + 1,
          title: currentSvg.title,
          hasContent: !!currentSvg.content,
          contentLength: currentSvg.content?.length || 0,
          contentPreview: currentSvg.content?.substring(0, 200) || 'No content',
          isSvgFormat: currentSvg.content?.includes('<svg') || false
        });
      }
    }, [currentIndex, currentSvg]);
    
    if (!svgContent || svgContent.length === 0) return null;
    
    return (
      <Modal
        open={svgPreviewVisible}
        onCancel={() => setSvgPreviewVisible(false)}
        width="95%"
        style={{ maxWidth: '1400px' }}
        footer={[
          <div key="controls" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <Button
                icon={<LeftOutlined />}
                onClick={() => setCurrentIndex(Math.max(0, currentIndex - 1))}
                disabled={currentIndex === 0 || regeneratingSvgIndex !== null}
                size="large"
              >
                上一页
              </Button>
              <Button
                icon={<RightOutlined />}
                onClick={() => setCurrentIndex(Math.min(svgContent.length - 1, currentIndex + 1))}
                disabled={currentIndex === svgContent.length - 1 || regeneratingSvgIndex !== null}
                size="large"
              >
                下一页
              </Button>
            </div>
            
            {/* 页面导航器 */}
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '8px',
              backgroundColor: '#f5f5f5',
              padding: '8px 12px',
              borderRadius: '20px'
            }}>
              <span style={{ fontSize: '14px', color: '#666', fontWeight: 'bold' }}>
                第 {currentIndex + 1} 页 / 共 {svgContent.length} 页
              </span>
              {svgContent.length <= 10 && (
                <div style={{ display: 'flex', gap: '4px', marginLeft: '8px' }}>
                  {svgContent.map((_, index) => (
                    <div
                      key={index}
                      onClick={() => regeneratingSvgIndex === null && setCurrentIndex(index)}
                      style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        backgroundColor: index === currentIndex ? '#1890ff' : '#d9d9d9',
                        cursor: regeneratingSvgIndex === null ? 'pointer' : 'not-allowed',
                        opacity: regeneratingSvgIndex === null ? 1 : 0.5,
                        transition: 'all 0.3s ease'
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <span style={{ fontSize: '13px', color: '#999' }}>
                {currentSvg?.title || `幻灯片 ${currentIndex + 1}`}
              </span>
              <Button
                size="small"
                onClick={() => setDebugMode(!debugMode)}
                style={{ marginRight: '8px' }}
              >
                {debugMode ? '关闭调试' : '调试模式'}
              </Button>
              <Tooltip title="重新生成当前页面">
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  loading={regeneratingSvgIndex === currentIndex}
                  onClick={() => {
                    handleRegenerateSingleSvg && handleRegenerateSingleSvg(
                      currentIndex, 
                      currentSvg?.title || `幻灯片 ${currentIndex + 1}`
                    );
                  }}
                  style={{ 
                    borderColor: '#1890ff',
                    color: '#1890ff'
                  }}
                >
                  重新生成
                </Button>
              </Tooltip>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => {
                  // {{CHENGQI:
                  // Action: Modified
                  // Timestamp: [2025-01-16T19:25:00+08:00]
                  // Reason: 修正SVG单页下载功能，添加桌面应用和网页版双重判断
                  // Principle_Applied: 环境适配 - 根据运行环境选择合适的下载方式; 用户体验 - 保持功能一致性
                  // Optimization: 桌面应用使用tk文件对话框，网页版使用原生下载，各自发挥优势
                  // Architectural_Note (AR): 统一下载接口设计，支持多环境适配
                  // Documentation_Note (DW): 修正单页SVG下载的环境适配问题
                  // }}
                  // 下载当前页面 - 桌面应用和网页版双重判断
                  if (currentSvg?.content) {
                    // 检测桌面应用环境
                    const isDesktopApp = window.pywebview || window.TKINTER_CLIENT === true;
                    
                    if (isDesktopApp) {
                       // 桌面应用环境，使用统一的handleDownload方法调用tk文件对话框
                       // 将SVG内容转换为base64格式，避免非ASCII字符问题
                       const base64Data = btoa(decodeURIComponent(encodeURIComponent(currentSvg.content)));
                       handleDownload(
                         base64Data, 
                         `slide-${currentIndex + 1}.svg`, 
                         'image/svg+xml'
                       );
                    } else {
                      // 网页环境，使用原生下载方式
                      const blob = new Blob([currentSvg.content], { type: 'image/svg+xml' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `slide-${currentIndex + 1}.svg`;
                      a.click();
                      URL.revokeObjectURL(url);
                    }
                  }
                }}
                disabled={!currentSvg?.content}
              >
                下载当前页
              </Button>
            </div>
          </div>
        ]}
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>SVG演示预览</span>
            <div style={{ fontSize: '12px', color: '#666' }}>
              按左右箭头键可切换页面
            </div>
          </div>
        }
        onKeyDown={(e) => {
          if (e.key === 'ArrowLeft' && currentIndex > 0) {
            setCurrentIndex(currentIndex - 1);
          } else if (e.key === 'ArrowRight' && currentIndex < svgContent.length - 1) {
            setCurrentIndex(currentIndex + 1);
          }
        }}
        tabIndex={0}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '500px',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          padding: '20px',
          border: '1px solid #e9ecef'
        }}>
          {debugMode ? (
            // 调试模式 - 显示原始内容和调试信息
            <div style={{
              width: '100%',
              height: '75vh',
              overflow: 'auto',
              backgroundColor: 'white',
              border: '1px solid #ddd',
              borderRadius: '8px',
              padding: '16px'
            }}>
              <div style={{ marginBottom: '16px', fontSize: '14px', fontWeight: 'bold' }}>
                调试信息 - 页面 {currentIndex + 1}
              </div>
              <div style={{ marginBottom: '12px', fontSize: '12px', color: '#666' }}>
                <strong>标题:</strong> {currentSvg?.title || 'Unknown'}
              </div>
              <div style={{ marginBottom: '12px', fontSize: '12px', color: '#666' }}>
                <strong>内容长度:</strong> {currentSvg?.content?.length || 0} 字符
              </div>
              <div style={{ marginBottom: '12px', fontSize: '12px', color: '#666' }}>
                <strong>包含SVG标签:</strong> {currentSvg?.content?.includes('<svg') ? '✅' : '❌'}
              </div>
              <div style={{ marginBottom: '16px' }}>
                <strong style={{ fontSize: '12px' }}>SVG原始内容:</strong>
                <textarea 
                  value={currentSvg?.content || '无内容'} 
                  readOnly 
                  style={{ 
                    width: '100%', 
                    height: '300px', 
                    fontSize: '11px', 
                    fontFamily: 'monospace',
                    marginTop: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    padding: '8px'
                  }} 
                />
              </div>
              <div>
                <strong style={{ fontSize: '12px' }}>渲染预览:</strong>
                <div 
                  style={{ 
                    marginTop: '8px',
                    minHeight: '200px',
                    border: '2px solid #e74c3c',
                    borderRadius: '4px',
                    padding: '8px',
                    backgroundColor: '#fef5f5'
                  }}
                  dangerouslySetInnerHTML={{ __html: currentSvg?.content || '<div>无内容</div>' }}
                />
              </div>
            </div>
          ) : currentSvg?.content ? (
            // 正常预览模式
            <div
              style={{
                width: '100%',
                height: '75vh',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                overflow: 'hidden',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                borderRadius: '8px',
                backgroundColor: 'white',
                position: 'relative'
              }}
            >
              {/* 重新生成加载遮罩 */}
              {regeneratingSvgIndex === currentIndex && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  zIndex: 1000,
                  borderRadius: '8px'
                }}>
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '16px'
                  }}>
                    <Spin size="large" />
                    <div style={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      color: '#1890ff'
                    }}>
                      正在重新生成第 {currentIndex + 1} 页...
                    </div>
                    <div style={{
                      fontSize: '13px',
                      color: '#666',
                      textAlign: 'center',
                      maxWidth: '300px'
                    }}>
                      使用原始设计规范重新生成，保持内容和样式一致性
                    </div>
                  </div>
                </div>
              )}
              {/* 使用iframe来隔离SVG渲染 */}
              <iframe
                srcDoc={`
                  <!DOCTYPE html>
                  <html>
                  <head>
                    <meta charset="utf-8">
                    <style>
                      body { 
                        margin: 0; 
                        padding: 20px; 
                        display: flex; 
                        justify-content: center; 
                        align-items: center; 
                        min-height: calc(100vh - 40px);
                        background: white;
                      }
                      svg {
                        max-width: 100%;
                        max-height: 100%;
                        width: 100%;
                        height: auto;
                        display: block;
                        object-fit: contain;
                      }
                    </style>
                  </head>
                  <body>
                    ${currentSvg.content}
                  </body>
                  </html>
                `}
                style={{
                  width: '95%',
                  height: '95%',
                  border: 'none',
                  borderRadius: '4px'
                }}
                title={`SVG Preview - ${currentSvg.title}`}
              />
            </div>
          ) : (
            <div style={{
              padding: '40px',
              textAlign: 'center',
              color: '#999',
              fontSize: '16px'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📄</div>
              <div>SVG内容加载中...</div>
              <div style={{ fontSize: '14px', marginTop: '8px' }}>
                页面 {currentIndex + 1} / {svgContent.length}
              </div>
              <div style={{ fontSize: '12px', marginTop: '12px', color: '#ff6b6b' }}>
                如果一直显示此消息，请点击"调试模式"查看详细信息
              </div>
            </div>
          )}
        </div>
      </Modal>
    );
  };

  return {
    ConfigPanel,
    OutlineEditor,
    ResultDisplay,
    SVGPreviewModal
  };
};

export default PPTContent; 