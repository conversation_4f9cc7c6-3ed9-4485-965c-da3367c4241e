<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      <![CDATA[
      /* Background and Fill Colors */
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #1E3A8A; }
      .secondary-color { fill: #1E40AF; }
      .accent-color { fill: #3B82F6; }
      .card-background { fill: #FFFFFF; }
      .container-background { fill: #E0F2FE; }

      /* Text Colors */
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }

      /* Gradients */
      /* primary_gradient: linear-gradient(135deg, #1E3A8A, #1E40AF) */
      .gradient-primary-bg { fill: url(#primaryGradient); }
      /* accent_gradient: linear-gradient(45deg, #3B82F6, #1E3A8A) */
      .gradient-accent-bg { fill: url(#accentGradient); }
      /* text_gradient: linear-gradient(135deg, #1E3A8A, #1E40AF) - not directly used as fill but defined */
      .text-gradient-fill { fill: url(#textGradient); }
      /* Custom highlight gradient for decorative elements (accent color with transparency) */
      .highlight-gradient-fill { fill: url(#highlightGradient); }

      /* Fonts */
      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
      .font-accent { font-family: "Times New Roman", serif; }

      /* Font Sizes and Weights (from design norms) */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* semibold */
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; } /* medium */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Specific styles for this template's text elements */
      .chapter-title-zh { font-size: 72px; font-weight: 700; } /* hero_title, bold */
      .chapter-title-en { font-size: 36px; font-weight: 300; } /* section_title, light */
      .page-info-text { font-size: 28px; font-weight: 500; } /* content_title, medium */

      /* Decorative elements styling */
      .decorative-shape-base { fill: #1E3A8A; opacity: 0.08; } /* Primary color, low opacity */
      .decorative-shape-accent { fill: #3B82F6; opacity: 0.08; } /* Accent color, low opacity */
      .decorative-line-stroke { stroke: #3B82F6; stroke-width: 2; opacity: 0.4; } /* Accent color, semi-transparent */
      .divider-line { stroke: #BAE6FD; stroke-width: 1; } /* Card border color for subtle lines */
      ]]>
    </style>

    <!-- Primary Gradient: linear-gradient(135deg, #1E3A8A, #1E40AF) -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>

    <!-- Accent Gradient: linear-gradient(45deg, #3B82F6, #1E3A8A) -->
    <linearGradient id="accentGradient" x1="0%" y1="100%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E3A8A;stop-opacity:1" />
    </linearGradient>

    <!-- Text Gradient: linear-gradient(135deg, #1E3A8A, #1E40AF) -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>

    <!-- Highlight Gradient (accent color with self-transparency) -->
    <linearGradient id="highlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.1" />
    </linearGradient>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />

  <!-- Decorative Background Elements (Subtle, large shapes) -->
  <!-- Top-left large abstract rectangle -->
  <rect x="-100" y="-50" width="600" height="300" class="decorative-shape-base" transform="rotate(-15 200 100)" />
  <!-- Bottom-right large abstract circle -->
  <circle cx="1800" cy="1000" r="250" class="decorative-shape-accent" />
  <!-- Central large shape with primary gradient, highly transparent -->
  <path d="M960 100 L1600 540 L960 980 L320 540 Z" class="gradient-primary-bg" style="opacity: 0.05;" />
  <!-- Central large shape with accent gradient, highly transparent, slightly offset -->
  <path d="M960 120 L1580 540 L960 960 L340 540 Z" class="gradient-accent-bg" style="opacity: 0.05;" />

  <!-- Main Content Area (Centralized for visual impact) -->
  <g id="chapter-section">
    <!-- Horizontal Divider Line above title -->
    <line x1="480" y1="400" x2="1440" y2="400" class="divider-line" />

    <!-- Chapter Title (Chinese - large and bold) -->
    <text x="960" y="500" text-anchor="middle" class="font-primary chapter-title-zh text-primary-color">
      {title}
    </text>

    <!-- Subtitle (English - smaller and lighter) -->
    <text x="960" y="560" text-anchor="middle" class="font-primary chapter-title-en text-secondary-color">
      {subtitle}
    </text>

    <!-- Horizontal Divider Line below title -->
    <line x1="480" y1="640" x2="1440" y2="640" class="divider-line" />
  </g>

  <!-- Page Information (Bottom-right) -->
  <text x="1840" y="1020" text-anchor="end" class="font-primary page-info-text text-light-color">
    {date} 和#38; {author} | 3/10
  </text>

  <!-- Logo Placeholder (Top-left) -->
  <image x="80" y="60" width="160" height="60" href="{logo_url}" />

  <!-- Additional Decorative Elements (Outline graphics for tech/data feel) -->
  <!-- Top-right abstract pattern -->
  <path d="M1700 200 L1800 100 L1700 0 L1600 100 Z" class="decorative-line-stroke" fill="none" />
  <circle cx="1700" cy="100" r="10" class="accent-color" />

  <!-- Bottom-left abstract pattern -->
  <path d="M220 880 L120 980 L220 1080 L320 980 Z" class="decorative-line-stroke" fill="none" />
  <circle cx="220" cy="980" r="10" class="accent-color" />

  <!-- Vertical lines for structure -->
  <line x1="80" y1="180" x2="80" y2="900" class="divider-line" />
  <line x1="1840" y1="180" x2="1840" y2="900" class="divider-line" />

</svg>