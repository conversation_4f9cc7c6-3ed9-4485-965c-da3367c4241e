#!/usr/bin/env python3
"""
AI Chat Desktop 打包脚本
用于跨平台打包的统一入口
"""

import os
import sys
import shutil
import logging
import subprocess
import argparse  # 添加命令行参数支持
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', encoding='utf-8')
logger = logging.getLogger("BuildScript")

# 获取项目根目录
ROOT_DIR = Path(__file__).parent.absolute()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="AI Chat Desktop 打包脚本")
    parser.add_argument('--exclude-module', dest='exclude_modules', action='append', default=[],
                        help='排除指定的Python模块，可多次使用此选项')
    parser.add_argument('--exclude-file', dest='exclude_files', action='append', default=[],
                        help='排除指定的文件，可多次使用此选项')
    return parser.parse_args()

def check_environment():
    """检查环境依赖"""
    logger.info("检查环境依赖...")
    
    # 检查并移除过时的pathlib包（与PyInstaller不兼容）
    try:
        import pkg_resources
        try:
            pkg_resources.get_distribution('pathlib')
            logger.info("发现过时的pathlib包，正在移除...")
            subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "pathlib", "-y"], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            logger.info("已成功移除过时的pathlib包")
        except pkg_resources.DistributionNotFound:
            pass  # pathlib包不存在，无需处理
    except Exception as e:
        logger.warning(f"检查pathlib包时出错: {e}")
    
    # 检查并安装DeerFlow依赖（关键修复）
    logger.info("检查DeerFlow依赖...")
    deer_flow_requirements = ROOT_DIR / "requirements_deer_flow.txt"
    if deer_flow_requirements.exists():
        logger.info("找到DeerFlow依赖文件，检查是否需要安装...")
        
        # 检查关键依赖是否已安装
        critical_deps = ['langchain', 'langchain_openai', 'langchain_core', 'langgraph']
        missing_deps = []
        
        for dep in critical_deps:
            try:
                __import__(dep)
                logger.info(f"✅ {dep} 已安装")
            except ImportError:
                missing_deps.append(dep)
                logger.warning(f"❌ {dep} 缺失")
        
        if missing_deps:
            logger.info(f"发现缺失的DeerFlow依赖: {missing_deps}")
            logger.info("正在安装DeerFlow依赖...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "-r", str(deer_flow_requirements)
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                logger.info("✅ DeerFlow依赖安装完成")
                
                # 再次验证关键依赖
                still_missing = []
                for dep in missing_deps:
                    try:
                        __import__(dep)
                        logger.info(f"✅ {dep} 安装验证成功")
                    except ImportError:
                        still_missing.append(dep)
                        logger.error(f"❌ {dep} 安装验证失败")
                
                if still_missing:
                    logger.error(f"以下依赖安装失败: {still_missing}")
                    logger.error("这可能影响DeerFlow插件的打包")
                    return False
                    
            except subprocess.CalledProcessError as e:
                logger.error(f"安装DeerFlow依赖失败: {e}")
                logger.error("这可能影响DeerFlow插件的打包")
                return False
        else:
            logger.info("✅ 所有DeerFlow依赖都已安装")
    else:
        logger.warning(f"未找到DeerFlow依赖文件: {deer_flow_requirements}")
        logger.warning("DeerFlow插件可能无法正常工作")
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
        logger.info(f"PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        logger.error("未安装PyInstaller，请先运行: pip install -r requirements.txt")
        return False
    
    # 检查前端构建目录是否存在
    build_dir = ROOT_DIR / "view" / "build"
    if not build_dir.exists():
        logger.warning("前端构建目录不存在，将执行前端构建...")
        if not build_frontend():
            logger.error("前端构建失败")
            return False
    
    # 检查资源目录
    resources_dir = ROOT_DIR / "resources"
    if not resources_dir.exists():
        logger.info("创建资源目录...")
        resources_dir.mkdir(exist_ok=True)
    logger.info(f"resources_dir: {resources_dir}")
    # 检查图标文件 - 统一使用 app_icon.png
    icon_files = {
        "all_platforms": resources_dir / "app_icon.png"
    }
    
    missing_icons = [k for k, v in icon_files.items() if not v.exists()]
    logger.info(f"missing_icons: {missing_icons}")
    if missing_icons:
        logger.warning(f"图标文件不存在: {', '.join(missing_icons)}")
        logger.warning("将使用默认图标或无图标进行打包")
    else:
        logger.info("图标文件检查通过: app_icon.png")
    # === [自动补 PATH，兼容打包后环境] ===
    if sys.platform.startswith('darwin'):
        if '/usr/local/bin' not in os.environ.get('PATH', ''):
            os.environ['PATH'] = '/usr/local/bin:' + os.environ.get('PATH', '')
    logger.info(f"os.environ['PATH']: {os.environ['PATH']}")
    return True

def build_frontend():
    """构建前端应用"""
    logger.info("开始构建前端...")
    
    try:
        # 检查node是否安装
        subprocess.check_call(["node", "--version"], stdout=subprocess.DEVNULL)
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("未安装Node.js，无法构建前端")
        return False
    
    try:
        # 进入前端目录
        os.chdir(ROOT_DIR / "view")
        
        # 安装依赖
        logger.info("安装前端依赖...")
        subprocess.check_call(["npm", "install"], stdout=subprocess.PIPE)
        
        # 构建前端
        logger.info("构建前端...")
        subprocess.check_call(["npm", "run", "build"], stdout=subprocess.PIPE)
        
        # 返回根目录
        os.chdir(ROOT_DIR)
        logger.info("前端构建完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"前端构建失败: {e}")
        # 返回根目录
        os.chdir(ROOT_DIR)
        return False

def clean_build_files():
    """清理旧的构建文件"""
    logger.info("清理旧的构建文件...")
    
    # 清理目录
    build_dirs = [
        ROOT_DIR / "build",
        ROOT_DIR / "dist"
    ]
    
    for dir_path in build_dirs:
        if dir_path.exists():
            logger.info(f"删除目录: {dir_path}")
            shutil.rmtree(dir_path, ignore_errors=True)
    
    # 清理PyInstaller缓存
    spec_file = ROOT_DIR / "build_app.spec"
    if spec_file.exists():
        logger.info(f"使用现有spec文件: {spec_file}")

def run_pyinstaller(args):
    """运行PyInstaller打包"""
    logger.info("开始PyInstaller打包...")
    
    # 构建PyInstaller命令
    cmd = ["pyinstaller", "--clean"]
    
    # 添加要排除的模块
    for module in args.exclude_modules:
        cmd.extend(["--exclude-module", module])
        logger.info(f"将排除模块: {module}")
    
    # 添加spec文件
    cmd.append("build_app.spec")
    
    # 如果有要排除的文件，需要提示用户直接修改spec文件
    if args.exclude_files:
        logger.info("注意: 要排除的文件列表将写入spec文件中的excluded_binaries和excluded_datas列表")
        logger.info("请在运行前检查spec文件，确保排除配置正确")
        
        # 简单地打印出如何修改spec文件的提示
        logger.info("\n您可以修改build_app.spec文件中的以下部分来排除特定文件:")
        logger.info("excluded_binaries = [")
        for file in args.exclude_files:
            logger.info(f"    '{file}',")
        logger.info("]")
    
    # 运行PyInstaller命令
    try:
        logger.info(f"运行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        logger.info("PyInstaller打包完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"PyInstaller打包失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始AI Chat Desktop打包")
    logger.info("=" * 60)
    
    # 解析命令行参数
    args = parse_args()
    
    # 检查环境
    if not check_environment():
        logger.error("环境检查失败，终止打包")
        sys.exit(1)
    
    # 清理旧的构建文件
    clean_build_files()
    
    # 运行PyInstaller打包
    if not run_pyinstaller(args):
        logger.error("打包失败")
        sys.exit(1)
    
    # 打包完成
    logger.info("=" * 60)
    logger.info("打包完成")
    
    # 输出打包结果路径
    dist_dir = ROOT_DIR / "dist"
    if sys.platform.startswith('win'):
        app_path = dist_dir / "Jimu Chat"
        logger.info(f"应用路径: {app_path}")
        logger.info("可执行文件: Jimu Chat.exe")
    elif sys.platform.startswith('darwin'):
        app_path = dist_dir / "Jimu Chat.app"
        logger.info(f"应用路径: {app_path}")
    else:
        app_path = dist_dir / "Jimu Chat"
        logger.info(f"应用路径: {app_path}")
        logger.info("可执行文件: Jimu Chat")
    
    logger.info("=" * 60)
    
    # macOS特定: 自动修复应用包启动问题
    if sys.platform.startswith('darwin') and app_path.exists():
        logger.info("正在自动修复macOS应用包...")
        try:
            fix_script = ROOT_DIR / "fix_app_bundle.sh"
            if fix_script.exists():
                subprocess.check_call([str(fix_script)], cwd=str(ROOT_DIR))
                logger.info("✅ macOS应用包修复完成")
            else:
                logger.warning("修复脚本不存在，请手动运行: ./fix_app_bundle.sh")
        except subprocess.CalledProcessError as e:
            logger.error(f"自动修复失败: {e}")
            logger.info("请手动运行: ./fix_app_bundle.sh")

if __name__ == "__main__":
    main()