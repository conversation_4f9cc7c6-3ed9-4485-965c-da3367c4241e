"""
🔥 上下文压缩引擎 - LangGraph 增强系统核心组件

解决关键痛点：
- 上下文无限累积导致性能下降
- AI 响应准确性降低
- 记忆混乱和幻觉问题
- 速度变慢

特性：
- 四级自适应压缩策略
- 智能信息保留和过滤
- 基于内容重要性的压缩
- 与现有 AIManager 完美集成
"""

import logging
import asyncio
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from enum import Enum

# 🔥 复用现有系统
from ..utils import get_ai_manager_bridge
from ..config.config_manager import get_config_manager

logger = logging.getLogger(__name__)


class CompressionLevel(Enum):
    """压缩等级枚举"""
    NONE = 0      # 无压缩 (0-4K tokens)
    LIGHT = 1     # 轻度压缩 (4K-8K tokens) 
    MEDIUM = 2    # 中度压缩 (8K-16K tokens)
    HEAVY = 3     # 重度压缩 (16K+ tokens)


class ContextCompressionEngine:
    """
    🔥 四级上下文压缩引擎
    
    核心功能：
    - 自适应压缩策略选择
    - 重要信息智能保留
    - 基于 AIManager 的摘要生成
    - 上下文质量监控
    """
    
    def __init__(self, ai_manager = None):
        """初始化压缩引擎"""
        if ai_manager is None:
            # 使用桥接器获取 AIManager
            ai_bridge = get_ai_manager_bridge()
            self.ai_manager = ai_bridge.ai_manager
        else:
            self.ai_manager = ai_manager
        
        # 从配置加载阈值
        config_manager = get_config_manager()
        context_config = config_manager.get_context_config()
        
        # 🔥 优化：基于实际使用调整压缩阈值
        self.compression_thresholds = context_config.get('compression_levels', {
            "light": 6000,   # 提高轻度压缩阈值，减少不必要压缩
            "medium": 12000, # 优化中度压缩触发点
            "heavy": 20000   # 调整重度压缩阈值
        })
        
        # 🔥 优化：分类的重要信息模式 + 协作信息保护
        self.important_patterns = {
            # 通用重要标识 + 协作信息
            'universal': [
                r'重要[：:]\s*(.+)',       # 明确标记为重要的信息
                r'关键[：:]\s*(.+)',       # 关键信息
                r'注意[：:]\s*(.+)',       # 注意事项
                r'结论[：:]\s*(.+)',       # 结论
                r'总结[：:]\s*(.+)',       # 总结
                r'核心[：:]\s*(.+)',       # 核心内容
                r'要点[：:]\s*(.+)',       # 要点
                r'关键词[：:]\s*(.+)',     # 关键词
                r'[\d\.]+\.\s*(.+)',       # 编号列表项
                r'[•·]\s*(.+)',            # 无序列表项
                r'\*\*(.+?)\*\*',          # 粗体文本
                r'__(.+?)__',              # 下划线强调
                r'【(.+?)】',               # 中文方括号强调
                # 🔥 新增：协作关键信息模式
                r'团队规则[：:]\s*(.+)',    # 团队协作规则
                r'工作流状态[：:]\s*(.+)',   # 工作流状态
                r'智能体传递[：:]\s*(.+)',   # 智能体间传递信息
                r'协作要求[：:]\s*(.+)',    # 协作要求
                r'下一步[：:]\s*(.+)',      # 下一步行动
                r'依赖关系[：:]\s*(.+)',    # 依赖关系
                r'状态更新[：:]\s*(.+)',    # 状态更新
                r'执行结果[：:]\s*(.+)',    # 执行结果
                r'工具输出[：:]\s*(.+)',    # 工具输出
                r'Agent\s+\w+\s*[：:]\s*(.+)',  # Agent通信
            ],
            
            # 研究型智能体关注的信息
            'research': [
                r'研究发现[：:]\s*(.+)',
                r'实验结果[：:]\s*(.+)',
                r'数据显示[：:]\s*(.+)',
                r'分析表明[：:]\s*(.+)',
                r'调研结果[：:]\s*(.+)',
                r'文献资料[：:]\s*(.+)',
                r'统计数据[：:]\s*(.+)',
                r'研究方法[：:]\s*(.+)',
                r'样本量[：:]\s*(.+)',
                r'置信度[：:]\s*(.+)',
                r'correlation|regression|significance',  # 统计术语
                r'\d+%|\d+比\d+|增长\d+',  # 百分比和比例
            ],
            
            # 技术/开发型智能体关注的信息
            'technical': [
                r'技术方案[：:]\s*(.+)',
                r'架构设计[：:]\s*(.+)',
                r'代码实现[：:]\s*(.+)',
                r'系统配置[：:]\s*(.+)',
                r'性能指标[：:]\s*(.+)',
                r'API接口[：:]\s*(.+)',
                r'数据库[：:]\s*(.+)',
                r'部署方案[：:]\s*(.+)',
                r'算法|framework|library|version',  # 技术术语
                r'class|function|method|interface',   # 代码术语
                r'CPU|GPU|内存|存储|带宽',  # 硬件资源
            ],
            
            # 业务/分析型智能体关注的信息
            'business': [
                r'商业模式[：:]\s*(.+)',
                r'市场分析[：:]\s*(.+)',
                r'用户需求[：:]\s*(.+)',
                r'竞品分析[：:]\s*(.+)',
                r'成本效益[：:]\s*(.+)',
                r'ROI[：:]\s*(.+)',
                r'KPI[：:]\s*(.+)',
                r'营收|利润|成本|预算',  # 财务术语
                r'目标用户|用户画像|市场份额',  # 市场术语
                r'增长率|转化率|留存率',  # 业务指标
            ],
            
            # 设计型智能体关注的信息
            'design': [
                r'设计理念[：:]\s*(.+)',
                r'用户体验[：:]\s*(.+)',
                r'界面设计[：:]\s*(.+)',
                r'交互方式[：:]\s*(.+)',
                r'视觉效果[：:]\s*(.+)',
                r'用户反馈[：:]\s*(.+)',
                r'UI|UX|前端|界面|交互',  # 设计术语
                r'颜色|字体|布局|样式',  # 视觉元素
                r'响应式|适配|兼容',  # 技术适配
            ],
            
            # 写作型智能体关注的信息
            'writing': [
                r'内容结构[：:]\s*(.+)',
                r'写作风格[：:]\s*(.+)',
                r'目标受众[：:]\s*(.+)',
                r'文案要求[：:]\s*(.+)',
                r'SEO关键词[：:]\s*(.+)',
                r'标题|段落|章节|目录',  # 结构术语
                r'语调|风格|文体|格式',  # 写作风格
                r'读者|受众|传播|发布',  # 传播相关
            ]
        }
        
        logger.info("🔥 上下文压缩引擎初始化完成")
    
    async def _summarize_single_tool_output(self, tool_name: str, output_str: str) -> str:
        """[异步工具] 对单个大型工具输出进行摘要"""
        logger.info(f"🔧 开始摘要工具 '{tool_name}' 输出 (原始大小: {len(output_str)} 字符)")
        try:
            summary_prompt = f"""
请对以下来自工具 '{tool_name}' 的输出内容进行智能摘要，保留最核心的信息和关键数据：

--- 工具输出开始 ---
{output_str}
--- 工具输出结束 ---

🔥 工具输出摘要要求：
- 【绝对保留】：关键数据、重要结果、错误信息的根本原因
- 【智能体协作】：保留对后续智能体有价值的所有信息
- 【数据完整性】：如果是数据输出，保留完整的关键指标和数值
- 【错误诊断】：如果是错误信息，详细说明错误原因和影响范围
- 【可操作性】：确保摘要信息足够支持后续智能体的决策和行动
- 【压缩度】：摘要应简洁但完整，通常不超过10句话，但关键信息不能丢失
- 【上下文价值】：优先保留对整个工作流有价值的信息，而非仅对当前任务有用的信息
"""
            summary_messages = [
                {'role': 'system', 'content': 'You are an expert AI tool output analyzer specialized in multi-agent workflows. Your summaries must preserve all critical information needed for seamless agent collaboration. Never sacrifice important data for brevity. Focus on actionable insights and collaborative value.'},
                {'role': 'user', 'content': summary_prompt}
            ]
            response = await self.ai_manager.chat_completion(
                messages=summary_messages,
                model='gemini-2.5-flash-preview-05-20',
                handle_tools=False,
                role_id="langgraph_agent"
            )
            summary_content = response.get('content', '[摘要生成失败]')
            logger.info(f"✅ 工具 '{tool_name}' 摘要完成 (原始: {len(output_str)} -> 摘要: {len(summary_content)} 字符)")
            return summary_content
        except Exception as e:
            logger.error(f"❌ 工具输出摘要失败: {e}")
            return "[工具输出过长，摘要失败]"

    async def _format_tool_history_for_summary(self, tool_calls_history: List[Dict]) -> str:
        """
        🔥 [优化] 高效处理工具调用历史格式化
        
        优化策略：
        - 减少不必要的Future创建
        - 批量处理相似大小的工具输出
        - 预筛选避免无效处理
        """
        if not tool_calls_history:
            return ""

        TOOL_OUTPUT_SUMMARY_THRESHOLD = 800  # 🔥 优化：提高阈值，减少不必要的摘要
        
        # 🔥 优化：预处理，分离大小工具输出
        large_outputs = []
        small_outputs = []
        
        for i, tool_call in enumerate(tool_calls_history):
            tool_output = tool_call.get("tool_output") or tool_call.get("result")
            try:
                if isinstance(tool_output, (dict, list)):
                    output_str = json.dumps(tool_output, ensure_ascii=False)
                else:
                    output_str = str(tool_output)
            except Exception:
                output_str = "[工具输出无法序列化]"

            tool_name = tool_call.get("tool_name") or tool_call.get("name", "unknown_tool")
            
            if len(output_str) > TOOL_OUTPUT_SUMMARY_THRESHOLD:
                large_outputs.append((i, tool_name, output_str))
            else:
                small_outputs.append((i, tool_name, output_str))

        # 🔥 优化：只对大型输出创建异步任务
        results = {}
        if large_outputs:
            tasks = [
                self._summarize_single_tool_output(tool_name, output_str) 
                for _, tool_name, output_str in large_outputs
            ]
            summarized_results = await asyncio.gather(*tasks)
            
            for (idx, tool_name, _), summary in zip(large_outputs, summarized_results):
                results[idx] = (tool_name, summary)

        # 直接处理小型输出
        for idx, tool_name, output_str in small_outputs:
            results[idx] = (tool_name, output_str)

        # 🔥 优化：按原始顺序重组输出
        formatted_outputs = []
        for i, tool_call in enumerate(tool_calls_history):
            if i in results:
                tool_name, output_content = results[i]
                formatted_outputs.append(f"\n--- 工具输出: {tool_name} ---\n{output_content}\n--- 结束输出: {tool_name} ---")
        
        return "".join(formatted_outputs)

    async def _build_content_for_summarization(self, messages: List[Dict]) -> str:
        """
        🔥 [异步] 构建用于摘要的完整内容字符串，包含处理过的工具历史。
        """
        content_to_summarize_parts = []
        for msg in messages:
            content = msg.get('content', '')
            role = msg.get('role', 'unknown')
            
            tool_history_str = ""
            if 'tool_calls_history' in msg and msg['tool_calls_history']:
                # 调用异步格式化函数
                tool_history_str = await self._format_tool_history_for_summary(msg['tool_calls_history'])
            
            full_content = f"[{role}]: {content}"
            if tool_history_str:
                full_content += f"\\n{tool_history_str}"
            content_to_summarize_parts.append(full_content)

        return "\\n\\n".join(content_to_summarize_parts)
    
    async def adaptive_compression(self, messages: List[Dict], strategy: str = 'adaptive', 
                                  user_topic: str = "", next_agent_info: Dict = None) -> Tuple[List[Dict], Dict]:
        """
        🔥 优化版：自适应上下文压缩主方法
        
        Args:
            messages: 消息列表
            strategy: 压缩策略 ('adaptive', 'research_focused', 'writing_focused', 'data_focused')
            user_topic: 用户主题，用于针对性压缩
            next_agent_info: 下一个智能体信息，用于定向优化
            
        Returns:
            Tuple[List[Dict], Dict]: (压缩后的消息, 压缩元数据)
        """
        try:
            start_time = datetime.now()
            logger.info(f"🔧 开始自适应压缩，策略: {strategy}")
            
            # 🔥 新增：设置当前压缩上下文信息，供内部方法使用
            self._current_user_topic = user_topic
            self._current_next_agent_info = next_agent_info
            
            # 记录优化信息
            if user_topic:
                logger.info(f"🎯 用户主题: {user_topic}")
            if next_agent_info and next_agent_info.get('role_name'):
                logger.info(f"👤 下一个智能体: {next_agent_info['role_name']}")
            
            # 1. 估算当前上下文大小
            context_size = self._estimate_context_size(messages)
            
            # 2. 确定压缩等级
            compression_level = self._determine_compression_level(context_size)
            
            # 3. 执行对应等级的压缩
            if compression_level == CompressionLevel.NONE:
                compressed_messages = messages
                logger.info("✅ 上下文大小合适，无需压缩")
            else:
                compressed_messages = await self._compress_by_level(
                    messages, compression_level, strategy
                )
            
            # 4. 生成压缩元数据
            compression_metadata = {
                'original_size': context_size,
                'compressed_size': self._estimate_context_size(compressed_messages),
                'compression_level': compression_level.name,
                'strategy': strategy,
                'compression_ratio': len(compressed_messages) / len(messages) if messages else 1.0,
                'processing_time': (datetime.now() - start_time).total_seconds(),
                'messages_reduced': len(messages) - len(compressed_messages)
            }
            
            logger.info(f"✅ 压缩完成: {len(messages)} -> {len(compressed_messages)} 条消息")
            return compressed_messages, compression_metadata
            
        except Exception as e:
            logger.error(f"❌ 上下文压缩失败: {str(e)}")
            return messages, {'error': str(e)}
    
    def _estimate_context_size(self, messages: List[Dict]) -> int:
        """估算上下文大小（字符数），只计算非系统消息，避免系统消息影响压缩判断"""
        total_chars = 0
        for msg in messages:
            # 跳过系统消息，只计算非系统消息的大小
            if msg.get('role') == 'system':
                continue
                
            # 计算 content 字段的长度
            content = msg.get('content', '')
            if isinstance(content, str):
                total_chars += len(content)
            elif isinstance(content, dict):
                total_chars += len(str(content))
            
            # 计算 tool_calls_history 的长度
            tool_history = msg.get('tool_calls_history', [])
            if tool_history:
                total_chars += len(json.dumps(tool_history, ensure_ascii=False))

        return total_chars
    
    def _determine_compression_level(self, context_size: int) -> CompressionLevel:
        """根据上下文大小确定压缩等级"""
        # 🔥 修复：正确映射字符串到枚举类型
        level_mapping = {
            "light": CompressionLevel.LIGHT,
            "medium": CompressionLevel.MEDIUM,
            "heavy": CompressionLevel.HEAVY
        }
        
        for level_name, threshold in self.compression_thresholds.items():
            if context_size <= threshold:
                return level_mapping.get(level_name, CompressionLevel.HEAVY)
        return CompressionLevel.HEAVY
    
    async def _compress_by_level(
        self, 
        messages: List[Dict], 
        level: CompressionLevel, 
        strategy: str
    ) -> List[Dict]:
        """根据压缩等级执行压缩"""
        
        if level == CompressionLevel.LIGHT:
            return await self._light_compression(messages, strategy)
        elif level == CompressionLevel.MEDIUM:
            return await self._medium_compression(messages, strategy)
        elif level == CompressionLevel.HEAVY:
            return await self._heavy_compression(messages, strategy)
        else:
            return messages
    
    async def _light_compression(self, messages: List[Dict], strategy: str) -> List[Dict]:
        """
        轻度压缩 - 去重和基础清理
        保留所有消息，只清理重复和冗余信息
        """
        logger.info("🔧 执行轻度压缩")
        
        # 保留系统消息和最近的重要消息
        compressed = []
        seen_content = set()
        
        for msg in messages:
            content = msg.get('content', '')
            
            # 总是保留系统消息
            if msg.get('role') == 'system':
                compressed.append(msg)
                continue
            
            # 去重：避免完全相同的消息
            content_hash = hash(content[:100])  # 使用前100字符作为去重依据
            if content_hash in seen_content:
                continue
            
            # 清理空白和格式
            cleaned_content = self._clean_message_content(content)
            if cleaned_content.strip():
                cleaned_msg = msg.copy()
                cleaned_msg['content'] = cleaned_content
                compressed.append(cleaned_msg)
                seen_content.add(content_hash)
        
        # 🔥 新增：对所有保留的消息进行工具输出压缩
        return await self._compress_tool_outputs_in_messages(compressed)
    
    async def _medium_compression(self, messages: List[Dict], strategy: str) -> List[Dict]:
        """
        中度压缩 - 智能摘要化非关键对话
        保留系统消息 + 重要消息 + 最近消息的摘要
        """
        logger.info("🔧 执行中度压缩")
        
        # 🔥 修复：更好地分离不同类型的消息
        system_messages = []
        non_system_messages = []
        
        for msg in messages:
            if msg.get('role') == 'system':
                system_messages.append(msg)
            else:
                non_system_messages.append(msg)
        
        # 🔥 优化：传递下一个智能体信息以进行智能过滤
        important_messages = self._extract_important_messages(
            non_system_messages, 
            next_agent_info=getattr(self, '_current_next_agent_info', None)
        )
        recent_messages = non_system_messages[-3:]  # 保留最近3条非系统消息
        
        # 🔥 修复：获取需要摘要的消息（只从非系统消息中筛选）
        messages_to_summarize = []
        exclude_messages = set()
        
        # 排除重要消息
        for imp_msg in important_messages:
            exclude_messages.add(id(imp_msg))
        
        # 排除最近3条消息
        for msg in recent_messages:
            exclude_messages.add(id(msg))
        
        # 收集需要摘要的非系统消息
        for msg in non_system_messages:
            if id(msg) not in exclude_messages:
                messages_to_summarize.append(msg)
        
        # 生成摘要
        summary_message = None
        if messages_to_summarize:
            # 🔥 优化：传递用户主题和下一个智能体信息
            summary_content = await self._generate_summary(
                messages_to_summarize, 
                strategy,
                user_topic=getattr(self, '_current_user_topic', ''),
                next_agent_info=getattr(self, '_current_next_agent_info', None)
            )
            summary_message = {
                'role': 'assistant',
                'content': f"📋 对话摘要：{summary_content}",
                'metadata': {
                    'type': 'compression_summary',
                    'original_messages_count': len(messages_to_summarize),
                    'compression_level': 'medium'
                }
            }
        
        # 🔥 修复：重新组合消息，确保正确的顺序
        compressed = []
        
        # 1. 首先添加所有系统消息
        compressed.extend(system_messages)
        
        # 2. 添加重要消息
        compressed.extend(important_messages)
        
        # 3. 添加摘要（如果有）
        if summary_message:
            compressed.append(summary_message)
        
        # 4. 最后添加最近消息
        compressed.extend(recent_messages)
        
        # 🔥 新增：对所有保留的消息进行工具输出压缩
        return await self._compress_tool_outputs_in_messages(compressed)
    
    async def _heavy_compression(self, messages: List[Dict], strategy: str) -> List[Dict]:
        """
        重度压缩 - 保留核心+最新信息
        只保留系统消息 + 核心摘要 + 最新1-2条消息
        """
        logger.info("🔧 执行重度压缩")
        
        # 🔥 修复：更严格地保留系统消息
        system_messages = []
        non_system_messages = []
        
        for msg in messages:
            if msg.get('role') == 'system':
                system_messages.append(msg)
            else:
                non_system_messages.append(msg)
        
        # 保留最新的1-2条非系统消息
        recent_messages = non_system_messages[-2:] if len(non_system_messages) >= 2 else non_system_messages
        
        # 对需要压缩的消息生成摘要（排除最新消息）
        messages_to_summarize = non_system_messages[:-2] if len(non_system_messages) > 2 else []
        
        # 生成高度压缩的摘要
        core_summary = None
        if messages_to_summarize:
            # 🔥 优化：传递用户主题和下一个智能体信息
            summary_content = await self._generate_core_summary(
                messages_to_summarize, 
                strategy,
                user_topic=getattr(self, '_current_user_topic', ''),
                next_agent_info=getattr(self, '_current_next_agent_info', None)
            )
            
            core_summary = {
                'role': 'assistant',
                'content': f"📋 [压缩摘要] {summary_content}",
                'metadata': {
                    'type': 'core_compression_summary',
                    'original_messages_count': len(messages_to_summarize),
                    'compression_level': 'heavy',
                    'is_summary': True
                }
            }
        
        # 🔥 修复：严格按顺序重新组合，确保系统消息在最前面
        compressed = []
        
        # 1. 首先添加所有系统消息
        compressed.extend(system_messages)
        
        # 2. 添加摘要（如果有）
        if core_summary:
            compressed.append(core_summary)
        
        # 3. 最后添加最新消息
        compressed.extend(recent_messages)
        
        # 🔥 新增：对所有保留的消息进行工具输出压缩
        final_compressed = await self._compress_tool_outputs_in_messages(compressed)
        
        # 🔥 修复：将压缩效果统计移到工具输出压缩之后
        # 计算压缩效果（分别计算系统消息和非系统消息）
        original_total_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in messages
        )
        original_non_system_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in messages if msg.get('role') != 'system'
        )
        
        compressed_total_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in final_compressed
        )
        compressed_non_system_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in final_compressed if msg.get('role') != 'system'
        )
        
        # 计算非系统消息的压缩率（这是我们关注的重点）
        non_system_reduction = ((original_non_system_size - compressed_non_system_size) / original_non_system_size * 100) if original_non_system_size > 0 else 0
        total_reduction = ((original_total_size - compressed_total_size) / original_total_size * 100) if original_total_size > 0 else 0
        
        logger.info(f"📉 重度压缩效果:")
        logger.info(f"   总体: {original_total_size} -> {compressed_total_size} 字符 ({total_reduction:.1f}%)")
        logger.info(f"   非系统内容: {original_non_system_size} -> {compressed_non_system_size} 字符 ({non_system_reduction:.1f}%)")
        logger.info(f"   系统消息: 完整保留 ({len(system_messages)}条)")
        
        return final_compressed
    
    async def _compress_tool_outputs_in_messages(self, messages: List[Dict]) -> List[Dict]:
        """
        🔥 [新增] 遍历消息列表，对其中过大的工具输出进行摘要压缩。
        这是为了确保即使被保留（未被摘要）的消息，其内部的工具输出如果过大，也能被处理。
        """
        if not messages:
            return []
        
        # 并发处理每条消息中的工具输出
        processed_messages = await asyncio.gather(
            *(self._compress_tool_outputs_in_single_message(m) for m in messages)
        )
        return list(processed_messages)

    async def _compress_tool_outputs_in_single_message(self, message: Dict) -> Dict:
        """
        🔥 [新增] 处理单条消息，压缩其内部过大的工具输出。
        """
        TOOL_OUTPUT_SUMMARY_THRESHOLD = 500
        
        if 'tool_calls_history' not in message or not message['tool_calls_history']:
            return message

        original_history = message.get('tool_calls_history', [])
        
        # 分析工具输出
        total_tool_output_size = 0
        large_outputs_count = 0
        
        for i, tool_call in enumerate(original_history):
            # 🔥 修复：支持多种字段名，优先查找tool_output，其次查找result  
            tool_output = tool_call.get('tool_output', '') or tool_call.get('result', '')
            
            # 🔥 修复：使用统一的大小计算逻辑
            if isinstance(tool_output, str):
                output_size = len(tool_output)
            else:
                try:
                    output_str = json.dumps(tool_output, ensure_ascii=False)
                    output_size = len(output_str)
                except:
                    output_size = len(str(tool_output))

            total_tool_output_size += output_size
            
            # 🔥 标记大型输出
            if output_size > TOOL_OUTPUT_SUMMARY_THRESHOLD:
                large_outputs_count += 1
        
        # 🔥 修复：检查是否需要摘要（使用修复后的逻辑）
        needs_summarization = large_outputs_count > 0

        if not needs_summarization:
            return message

        # 为需要摘要的工具输出创建任务
        tasks = []
        indices_to_summarize = []
        
        for i, tool_call in enumerate(original_history):
            # 🔥 修复：支持多种字段名，优先查找tool_output，其次查找result  
            tool_output = tool_call.get('tool_output', '') or tool_call.get('result', '')
            
            # 🔥 修复：使用统一的大小计算逻辑
            if isinstance(tool_output, str):
                output_size = len(tool_output)
                output_str = tool_output
            else:
                try:
                    output_str = json.dumps(tool_output, ensure_ascii=False)
                    output_size = len(output_str)
                except:
                    output_str = str(tool_output)
                    output_size = len(output_str)

            if output_size > TOOL_OUTPUT_SUMMARY_THRESHOLD:
                tool_name = tool_call.get("tool_name") or tool_call.get("name", "unknown_tool")
                tasks.append(self._summarize_single_tool_output(tool_name, output_str))
                indices_to_summarize.append(i)

        if not tasks:
            return message
            
        summarized_outputs = await asyncio.gather(*tasks)

        # 创建带有压缩后工具输出的新消息副本
        new_message = message.copy()
        new_history = [tc.copy() for tc in original_history]

        for i, original_idx in enumerate(indices_to_summarize):
            summary_content = summarized_outputs[i]
            # 🔥 修复：根据原始字段名设置压缩后的内容
            original_tool_call = original_history[original_idx]
            if 'tool_output' in original_tool_call:
                new_history[original_idx]['tool_output'] = summary_content
            elif 'result' in original_tool_call:
                new_history[original_idx]['result'] = summary_content
            else:
                # 默认使用tool_output字段
                new_history[original_idx]['tool_output'] = summary_content
            
            metadata = new_history[original_idx].get('metadata', {})
            metadata['compression_info'] = {
                'status': 'tool_output_summarized',
                'original_output_size': len(str(original_tool_call.get('tool_output', '') or original_tool_call.get('result', '')))
            }
            new_history[original_idx]['metadata'] = metadata

        new_message['tool_calls_history'] = new_history
        return new_message
    
    def _extract_important_messages(self, messages: List[Dict], next_agent_info: Dict = None) -> List[Dict]:
        """
        🔥 优化版：基于下一个智能体类型提取重要消息
        
        Args:
            messages: 消息列表
            next_agent_info: 下一个智能体信息，用于定向过滤
            
        Returns:
            重要消息列表
        """
        
        important = []
        
        # 确定智能体类型和对应的模式
        agent_type = self._determine_agent_type(next_agent_info)
        relevant_patterns = self._get_relevant_patterns(agent_type)
        
        logger.debug(f"🎯 为智能体类型 '{agent_type}' 提取重要信息，使用 {len(relevant_patterns)} 个模式")
        
        for msg in messages:
            content = msg.get('content', '')
            is_important = False
            matched_patterns = []
            
            # 检查是否包含相关的重要信息模式
            for pattern in relevant_patterns:
                try:
                    if re.search(pattern, content, re.IGNORECASE):
                        is_important = True
                        matched_patterns.append(pattern)
                        break
                except re.error:
                    # 忽略无效的正则表达式
                    continue
            
            # 检查消息元数据中的重要性标记
            if msg.get('metadata', {}).get('important', False):
                is_important = True
                matched_patterns.append('metadata_important')
            
            # 🔥 新增：检查用户主题相关性
            user_topic = getattr(self, '_current_user_topic', '')
            if user_topic and user_topic.lower() in content.lower():
                is_important = True
                matched_patterns.append('topic_relevant')
            
            if is_important:
                # 添加匹配信息到消息元数据
                msg_copy = msg.copy()
                if 'metadata' not in msg_copy:
                    msg_copy['metadata'] = {}
                msg_copy['metadata']['importance_reasons'] = matched_patterns
                msg_copy['metadata']['agent_type_relevance'] = agent_type
                important.append(msg_copy)
        
        logger.debug(f"✅ 从 {len(messages)} 条消息中提取了 {len(important)} 条重要消息")
        return important
    
    def _determine_agent_type(self, next_agent_info: Dict = None) -> str:
        """
        🔥 新增：根据智能体信息确定类型
        
        Args:
            next_agent_info: 下一个智能体信息
            
        Returns:
            智能体类型字符串
        """
        if not next_agent_info:
            return 'universal'
        
        role_name = next_agent_info.get('role_name', '').lower()
        expertise = [exp.lower() for exp in next_agent_info.get('expertise', [])]
        
        # 根据角色名称和专业领域判断类型
        type_keywords = {
            'research': ['research', 'analyst', '研究', '分析', '数据科学', 'scientist', '调研', 'data scientist'],
            'technical': ['developer', 'engineer', 'architect', '开发', '工程师', '架构师', '技术', 'full-stack', 'backend', 'frontend'],
            'business': ['manager', 'business', 'marketing', '经理', '商务', '营销', '产品', 'product', 'sales'],
            'design': ['designer', 'ui', 'ux', '设计师', '设计', '美工', '交互', 'visual', 'graphic'],
            'writing': ['writer', 'content', 'editor', '作家', '编辑', '写作', '文案', 'technical writer', 'copywriter']
        }
        
        # 检查角色名称
        for agent_type, keywords in type_keywords.items():
            if any(keyword in role_name for keyword in keywords):
                return agent_type
        
        # 检查专业领域
        for agent_type, keywords in type_keywords.items():
            if any(any(keyword in exp for keyword in keywords) for exp in expertise):
                return agent_type
        
        return 'universal'
    
    def _get_relevant_patterns(self, agent_type: str) -> List[str]:
        """
        🔥 新增：获取与智能体类型相关的模式
        
        Args:
            agent_type: 智能体类型
            
        Returns:
            相关的正则表达式模式列表
        """
        patterns = []
        
        # 始终包含通用模式
        patterns.extend(self.important_patterns['universal'])
        
        # 添加特定类型的模式
        if agent_type in self.important_patterns:
            patterns.extend(self.important_patterns[agent_type])
        
        # 🔥 新增：根据智能体类型添加交叉相关的模式
        cross_relevant = {
            'research': ['business'],  # 研究型也关注业务数据
            'technical': ['research'],  # 技术型也关注研究结果
            'business': ['research', 'design'],  # 业务型关注研究和设计
            'design': ['business', 'writing'],  # 设计型关注业务和写作
            'writing': ['research', 'business']  # 写作型关注研究和业务
        }
        
        if agent_type in cross_relevant:
            for related_type in cross_relevant[agent_type]:
                if related_type in self.important_patterns:
                    # 只添加部分相关模式，避免过度匹配
                    related_patterns = self.important_patterns[related_type][:3]
                    patterns.extend(related_patterns)
        
        return patterns
    
    async def _generate_summary(self, messages: List[Dict], strategy: str, 
                               user_topic: str = "", next_agent_info: Dict = None) -> str:
        """🔥 优化版：使用 AIManager 生成智能摘要"""
        try:
            # 🔥 重构：调用新的异步内容构建函数
            content_to_summarize = await self._build_content_for_summarization(messages)
            
            # 🔥 新增：传递用户主题和下一个智能体信息
            summary_prompt = self._build_summary_prompt(
                content_to_summarize, 
                strategy, 
                'medium',
                user_topic=user_topic,
                next_agent_info=next_agent_info
            )
            
            # 🔥 优化：更专业的系统消息，强调协作信息保护
            system_content = 'You are an expert AI context summarizer specialized in multi-agent workflows. Your primary responsibility is to preserve ALL critical information needed for seamless agent collaboration, including team rules, agent-to-agent communications, and workflow states. Never sacrifice collaborative context for brevity. Create focused summaries that enable perfect handoffs between agents.'
            
            summary_messages = [
                {'role': 'system', 'content': system_content},
                {'role': 'user', 'content': summary_prompt}
            ]
            
            response = await self.ai_manager.chat_completion(
                messages=summary_messages,
                model='gemini-2.5-flash-preview-05-20',
                handle_tools=False,  # 摘要生成不需要工具调用
                role_id="langgraph_agent"
            )
            
            return response.get('content', '无法生成摘要')
            
        except Exception as e:
            logger.error(f"❌ 摘要生成失败: {str(e)}")
            return f"摘要生成失败，包含 {len(messages)} 条消息"
    
    async def _generate_core_summary(self, messages: List[Dict], strategy: str,
                                     user_topic: str = "", next_agent_info: Dict = None) -> str:
        """🔥 优化版：生成核心摘要（重度压缩）"""
        try:
            # 🔥 重构：调用新的异步内容构建函数
            content_to_summarize = await self._build_content_for_summarization(messages)
            
            # 🔥 新增：传递用户主题和下一个智能体信息
            summary_prompt = self._build_summary_prompt(
                content_to_summarize, 
                strategy, 
                'heavy',
                user_topic=user_topic,
                next_agent_info=next_agent_info
            )
            
            # 🔥 优化：针对重度压缩的专业系统消息，强调关键信息绝对保护
            system_content = 'You are an expert AI context compressor for multi-agent systems. Create ultra-condensed summaries while ABSOLUTELY preserving: 1) Team collaboration rules, 2) Agent-to-agent critical communications, 3) Key decisions and their context, 4) Important tool outputs, 5) Workflow state information. Never compress away information that breaks agent collaboration chains. Focus on actionable insights and seamless handoffs.'
            
            summary_messages = [
                {'role': 'system', 'content': system_content},
                {'role': 'user', 'content': summary_prompt}
            ]
            
            response = await self.ai_manager.chat_completion(
                messages=summary_messages,
                model='gemini-2.5-flash-preview-05-20',
                handle_tools=False
            )
            
            return response.get('content', '无法生成核心摘要')
            
        except Exception as e:
            logger.error(f"❌ 核心摘要生成失败: {str(e)}")
            return f"核心摘要生成失败，包含 {len(messages)} 条消息"
    
    def _build_summary_prompt(self, content: str, strategy: str, compression_level: str, 
                             user_topic: str = "", next_agent_info: Dict = None) -> str:
        """
        🔥 优化版：构建智能摘要提示词
        
        根据用户主题和下一个智能体信息进行针对性优化
        """
        # 🔥 优化：基于用户主题的智能提示 + 协作信息保护
        if user_topic:
            base_prompt = f"""
请对以下关于"{user_topic}"主题的对话内容进行{compression_level}级别的摘要，重点保留与该主题相关的核心信息：

{content}

🔥 关键摘要要求：
- 【绝对保留】：所有标记为"重要"、"关键"、"核心"、"结论"的信息
- 【优先保留】：与"{user_topic}"直接相关的事实、数据和决策
- 【智能体协作】：保留团队协作规则、智能体间的传递信息和工作流状态
- 【记忆工具信息】：必须保留所有记忆存储操作（store_memory）的详细信息，包括存储的键名、内容和智能体ID，这对后续智能体访问记忆至关重要
- 【上下文连贯】：维持对话的逻辑连贯性，特别是围绕该主题的讨论脉络
- 【关键决策】：突出与"{user_topic}"相关的关键决策、结论和进展
- 【工具输出】：保留重要的工具调用结果和数据输出
- 【简洁清晰】：使用简洁清晰的语言，便于后续智能体理解主题背景和协作上下文
"""
        else:
            base_prompt = f"""
请对以下对话内容进行{compression_level}级别的摘要，保留关键信息和重要细节：

{content}

🔥 关键摘要要求：
- 【绝对保留】：所有标记为"重要"、"关键"、"核心"、"结论"的信息
- 【智能体协作】：保留团队协作规则、智能体间的传递信息和工作流状态
- 【事实数据】：保留所有重要的事实、数据和统计信息
- 【上下文连贯】：维持对话的逻辑连贯性和时间顺序
- 【关键决策】：突出关键决策、结论和行动计划
- 【工具输出】：保留重要的工具调用结果和系统输出
- 【简洁清晰】：使用简洁清晰的语言，便于后续智能体理解和协作
"""
        
        # 🔥 新增：基于下一个智能体的针对性优化
        if next_agent_info:
            next_agent_role = next_agent_info.get('role_name', '')
            next_agent_expertise = next_agent_info.get('expertise', [])
            
            if next_agent_role:
                base_prompt += f"\n\n🎯 下一步处理者：{next_agent_role}"
                
                # 根据下一个智能体的角色调整摘要重点
                if '研究' in next_agent_role or 'research' in next_agent_role.lower():
                    base_prompt += "\n重点保留：研究发现、数据源、方法论、实验结果和研究结论"
                elif '开发' in next_agent_role or 'develop' in next_agent_role.lower() or 'engineer' in next_agent_role.lower():
                    base_prompt += "\n重点保留：技术方案、代码示例、架构设计、实现细节和技术决策"
                elif '分析' in next_agent_role or 'analyst' in next_agent_role.lower():
                    base_prompt += "\n重点保留：数据分析、统计结果、趋势判断、指标变化和分析结论"
                elif '设计' in next_agent_role or 'design' in next_agent_role.lower():
                    base_prompt += "\n重点保留：设计理念、用户需求、界面规范、交互逻辑和设计决策"
                elif '写作' in next_agent_role or 'writer' in next_agent_role.lower():
                    base_prompt += "\n重点保留：写作要求、内容结构、目标受众、风格指导和创作素材"
                elif '营销' in next_agent_role or 'market' in next_agent_role.lower():
                    base_prompt += "\n重点保留：市场数据、用户画像、竞品分析、营销策略和商业洞察"
                elif '项目' in next_agent_role or 'project' in next_agent_role.lower() or 'manager' in next_agent_role.lower():
                    base_prompt += "\n重点保留：项目进度、资源分配、风险评估、里程碑和管理决策"
                
                # 基于专业领域进一步优化
                if next_agent_expertise:
                    expertise_text = "、".join(next_agent_expertise[:3])  # 取前3个专业领域
                    base_prompt += f"\n专业重点：与{expertise_text}相关的专业信息和技术细节"
        
        # 根据策略调整摘要重点（保留原有逻辑）
        if strategy == 'research_focused':
            base_prompt += "\n特别关注：研究发现、数据分析、方法论和结论"
        elif strategy == 'writing_focused':
            base_prompt += "\n特别关注：写作结构、概念定义、示例和技术细节"
        elif strategy == 'data_focused':
            base_prompt += "\n特别关注：数值数据、统计结果、计算过程和数据来源"
        
        if compression_level == 'heavy':
            base_prompt += """

⚠️ 重度压缩模式特殊要求：
- 📋 将内容压缩到最核心的要点，每个主题只保留1-2句话
- 🔒 但是绝对不能压缩：团队协作规则、智能体传递的关键信息、重要的工具输出结果
- 🎯 确保下一个智能体能快速理解关键信息和协作上下文
- 📝 保持信息的完整性，宁可多保留一些重要信息，也不能丢失关键协作细节
- ⚡ 优先级：协作信息 > 用户目标 > 事实数据 > 一般对话"""
        
        return base_prompt
    
    def _clean_message_content(self, content: str) -> str:
        """清理消息内容"""
        # 移除多余的空白字符
        content = re.sub(r'\s+', ' ', content)
        
        # 移除重复的标点符号
        content = re.sub(r'[.]{3,}', '...', content)
        content = re.sub(r'[!]{2,}', '!!', content)
        content = re.sub(r'[?]{2,}', '??', content)
        
        # 移除过长的重复字符
        content = re.sub(r'(.)\1{5,}', r'\1\1\1', content)
        
        return content.strip()
    
    def get_compression_stats(self, original_messages: List[Dict], compressed_messages: List[Dict]) -> Dict:
        """获取压缩统计信息，分别分析系统消息和非系统消息"""
        # 计算原始大小
        original_total_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in original_messages
        )
        original_system_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in original_messages if msg.get('role') == 'system'
        )
        original_non_system_size = original_total_size - original_system_size
        
        # 计算压缩后大小
        compressed_total_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in compressed_messages
        )
        compressed_system_size = sum(
            len(str(msg.get('content', ''))) + len(json.dumps(msg.get('tool_calls_history', []), ensure_ascii=False))
            for msg in compressed_messages if msg.get('role') == 'system'
        )
        compressed_non_system_size = compressed_total_size - compressed_system_size
        
        # 计算压缩比率
        total_reduction_ratio = (original_total_size - compressed_total_size) / original_total_size if original_total_size > 0 else 0
        non_system_reduction_ratio = (original_non_system_size - compressed_non_system_size) / original_non_system_size if original_non_system_size > 0 else 0
        
        # 计算消息数量变化
        original_system_count = len([msg for msg in original_messages if msg.get('role') == 'system'])
        original_non_system_count = len(original_messages) - original_system_count
        compressed_system_count = len([msg for msg in compressed_messages if msg.get('role') == 'system'])
        compressed_non_system_count = len(compressed_messages) - compressed_system_count
        
        message_reduction_ratio = (len(original_messages) - len(compressed_messages)) / len(original_messages) if original_messages else 0
        non_system_message_reduction_ratio = (original_non_system_count - compressed_non_system_count) / original_non_system_count if original_non_system_count > 0 else 0
        
        return {
            'original_message_count': len(original_messages),
            'compressed_message_count': len(compressed_messages),
            'original_total_size_chars': original_total_size,
            'compressed_total_size_chars': compressed_total_size,
            'original_system_size_chars': original_system_size,
            'compressed_system_size_chars': compressed_system_size,
            'original_non_system_size_chars': original_non_system_size,
            'compressed_non_system_size_chars': compressed_non_system_size,
            'total_size_reduction_ratio': total_reduction_ratio,
            'non_system_size_reduction_ratio': non_system_reduction_ratio,
            'message_reduction_ratio': message_reduction_ratio,
            'non_system_message_reduction_ratio': non_system_message_reduction_ratio,
            'system_messages_preserved': original_system_count == compressed_system_count,
            'original_system_message_count': original_system_count,
            'compressed_system_message_count': compressed_system_count,
            'original_non_system_message_count': original_non_system_count,
            'compressed_non_system_message_count': compressed_non_system_count
        }


class ContextQualityAnalyzer:
    """
    🔥 上下文质量分析器
    
    功能：
    - 检测上下文质量问题
    - 识别信息冗余和重复
    - 评估压缩效果
    """
    
    def __init__(self):
        self.quality_thresholds = {
            'repetition_ratio': 0.3,    # 重复内容比例阈值
            'coherence_score': 0.7,     # 连贯性分数阈值
            'information_density': 0.5   # 信息密度阈值
        }
    
    def analyze_context_quality(self, messages: List[Dict]) -> Dict:
        """分析上下文质量"""
        try:
            analysis = {
                'repetition_score': self._calculate_repetition_score(messages),
                'coherence_score': self._calculate_coherence_score(messages),
                'information_density': self._calculate_information_density(messages),
                'quality_issues': [],
                'recommendations': []
            }
            
            # 检查质量问题
            if analysis['repetition_score'] > self.quality_thresholds['repetition_ratio']:
                analysis['quality_issues'].append('高重复内容')
                analysis['recommendations'].append('建议执行去重压缩')
            
            if analysis['coherence_score'] < self.quality_thresholds['coherence_score']:
                analysis['quality_issues'].append('连贯性较差')
                analysis['recommendations'].append('建议重新组织上下文结构')
            
            if analysis['information_density'] < self.quality_thresholds['information_density']:
                analysis['quality_issues'].append('信息密度较低')
                analysis['recommendations'].append('建议执行摘要压缩')
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 上下文质量分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_repetition_score(self, messages: List[Dict]) -> float:
        """计算重复内容比例"""
        if not messages:
            return 0.0
        
        contents = [msg.get('content', '') for msg in messages]
        content_hashes = [hash(content[:100]) for content in contents]
        unique_hashes = set(content_hashes)
        
        return 1.0 - (len(unique_hashes) / len(content_hashes))
    
    def _calculate_coherence_score(self, messages: List[Dict]) -> float:
        """计算连贯性分数（简化版）"""
        # 简化的连贯性评估：检查相邻消息的关联性
        if len(messages) < 2:
            return 1.0
        
        coherence_score = 0.0
        for i in range(len(messages) - 1):
            current_content = messages[i].get('content', '')
            next_content = messages[i + 1].get('content', '')
            
            # 简单的关联性检查：共同词汇
            current_words = set(current_content.lower().split())
            next_words = set(next_content.lower().split())
            
            if current_words and next_words:
                overlap = len(current_words & next_words)
                total = len(current_words | next_words)
                coherence_score += overlap / total if total > 0 else 0
        
        return coherence_score / (len(messages) - 1)
    
    def _calculate_information_density(self, messages: List[Dict]) -> float:
        """计算信息密度"""
        if not messages:
            return 0.0
        
        total_chars = 0
        meaningful_chars = 0
        
        for msg in messages:
            content = msg.get('content', '')
            total_chars += len(content)
            
            # 计算有意义的字符（排除空白和常见填充词）
            meaningful_content = re.sub(r'\s+', '', content)
            meaningful_content = re.sub(r'[，。！？、；：""''（）【】]', '', meaningful_content)
            meaningful_chars += len(meaningful_content)
        
        return meaningful_chars / total_chars if total_chars > 0 else 0.0


# 导出主要类
__all__ = [
    'ContextCompressionEngine',
    'ContextQualityAnalyzer',
    'CompressionLevel'
]

# 全局压缩引擎实例
def get_compression_engine() -> ContextCompressionEngine:
    """获取全局压缩引擎实例"""
    if not hasattr(get_compression_engine, '_instance'):
        get_compression_engine._instance = ContextCompressionEngine()
    return get_compression_engine._instance

async def get_compression_stats() -> Dict[str, Any]:
    """获取压缩引擎统计信息"""
    engine = get_compression_engine()
    return {
        "engine_initialized": True,
        "compression_levels": [level.name for level in CompressionLevel],
        "ai_manager_available": engine.ai_manager is not None
    }

def get_compression_engine_stats() -> Dict[str, Any]:
    """获取压缩引擎基础统计信息（同步版本）"""
    engine = get_compression_engine()
    return {
        "engine_initialized": True,
        "compression_levels": [level.name for level in CompressionLevel],
        "ai_manager_available": engine.ai_manager is not None
    } 