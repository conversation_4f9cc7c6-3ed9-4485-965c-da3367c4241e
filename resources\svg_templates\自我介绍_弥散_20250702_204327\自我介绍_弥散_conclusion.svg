<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变：弥散风格与深色背景结合 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#000000" />
      <stop offset="1" stop-color="#1E293B" />
    </linearGradient>

    <!-- 特斯拉红渐变：高亮强调色，带透明度制造科技感 -->
    <linearGradient id="teslaRedGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#E31937" stop-opacity="0.8" />
      <stop offset="1" stop-color="#E31937" stop-opacity="0.1" />
    </linearGradient>

    <!-- 强调蓝渐变：辅助高亮色，带透明度 -->
    <linearGradient id="accentBlueGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#06B6D4" stop-opacity="0.8" />
      <stop offset="1" stop-color="#06B6D4" stop-opacity="0.1" />
    </linearGradient>

    <!-- 柔和发光滤镜 (红色) -->
    <filter id="softGlowRed" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="15" result="blur" />
      <feFlood flood-color="#E31937" flood-opacity="0.6" result="color" />
      <feComposite in="color" in2="blur" operator="in" result="glow" />
      <feMerge>
        <feMergeNode in="glow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- 柔和发光滤镜 (蓝色) -->
    <filter id="softGlowBlue" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="15" result="blur" />
      <feFlood flood-color="#06B6D4" flood-opacity="0.6" result="color" />
      <feComposite in="color" in2="blur" operator="in" result="glow" />
      <feMerge>
        <feMergeNode in="glow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- 可复用图标符号：圆形 -->
    <symbol id="icon-circle" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" stroke="#06B6D4" stroke-width="2" fill="none" />
    </symbol>
    <!-- 可复用图标符号：星形 -->
    <symbol id="icon-star" viewBox="0 0 24 24">
      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#06B6D4" stroke-width="2" fill="none" />
    </symbol>
    <!-- 可复用图标符号：勾选 -->
    <symbol id="icon-check" viewBox="0 0 24 24">
      <path d="M20 6L9 17L4 12" stroke="#10B981" stroke-width="2" fill="none" />
    </symbol>
    <!-- 可复用图标符号：电话 -->
    <symbol id="icon-phone" viewBox="0 0 24 24">
      <path d="M22 16.92V20C22 20.5304 21.7893 21.0391 21.4142 21.4142C21.0391 21.7893 20.5304 22 20 22H4C3.46957 22 2.96086 21.7893 2.58579 21.4142C2.21071 21.0391 2 20.5304 2 20V16.92C2 16.3896 2.21071 15.8809 2.58579 15.5058C2.96086 15.1307 3.46957 14.92 4 14.92C4.53043 14.92 5.03914 15.1307 5.41421 15.5058C5.78929 15.8809 6 16.3896 6 16.92V18H18V16.92C18 16.3896 18.2107 15.8809 18.5858 15.5058C18.9609 15.1307 19.4696 14.92 20 14.92C20.5304 14.92 21.0391 15.1307 21.4142 15.5058C21.7893 15.8809 22 16.3896 22 16.92Z" stroke="#06B6D4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 2C12.5304 2 13.0391 2.21071 13.4142 2.58579C13.7893 2.96086 14 3.46957 14 4V10C14 10.5304 13.7893 11.0391 13.4142 11.4142C13.0391 11.7893 12.5304 12 12 12C11.4696 12 10.9609 11.7893 10.5858 11.4142C10.2107 11.0391 10 10.5304 10 10V4C10 3.46957 10.2107 2.96086 10.5858 2.58579C10.9609 2.21071 11.4696 2 12 2Z" stroke="#06B6D4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- 可复用图标符号：邮件 -->
    <symbol id="icon-mail" viewBox="0 0 24 24">
      <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="#06B6D4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M22 6L12 13L2 6" stroke="#06B6D4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>
    <!-- 可复用图标符号：领英 -->
    <symbol id="icon-linkedin" viewBox="0 0 24 24">
      <path d="M16 8C17.6568 8 19 9.34315 19 11V18H15V11C15 10.4477 14.5523 10 14 10C13.4477 10 13 10.4477 13 11V18H9V11C9 9.34315 10.3431 8 12 8C13.6568 8 15 9.34315 15 11Z" stroke="#06B6D4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M7 18V8" stroke="#06B6D4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M5 6C6.10457 6 7 5.10457 7 4C7 2.89543 6.10457 2 5 2C3.89543 2 3 2.89543 3 4C3 5.10457 3.89543 6 5 6Z" stroke="#06B6D4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

  </defs>

  <style>
    /* 全局字体定义 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* 字体大小与粗细 */
    .hero-title { font-size: 72px; font-weight: 700; }
    .main-title { font-size: 56px; font-weight: 700; }
    .section-title { font-size: 36px; font-weight: 700; }
    .content-title { font-size: 28px; font-weight: 700; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
    .small-text { font-size: 16px; font-weight: 400; }
    .caption-text { font-size: 14px; font-weight: 400; }

    /* 颜色定义 */
    .text-light-primary { fill: #F8FAFC; } /* 用于深色背景的浅色文本 */
    .text-light-secondary { fill: #94A3B8; } /* 用于深色背景的次要浅色文本 */
    .text-accent-red { fill: #E31937; } /* 特斯拉红强调色 */
    .text-accent-blue { fill: #06B6D4; } /* 强调蓝 */
    .stroke-red { stroke: #E31937; }
    .stroke-blue { stroke: #06B6D4; }

    /* 卡片/模块样式：Bento Grid风格，透明底，发光边框 */
    .card-background {
      fill: rgba(255, 255, 255, 0.05); /* 半透明白色填充 */
      stroke: rgba(6, 182, 212, 0.2); /* 蓝色边框 */
      stroke-width: 1px;
      rx: 12px;
      ry: 12px;
      filter: url(#softGlowBlue); /* 蓝色发光效果 */
    }
    .card-background-red {
      fill: rgba(227, 25, 55, 0.08); /* 半透明红色填充 */
      stroke: rgba(227, 25, 55, 0.3); /* 红色边框 */
      stroke-width: 1px;
      rx: 12px;
      ry: 12px;
      filter: url(#softGlowRed); /* 红色发光效果 */
    }
  </style>

  <!-- 背景层：应用深色渐变 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 动态背景装饰：弥散渐变图形，提升科技感 -->
  <g opacity="0.3">
    <ellipse cx="200" cy="800" rx="400" ry="200" fill="url(#teslaRedGradient)" transform="rotate(-30 200 800)" />
    <ellipse cx="1700" cy="250" rx="300" ry="150" fill="url(#accentBlueGradient)" transform="rotate(45 1700 250)" />
    <circle cx="960" cy="540" r="150" fill="url(#teslaRedGradient)" opacity="0.1" />
  </g>

  <!-- 主要内容区域：基于Bento Grid布局原则 -->
  <g transform="translate(80 60)">
    <!-- 顶部标题与副标题 -->
    <text x="0" y="56" class="main-title font-primary text-light-primary">
      {title}
    </text>
    <text x="0" y="106" class="body-text font-secondary text-light-secondary">
      {subtitle}
    </text>

    <!-- 总结要点区域 (Bento Grid 模块) -->
    <!-- 模块1：核心结论/优势1，强调数字与中英文结合 -->
    <rect x="0" y="180" width="800" height="300" class="card-background" />
    <text x="40" y="260" class="hero-title font-primary text-accent-red" filter="url(#softGlowRed)">
      {number_1}
    </text>
    <text x="40" y="320" class="content-title font-primary text-light-primary">
      {summary_point_1_zh}
    </text>
    <text x="40" y="360" class="body-text font-secondary text-light-secondary">
      {summary_point_1_en}
    </text>
    <text x="40" y="400" class="small-text font-secondary text-light-secondary">
      {summary_detail_1}
    </text>
    <use href="#icon-star" x="740" y="200" width="32" height="32" class="stroke-blue" />

    <!-- 模块2：核心结论/优势2，强调数字与中英文结合 -->
    <rect x="840" y="180" width="800" height="300" class="card-background" />
    <text x="880" y="260" class="hero-title font-primary text-accent-blue" filter="url(#softGlowBlue)">
      {number_2}
    </text>
    <text x="880" y="320" class="content-title font-primary text-light-primary">
      {summary_point_2_zh}
    </text>
    <text x="880" y="360" class="body-text font-secondary text-light-secondary">
      {summary_point_2_en}
    </text>
    <text x="880" y="400" class="small-text font-secondary text-light-secondary">
      {summary_detail_2}
    </text>
    <use href="#icon-circle" x="1580" y="200" width="32" height="32" class="stroke-blue" />

    <!-- 行动建议/号召行动区域：强调设计，红色高亮 -->
    <rect x="0" y="512" width="1640" height="200" class="card-background-red" />
    <text x="40" y="562" class="section-title font-primary text-accent-red">
      {call_to_action_title_zh}
    </text>
    <text x="40" y="602" class="content-title font-primary text-light-primary">
      {call_to_action_title_en}
    </text>
    <text x="40" y="642" class="body-text font-secondary text-light-primary">
      {call_to_action_content}
    </text>
    <use href="#icon-check" x="1580" y="532" width="32" height="32" />

    <!-- 联系信息模块：图标与文本结合，便利获取 -->
    <rect x="0" y="744" width="800" height="240" class="card-background" />
    <text x="40" y="794" class="section-title font-primary text-light-primary">
      联系方式
    </text>

    <g class="body-text font-secondary text-light-secondary">
      <use href="#icon-mail" x="40" y="824" width="24" height="24" />
      <text x="80" y="844">
        邮箱: {email_address}
      </text>

      <use href="#icon-phone" x="40" y="864" width="24" height="24" />
      <text x="80" y="884">
        电话: {phone_number}
      </text>

      <use href="#icon-linkedin" x="40" y="904" width="24" height="24" />
      <text x="80" y="924">
        领英: {linkedin_url}
      </text>
    </g>

    <!-- 感谢语或结束语模块：强调设计，蓝色高亮 -->
    <rect x="840" y="744" width="800" height="240" class="card-background" />
    <text x="880" y="814" class="main-title font-primary text-accent-blue" filter="url(#softGlowBlue)">
      感谢您的时间
    </text>
    <text x="880" y="874" class="body-text font-secondary text-light-secondary">
      {closing_remark}
    </text>
    <text x="880" y="914" class="small-text font-secondary text-light-secondary">
      {author} | {date}
    </text>

  </g>

  <!-- Logo占位符 (左上角) -->
  <g transform="translate(80 20)">
    <image href="{logo_url}" x="0" y="0" width="80" height="80" />
  </g>

</svg>