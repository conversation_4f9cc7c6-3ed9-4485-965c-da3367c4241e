<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 样式定义 -->
    <style type="text/css">
      /* 字体定义 */
      @font-face {
        font-family: 'Microsoft YaHei';
        src: local('Microsoft YaHei'), local('MicrosoftYaHei');
        font-weight: normal;
      }
      @font-face {
        font-family: 'Segoe UI';
        src: local('Segoe UI'), local('SegoeUI');
        font-weight: normal;
      }
      @font-face {
        font-family: 'Source Han Sans CN';
        src: local('Source Han Sans CN'), local('SourceHanSansCN');
        font-weight: normal;
      }
      @font-face {
        font-family: 'Noto Sans CJK SC';
        src: local('Noto Sans CJK SC'), local('NotoSansCJKSC');
        font-weight: normal;
      }

      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* 颜色变量 */
      .color-primary { fill: #1E40AF; }
      .color-secondary { fill: #475569; }
      .color-accent { fill: #3B82F6; }
      .color-background { fill: #F8FAFC; }
      .color-text-primary { fill: #1E293B; }
      .color-text-secondary { fill: #64748B; }
      .color-card-background { fill: #FFFFFF; }
      .color-card-border { stroke: #BAE6FD; }
      .color-timeline-line { stroke: #BAE6FD; }
      .color-timeline-dot { fill: #1E40AF; }
      .color-milestone-dot { fill: #3B82F6; }
      .color-milestone-text { fill: #1E40AF; }

      /* 字体大小和粗细 */
      .text-hero-title { font-size: 72px; font-weight: 700; } /* bold */
      .text-main-title { font-size: 56px; font-weight: 700; } /* bold */
      .text-section-title { font-size: 36px; font-weight: 600; } /* semibold */
      .text-content-title { font-size: 28px; font-weight: 600; } /* semibold */
      .text-body { font-size: 22px; font-weight: 400; } /* normal */
      .text-small { font-size: 16px; font-weight: 400; } /* normal */
      .text-caption { font-size: 14px; font-weight: 400; } /* normal */
      .text-bold { font-weight: 700; }

      /* 布局和通用样式 */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        filter: url(#cardShadow);
      }
      .milestone-card-style {
        fill: #E0F2FE; /* Lighter blue background for milestone */
        stroke: #3B82F6; /* Accent blue border */
        stroke-width: 2px;
        filter: url(#milestoneCardShadow);
      }
      .text-align-center { text-anchor: middle; }
      .text-align-right { text-anchor: end; }
    </style>

    <!-- 阴影滤镜 -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4" />
      <feColorMatrix result="shadowMatrix" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
      <feBlend in="SourceGraphic" in2="shadowMatrix" mode="normal" />
    </filter>
    <filter id="milestoneCardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="8" />
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="6" />
      <feColorMatrix result="shadowMatrix" in="blurOut" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
      <feBlend in="SourceGraphic" in2="shadowMatrix" mode="normal" />
    </filter>

    <!-- 渐变定义 (用于背景或装饰) -->
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- 箭头标记定义 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#BAE6FD" />
    </marker>

  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 顶部 Logo 占位符 -->
  <g id="logo-section">
    <!-- 替换为您的Logo SVG或Image -->
    <!-- 例如: <image xlink:href="{logo_url}" x="80" y="60" width="150" height="40" /> -->
    <rect x="80" y="60" width="150" height="40" fill="#E0F2FE" rx="5" />
    <text x="155" y="87" class="font-primary text-small color-text-secondary text-align-center">
      {logo_url}
    </text>
  </g>

  <!-- 页面标题 -->
  <g id="page-title">
    <text x="960" y="160" class="font-primary text-main-title color-text-primary text-align-center">
      <tspan x="960" y="160">公司发展里程碑</tspan>
      <tspan x="960" y="220" class="text-section-title color-text-secondary">{title}</tspan>
    </text>
    <text x="960" y="265" class="font-secondary text-body color-text-secondary text-align-center">
      <tspan x="960" y="265">回顾我们共同走过的创新和成长之路</tspan>
      <tspan x="960" y="300" class="text-small color-text-light">{subtitle}</tspan>
    </text>
  </g>

  <!-- 时间轴核心元素 -->
  <g id="timeline-core">
    <!-- 时间轴主线 -->
    <line x1="960" y1="360" x2="960" y2="980" stroke="#BAE6FD" stroke-width="3" marker-end="url(#arrowhead)" />

    <!-- 时间轴事件节点 -->
    <!-- Event 1: 2020 - 公司成立 -->
    <circle cx="960" cy="400" r="10" class="color-timeline-dot" />
    <rect x="1020" y="360" width="700" height="120" rx="12" class="card-style" />
    <text x="1050" y="395" class="font-primary text-content-title color-milestone-text">
      <tspan class="text-bold">2020</tspan>
      <tspan class="text-bold" dx="20">公司成立和初期探索</tspan>
    </text>
    <text x="1050" y="435" class="font-secondary text-body color-text-secondary">
      <tspan x="1050" y="435">我们怀揣梦想，开启了创新之旅。</tspan>
      <tspan x="1050" y="465" class="text-small color-text-light">{content}</tspan>
    </text>

    <!-- Event 2: 2021 - 首款产品发布 -->
    <circle cx="960" cy="530" r="10" class="color-timeline-dot" />
    <rect x="200" y="490" width="700" height="120" rx="12" class="card-style" />
    <text x="870" y="525" class="font-primary text-content-title color-milestone-text text-align-right">
      <tspan class="text-bold">2021</tspan>
      <tspan class="text-bold" dx="-20">首款产品发布</tspan>
    </text>
    <text x="870" y="565" class="font-secondary text-body color-text-secondary text-align-right">
      <tspan x="870" y="565">市场反响热烈，奠定了发展基础。</tspan>
      <tspan x="870" y="595" class="text-small color-text-light">{content}</tspan>
    </text>

    <!-- Event 3: 2022 - 市场拓展和用户增长 -->
    <circle cx="960" cy="660" r="10" class="color-timeline-dot" />
    <rect x="1020" y="620" width="700" height="120" rx="12" class="card-style" />
    <text x="1050" y="655" class="font-primary text-content-title color-milestone-text">
      <tspan class="text-bold">2022</tspan>
      <tspan class="text-bold" dx="20">市场拓展和用户增长</tspan>
    </text>
    <text x="1050" y="695" class="font-secondary text-body color-text-secondary">
      <tspan x="1050" y="695">用户数量快速增长，市场份额持续扩大。</tspan>
      <tspan x="1050" y="725" class="text-small color-text-light">{content}</tspan>
    </text>

    <!-- Event 4: 2023 - 获得A轮融资 (Milestone) -->
    <circle cx="960" cy="790" r="15" class="color-milestone-dot" />
    <rect x="200" y="750" width="700" height="120" rx="12" class="milestone-card-style" />
    <text x="870" y="785" class="font-primary text-content-title color-milestone-text text-align-right">
      <tspan class="text-bold">2023</tspan>
      <tspan class="text-bold" dx="-20">获得A轮融资</tspan>
    </text>
    <text x="870" y="825" class="font-secondary text-body color-text-secondary text-align-right">
      <tspan x="870" y="825">资本的认可为我们注入了新的活力。</tspan>
      <tspan x="870" y="855" class="text-small color-text-secondary">{content}</tspan>
    </text>

    <!-- Event 5: 2024 - 全球战略伙伴关系建立 (Milestone) -->
    <circle cx="960" cy="920" r="15" class="color-milestone-dot" />
    <rect x="1020" y="880" width="700" height="120" rx="12" class="milestone-card-style" />
    <text x="1050" y="915" class="font-primary text-content-title color-milestone-text">
      <tspan class="text-bold">2024</tspan>
      <tspan class="text-bold" dx="20">全球战略伙伴关系建立</tspan>
    </text>
    <text x="1050" y="955" class="font-secondary text-body color-text-secondary">
      <tspan x="1050" y="955">与国际伙伴携手，拓展全球市场。</tspan>
      <tspan x="1050" y="985" class="text-small color-text-secondary">{content}</tspan>
    </text>

  </g>

  <!-- 底部页码/信息 -->
  <g id="footer-info">
    <text x="960" y="1050" class="font-secondary text-caption color-text-light text-align-center">
      <tspan x="960" y="1050">页面 8/10</tspan>
      <tspan x="960" y="1050" dx="100">{date}</tspan>
      <tspan x="960" y="1050" dx="200">{author}</tspan>
    </text>
  </g>

</svg>