<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- 主色调渐变 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- 强调色渐变 -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E3A8A" />
    </linearGradient>

    <!-- 图标定义 -->
    <symbol id="iconCheck" viewBox="0 0 24 24">
      <path fill="#3B82F6" d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
    </symbol>
    <symbol id="iconStar" viewBox="0 0 24 24">
      <path fill="#3B82F6" d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
    </symbol>
    <symbol id="iconPhone" viewBox="0 0 24 24">
      <path fill="#1E3A8A" d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.46.57 3.57.12.35.03.75-.25 1.02l-2.2 2.2z"/>
    </symbol>
    <symbol id="iconEmail" viewBox="0 0 24 24">
      <path fill="#1E3A8A" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
    </symbol>

    <!-- 卡片阴影滤镜 -->
    <!-- SVG不支持CSS的box-shadow，这里使用feDropShadow模拟 -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="rgba(0,0,0,0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.06)"/>
    </filter>
  </defs>

  <style>
    /* 字体族 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* 文本颜色 */
    .text-primary-color { fill: #1E293B; }
    .text-secondary-color { fill: #64748B; }
    .text-light-color { fill: #94A3B8; }
    .text-accent-color { fill: #3B82F6; }
    .text-white { fill: #FFFFFF; }

    /* 字体大小和粗细 */
    .hero-title { font-size: 72px; font-weight: 700; } /* bold */
    .main-title { font-size: 56px; font-weight: 700; } /* bold */
    .section-title { font-size: 36px; font-weight: 600; } /* semibold */
    .content-title { font-size: 28px; font-weight: 600; } /* semibold */
    .body-text { font-size: 22px; font-weight: 400; } /* normal */
    .small-text { font-size: 16px; font-weight: 400; } /* normal */
    .caption { font-size: 14px; font-weight: 400; } /* normal */

    /* 填充和描边颜色 */
    .fill-primary { fill: #1E3A8A; }
    .fill-secondary { fill: #1E40AF; }
    .fill-accent { fill: #3B82F6; }
    .stroke-primary { stroke: #1E3A8A; }
    .stroke-accent { stroke: #3B82F6; }
    .stroke-width-2 { stroke-width: 2; }
    .rounded-rect { rx: 12px; ry: 12px; } /* 卡片圆角 */
  </style>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- 顶部左侧装饰元素 -->
  <rect x="0" y="0" width="200" height="150" fill="#1E3A8A" opacity="0.05" />
  <circle cx="100" cy="100" r="80" fill="#3B82F6" opacity="0.03" />

  <!-- 页面标题区域 -->
  <g id="title-section">
    <text x="960" y="150" text-anchor="middle" class="font-primary main-title text-primary-color">
      <tspan x="960" dy="0">{title} 新时代 新征程：总结和展望</tspan>
      <tspan x="960" dy="60" class="font-secondary section-title text-secondary-color">
        {subtitle} New Era, New Journey: Summary and Outlook
      </tspan>
    </text>
    <!-- Logo 占位符 -->
    <image href="{logo_url}" x="80" y="60" width="120" height="60" />
    <!-- 如果{logo_url}为空，可使用以下示例Logo -->
    <!-- <rect x="80" y="60" width="120" height="60" fill="#1E3A8A"/>
    <text x="140" y="95" text-anchor="middle" class="font-primary content-title text-white">LOGO</text> -->
  </g>

  <!-- 主要内容区域 -->
  <g id="content-area">
    <!-- 主要结论部分 -->
    <rect x="80" y="280" width="850" height="400" fill="#FFFFFF" class="rounded-rect" filter="url(#cardShadow)" />
    <text x="120" y="330" class="font-primary section-title text-primary-color">主要结论</text>
    <line x1="120" y1="355" x2="320" y2="355" stroke="#BAE6FD" stroke-width="2" />

    <g id="conclusions-list">
      <use href="#iconCheck" x="120" y="385" width="24" height="24" />
      <text x="155" y="405" class="font-secondary body-text text-primary-color">
        <tspan x="155" dy="0">1. {content} 政策体系日益完善，成效显著。</tspan>
        <tspan x="155" dy="35">Policy system is increasingly perfect, with remarkable results.</tspan>
      </text>

      <use href="#iconCheck" x="120" y="460" width="24" height="24" />
      <text x="155" y="480" class="font-secondary body-text text-primary-color">
        <tspan x="155" dy="0">2. {content} 社会民生福祉持续改善，人民获得感提升。</tspan>
        <tspan x="155" dy="35">Social welfare continues to improve, enhancing public sense of gain.</tspan>
      </text>

      <use href="#iconCheck" x="120" y="535" width="24" height="24" />
      <text x="155" y="555" class="font-secondary body-text text-primary-color">
        <tspan x="155" dy="0">3. {content} 经济高质量发展取得新突破，前景广阔。</tspan>
        <tspan x="155" dy="35">High-quality economic development achieves new breakthroughs, with broad prospects.</tspan>
      </text>

      <use href="#iconCheck" x="120" y="610" width="24" height="24" />
      <text x="155" y="630" class="font-secondary body-text text-primary-color">
        <tspan x="155" dy="0">4. {content} 党建工作深入推进，凝聚力不断增强。</tspan>
        <tspan x="155" dy="35">Party building work deepens, continuously strengthening cohesion.</tspan>
      </text>
    </g>

    <!-- 行动要点部分 (强调设计) -->
    <rect x="990" y="280" width="850" height="400" fill="#1E3A8A" class="rounded-rect" filter="url(#cardShadow)" />
    <text x="1030" y="330" class="font-primary section-title text-white">行动要点</text>
    <line x1="1030" y1="355" x2="1230" y2="355" stroke="#3B82F6" stroke-width="2" />

    <g id="action-points-list">
      <use href="#iconStar" x="1030" y="385" width="24" height="24" />
      <text x="1065" y="405" class="font-secondary body-text text-white">
        <tspan x="1065" dy="0">1. {content} 持续深化改革开放，激发市场活力。</tspan>
        <tspan x="1065" dy="35">Continue to deepen reform and opening up, stimulate market vitality.</tspan>
      </text>

      <use href="#iconStar" x="1030" y="460" width="24" height="24" />
      <text x="1065" y="480" class="font-secondary body-text text-white">
        <tspan x="1065" dy="0">2. {content} 坚持创新驱动发展，提升核心竞争力。</tspan>
        <tspan x="1065" dy="35">Adhere to innovation-driven development, enhance core competitiveness.</tspan>
      </text>

      <use href="#iconStar" x="1030" y="535" width="24" height="24" />
      <text x="1065" y="555" class="font-secondary body-text text-white">
        <tspan x="1065" dy="0">3. {content} 强化基层治理能力，服务人民群众。</tspan>
        <tspan x="1065" dy="35">Strengthen grassroots governance capacity, serve the people.</tspan>
      </text>

      <use href="#iconStar" x="1030" y="610" width="24" height="24" />
      <text x="1065" y="630" class="font-secondary body-text text-white">
        <tspan x="1065" dy="0">4. {content} 筑牢安全发展底线，维护社会稳定。</tspan>
        <tspan x="1065" dy="35">Solidify the bottom line of safe development, maintain social stability.</tspan>
      </text>
    </g>
  </g>

  <!-- 底部区域：联系信息和号召行动 -->
  <g id="footer-section">
    <!-- 联系信息卡片 -->
    <rect x="80" y="730" width="850" height="270" fill="#FFFFFF" class="rounded-rect" filter="url(#cardShadow)" />
    <text x="120" y="780" class="font-primary section-title text-primary-color">联系我们</text>
    <line x1="120" y1="805" x2="320" y2="805" stroke="#BAE6FD" stroke-width="2" />

    <g id="contact-details">
      <use href="#iconPhone" x="120" y="840" width="28" height="28" />
      <text x="160" y="860" class="font-secondary body-text text-primary-color">电话: {contact_phone}</text>
      <use href="#iconEmail" x="120" y="890" width="28" height="28" />
      <text x="160" y="910" class="font-secondary body-text text-primary-color">邮箱: {contact_email}</text>
      <text x="120" y="950" class="font-secondary body-text text-secondary-color">
        <tspan x="120" dy="0">地址: {contact_address}</tspan>
        <tspan x="120" dy="35">Address: {contact_address_en}</tspan>
      </text>
    </g>

    <!-- 感谢语和号召行动 -->
    <rect x="990" y="730" width="850" height="270" fill="url(#accentGradient)" class="rounded-rect" filter="url(#cardShadow)" />
    <text x="1030" y="790" class="font-primary hero-title text-white">
      <tspan x="1030" dy="0">感谢您的关注！</tspan>
      <tspan x="1030" dy="70" class="font-secondary section-title">Thank You For Your Attention!</tspan>
    </text>

    <!-- 号召行动按钮 -->
    <rect x="1030" y="900" width="350" height="60" fill="#1E3A8A" class="rounded-rect" />
    <text x="1205" y="930" text-anchor="middle" class="font-primary content-title text-white">
      <tspan x="1205" dy="0">了解更多</tspan>
      <tspan x="1205" dy="30" class="small-text">Learn More</tspan>
    </text>
    <!-- 装饰性箭头 -->
    <polyline points="1395,930 1415,940 1395,950" fill="none" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
  </g>

  <!-- 底部右侧装饰元素 -->
  <rect x="1720" y="930" width="200" height="150" fill="#1E40AF" opacity="0.05" />
  <circle cx="1820" cy="980" r="80" fill="#1E3A8A" opacity="0.03" />

</svg>