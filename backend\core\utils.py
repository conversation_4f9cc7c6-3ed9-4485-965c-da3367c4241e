"""
通用工具函数模块
"""
import os
import sys
import logging
import psutil
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def terminate_child_processes(pid=None):
    """
    递归终止子进程
    
    Args:
        pid: 进程ID，如果为None则使用当前进程ID
    """
    if pid is None:
        pid = os.getpid()
        
    try:
        parent = psutil.Process(pid)
        logger.info(f"正在终止API服务器相关子进程，根进程ID: {pid}")
        
        # 获取所有子进程
        children = parent.children(recursive=True)
        
        # 先尝试正常终止子进程
        for child in children:
            try:
                logger.debug(f"正在终止子进程: {child.pid}")
                child.terminate()
            except Exception as e:
                logger.warning(f"终止子进程 {child.pid} 时出错: {e}")
        
        # 等待子进程终止
        gone, alive = psutil.wait_procs(children, timeout=2)
        
        # 如果仍有进程存活，强制杀死
        for child in alive:
            try:
                logger.warning(f"子进程 {child.pid} 未能正常终止，强制杀死")
                child.kill()
            except Exception as e:
                logger.warning(f"杀死子进程 {child.pid} 时出错: {e}")
                
        logger.info(f"已终止所有API服务器子进程（已终止: {len(gone)}，强制杀死: {len(alive)}）")
        
    except Exception as e:
        logger.error(f"终止API服务器子进程时出错: {e}")


def safe_create_file_handler(log_file_path: str, encoding: str = 'utf-8', mode: str = 'a') -> logging.Handler:
    """
    安全创建FileHandler，确保目录和文件存在
    
    Args:
        log_file_path: 日志文件路径
        encoding: 文件编码，默认utf-8
        mode: 文件打开模式，默认追加模式
        
    Returns:
        logging.FileHandler: 配置好的文件处理器
        
    Raises:
        Exception: 如果无法创建目录或文件
    """
    try:
        # 确保父目录存在
        log_dir = os.path.dirname(log_file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            logger.info(f"创建日志目录: {log_dir}")
        
        # 确保文件存在（如果文件不存在则创建空文件）
        if not os.path.exists(log_file_path):
            with open(log_file_path, 'w', encoding=encoding) as f:
                f.write('')  # 创建空文件
            logger.info(f"创建日志文件: {log_file_path}")
        
        # 创建FileHandler
        file_handler = logging.FileHandler(log_file_path, encoding=encoding, mode=mode)
        logger.info(f"成功创建FileHandler: {log_file_path}")
        
        return file_handler
        
    except Exception as e:
        logger.warning(f"创建FileHandler失败: {log_file_path}, 错误: {e}")
        # 如果无法创建文件处理器，创建一个空的StreamHandler作为后备
        logger.warning("使用StreamHandler作为后备方案")
        return logging.StreamHandler()


def get_resource_path(relative_path: str) -> str:
    """
    获取资源文件的绝对路径，兼容开发环境和打包环境
    
    Args:
        relative_path: 相对于项目根目录的资源文件路径，如 "resources/svg_templates"
        
    Returns:
        str: 资源文件的绝对路径
        
    Examples:
        >>> get_resource_path("resources/svg_templates")
        # 开发环境: "E:/jimu-new/resources/svg_templates"
        # 打包环境: "C:/Users/<USER>/AppData/Local/Temp/_MEI123456/resources/svg_templates"
    """
    try:
        # 检查是否在PyInstaller打包环境中
        if getattr(sys, '_MEIPASS', None):
            # 打包环境：使用临时解压目录
            base_path = getattr(sys, '_MEIPASS')
            logger.debug(f"检测到打包环境，使用_MEIPASS: {base_path}")
        else:
            # 开发环境：使用当前工作目录
            base_path = os.getcwd()
            logger.debug(f"检测到开发环境，使用getcwd: {base_path}")
        
        # 拼接完整路径
        full_path = os.path.join(base_path, relative_path)
        full_path = os.path.normpath(full_path)  # 规范化路径
        
        logger.debug(f"资源路径解析: {relative_path} -> {full_path}")
        return full_path
        
    except Exception as e:
        logger.error(f"获取资源路径失败: {relative_path}, 错误: {e}")
        # 后备方案：直接使用当前目录
        fallback_path = os.path.join(os.getcwd(), relative_path)
        logger.warning(f"使用后备路径: {fallback_path}")
        return fallback_path


def get_svg_templates_dir() -> str:
    """
    获取SVG模板目录的绝对路径
    
    Returns:
        str: SVG模板目录的绝对路径
    """
    return get_resource_path("resources/svg_templates") 