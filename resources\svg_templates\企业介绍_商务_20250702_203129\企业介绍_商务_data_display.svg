<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette and General Styles -->
    <style type="text/css">
      /* Background */
      .bg-color { fill: #F8FAFC; }
      .card-bg { fill: #FFFFFF; }
      .container-bg { fill: #E0F2FE; }

      /* Primary Colors */
      .primary-color-fill { fill: #1E40AF; }
      .primary-color-stroke { stroke: #1E40AF; }
      .accent-color-fill { fill: #3B82F6; }
      .accent-color-stroke { stroke: #3B82F6; }
      .secondary-color-fill { fill: #475569; }
      .secondary-color-stroke { stroke: #475569; }

      /* Text Colors */
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .text-accent-color { fill: #3B82F6; }

      /* Card Border */
      .card-border-stroke { stroke: #BAE6FD; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }
      .large-number { font-size: 96px; font-weight: 900; line-height: 1.1; } /* Custom for emphasis */
      .stat-label { font-size: 24px; font-weight: 500; line-height: 1.4; }

      /* Card Style */
      .card-shadow { filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06)); }

      /* Icon Style */
      .icon-style { stroke: #4A86E8; stroke-width: 2; fill: none; } /* Using a slightly brighter blue for icons */

    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF" />
      <stop offset="100%" style="stop-color:#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#1E40AF" />
    </linearGradient>

    <!-- Reusable Icon: Bar Chart Icon (filled for clarity in this context) -->
    <symbol id="icon-bar-chart" viewBox="0 0 24 24">
      <path d="M4 20H20V22H4V20Z" fill="#4A86E8" />
      <rect x="5" y="10" width="4" height="10" fill="#4A86E8" />
      <rect x="10" y="6" width="4" height="14" fill="#4A86E8" />
      <rect x="15" y="2" width="4" height="18" fill="#4A86E8" />
    </symbol>

    <!-- Reusable Icon: Target Icon (outline style) -->
    <symbol id="icon-target" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" stroke="#4A86E8" stroke-width="2" fill="none"/>
      <circle cx="12" cy="12" r="6" stroke="#4A86E8" stroke-width="2" fill="none"/>
      <circle cx="12" cy="12" r="2" fill="#4A86E8"/>
    </symbol>

    <!-- Reusable Icon: People Icon (filled for clarity) -->
    <symbol id="icon-people" viewBox="0 0 24 24">
      <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="#4A86E8" />
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Decorative geometric elements (subtle, in blue tones, low opacity) -->
  <circle cx="1700" cy="100" r="150" fill="#3B82F6" opacity="0.05" />
  <rect x="100" y="800" width="300" height="200" rx="20" fill="#1E40AF" opacity="0.03" />
  <path d="M0 0 L 300 0 L 0 300 Z" fill="#475569" opacity="0.02" />
  <path d="M1920 1080 L 1620 1080 L 1920 780 Z" fill="#475569" opacity="0.02" />

  <!-- Header: Logo Placeholder -->
  <g id="header-logo">
    <rect x="80" y="60" width="180" height="50" fill="#E0F2FE" rx="8" />
    <text x="170" y="95" text-anchor="middle" class="font-primary body-text text-primary-color">
      {logo_url}
      <tspan x="170" dy="0" font-size="24" font-weight="700">公司徽标</tspan>
    </text>
  </g>

  <!-- Main Content Area, positioned with page margins -->
  <g id="main-content" transform="translate(80 140)">
    <!-- Title and Subtitle -->
    <text x="0" y="0" class="font-primary main-title text-primary-color">
      <tspan x="0" dy="0">企业核心数据概览</tspan>
      <tspan x="0" dy="65" class="font-primary section-title text-secondary-color">Business Performance Snapshot</tspan>
    </text>

    <!-- Main Chart Area (Simulated Bar Chart) -->
    <g id="chart-area" transform="translate(0 150)">
      <rect x="0" y="0" width="1100" height="500" rx="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />

      <!-- Chart Title -->
      <text x="550" y="50" text-anchor="middle" class="font-primary content-title text-primary-color">
        <tspan x="550" dy="0">年度业绩增长趋势</tspan>
        <tspan x="550" dy="35" class="font-secondary small-text text-secondary-color">Annual Performance Growth Trend</tspan>
      </text>

      <!-- Chart Axes -->
      <line x1="100" y1="400" x2="1000" y2="400" stroke="#E0F2FE" stroke-width="2" /> <!-- X-axis -->
      <line x1="100" y1="400" x2="100" y2="100" stroke="#E0F2FE" stroke-width="2" /> <!-- Y-axis -->

      <!-- Simulated Bars and Labels -->
      <!-- Bar 1: 2020 -->
      <rect x="150" y="300" width="80" height="100" rx="4" fill="#1E40AF" />
      <text x="190" y="425" text-anchor="middle" class="font-secondary small-text text-secondary-color">2020</text>
      <text x="190" y="290" text-anchor="middle" class="font-primary small-text text-accent-color">1.2亿</text>

      <!-- Bar 2: 2021 -->
      <rect x="300" y="250" width="80" height="150" rx="4" fill="#1E40AF" />
      <text x="340" y="425" text-anchor="middle" class="font-secondary small-text text-secondary-color">2021</text>
      <text x="340" y="240" text-anchor="middle" class="font-primary small-text text-accent-color">1.8亿</text>

      <!-- Bar 3: 2022 (Highlighted) -->
      <rect x="450" y="150" width="80" height="250" rx="4" fill="#3B82F6" />
      <text x="490" y="425" text-anchor="middle" class="font-secondary small-text text-primary-color" font-weight="700">2022</text>
      <text x="490" y="140" text-anchor="middle" class="font-primary small-text text-accent-color" font-weight="700">3.5亿</text>

      <!-- Bar 4: 2023 -->
      <rect x="600" y="200" width="80" height="200" rx="4" fill="#1E40AF" />
      <text x="640" y="425" text-anchor="middle" class="font-secondary small-text text-secondary-color">2023</text>
      <text x="640" y="190" text-anchor="middle" class="font-primary small-text text-accent-color">2.5亿</text>

      <!-- Bar 5: 2024 -->
      <rect x="750" y="100" width="80" height="300" rx="4" fill="#1E40AF" />
      <text x="790" y="425" text-anchor="middle" class="font-secondary small-text text-secondary-color">2024</text>
      <text x="790" y="90" text-anchor="middle" class="font-primary small-text text-accent-color">4.2亿</text>

      <!-- Y-axis labels -->
      <text x="90" y="400" text-anchor="end" class="font-secondary caption-text text-secondary-color">0</text>
      <text x="90" y="300" text-anchor="end" class="font-secondary caption-text text-secondary-color">1亿</text>
      <text x="90" y="200" text-anchor="end" class="font-secondary caption-text text-secondary-color">3亿</text>
      <text x="90" y="100" text-anchor="end" class="font-secondary caption-text text-secondary-color">5亿</text>

      <!-- Chart Description (minimum 25px dy for line spacing) -->
      <text x="100" y="470" class="font-secondary small-text text-secondary-color">
        <tspan x="100" dy="0">数据来源：公司财务报告和市场分析。趋势表明稳健增长。</tspan>
        <tspan x="100" dy="25">Data Source: Company Financial Reports 和#38; Market Analysis. Trend indicates steady growth.</tspan>
      </text>
    </g>

    <!-- Key Statistics / Data Cards (Right Side) -->
    <g id="statistics-cards" transform="translate(1200 150)">
      <!-- Card 1: Total Revenue -->
      <rect x="0" y="0" width="600" height="200" rx="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
      <use xlink:href="#icon-bar-chart" x="30" y="30" width="48" height="48" />
      <text x="100" y="60" class="font-primary content-title text-primary-color">
        <tspan x="100" dy="0">累计总收入</tspan>
        <tspan x="100" dy="35" class="font-secondary small-text text-secondary-color">Total Cumulative Revenue</tspan>
      </text>
      <text x="30" y="150" class="font-primary large-number text-accent-color">
        <tspan x="30" dy="0">10.5亿</tspan>
        <tspan x="30" dy="90" class="font-secondary stat-label text-secondary-color">人民币</tspan>
      </text>

      <!-- Card 2: Market Share (50px vertical spacing from previous card) -->
      <rect x="0" y="250" width="600" height="200" rx="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
      <use xlink:href="#icon-target" x="30" y="280" width="48" height="48" />
      <text x="100" y="310" class="font-primary content-title text-primary-color">
        <tspan x="100" dy="0">市场占有率</tspan>
        <tspan x="100" dy="35" class="font-secondary small-text text-secondary-color">Market Share</tspan>
      </text>
      <text x="30" y="400" class="font-primary large-number text-accent-color">
        <tspan x="30" dy="0">28.5%</tspan>
        <tspan x="30" dy="90" class="font-secondary stat-label text-secondary-color">行业领先</tspan>
      </text>

      <!-- Card 3: Team Growth (50px vertical spacing from previous card) -->
      <rect x="0" y="500" width="600" height="200" rx="12" class="card-bg card-shadow" stroke="#BAE6FD" stroke-width="1" />
      <use xlink:href="#icon-people" x="30" y="530" width="48" height="48" />
      <text x="100" y="560" class="font-primary content-title text-primary-color">
        <tspan x="100" dy="0">团队规模增长</tspan>
        <tspan x="100" dy="35" class="font-secondary small-text text-secondary-color">Team Size Growth</tspan>
      </text>
      <text x="30" y="650" class="font-primary large-number text-accent-color">
        <tspan x="30" dy="0">300+</tspan>
        <tspan x="30" dy="90" class="font-secondary stat-label text-secondary-color">专业人才</tspan>
      </text>
    </g>

    <!-- Bottom Section: Additional Data Points / Call to Action (50px vertical spacing from chart area) -->
    <g id="bottom-info" transform="translate(0 750)">
      <text x="0" y="0" class="font-primary section-title text-primary-color">
        <tspan x="0" dy="0">未来展望与战略目标</tspan>
        <tspan x="0" dy="45" class="font-secondary body-text text-secondary-color">Future Outlook and Strategic Objectives</tspan>
      </text>

      <rect x="0" y="100" width="1760" height="100" rx="12" class="container-bg" stroke="#BAE6FD" stroke-width="1" />
      <text x="30" y="150" class="font-primary body-text text-primary-color">
        <tspan x="30" dy="0">
          我们致力于持续创新和全球扩张，目标在未来三年内实现业绩翻番，并进一步提升市场竞争力。
        </tspan>
        <tspan x="30" dy="35" class="font-secondary small-text text-secondary-color">
          We are committed to continuous innovation 和#38; global expansion, aiming to double performance in the next three years.
        </tspan>
      </text>
    </g>
  </g>
</svg>