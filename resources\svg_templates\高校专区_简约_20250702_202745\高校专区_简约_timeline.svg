<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景色 -->
  <rect width="1920" height="1080" fill="#F8FAFC"/>

  <defs>
    <!-- CSS 样式定义 -->
    <style type="text/css">
      /* 颜色变量 */
      .primary-color { fill: #3B82F6; }
      .secondary-color { fill: #7DD3FC; }
      .accent-color { fill: #BAE6FD; }
      .background-color { fill: #F8FAFC; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border-color { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }

      /* 字体定义 */
      .font-inter { font-family: 'Inter', Helvetica, Arial, sans-serif; }
      .font-sf-pro { font-family: 'SF Pro Display', system-ui, sans-serif; }
      .font-poppins { font-family: 'Poppins', sans-serif; }

      /* 字体大小和粗细 */
      .hero-title { font-size: 72px; font-weight: 700; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; } /* bold */
      .section-title { font-size: 36px; font-weight: 700; } /* bold */
      .content-title { font-size: 28px; font-weight: 600; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; } /* normal */

      /* 时间轴特定样式 */
      .timeline-line { stroke: #3B82F6; stroke-width: 4; stroke-linecap: round; }
      .timeline-node { fill: #3B82F6; stroke: #F8FAFC; stroke-width: 4; }
      .milestone-node { fill: #7DD3FC; stroke: #3B82F6; stroke-width: 6; }
      .connector-line { stroke: #BAE6FD; stroke-width: 2; }

      /* 卡片样式 */
      .card {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1;
      }
    </style>

    <!-- 线性渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#7DD3FC" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#BAE6FD" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
  </defs>

  <!-- 顶部 Logo 占位符 -->
  <g transform="translate(80, 60)">
    <rect x="0" y="0" width="120" height="40" fill="#BAE6FD" rx="5"/>
    <text x="60" y="25" text-anchor="middle" class="small-text text-primary-color font-inter">
      <tspan>LOGO</tspan>
    </text>
    <!-- 实际 Logo 图片可以使用以下标签替换： -->
    <!-- <image href="{logo_url}" x="0" y="0" width="120" height="40"/> -->
  </g>

  <!-- 头部区域：标题和副标题 -->
  <text x="960" y="150" text-anchor="middle" class="section-title text-primary-color font-poppins">
    <tspan>{title}</tspan>
  </text>
  <text x="960" y="195" text-anchor="middle" class="body-text text-secondary-color font-inter">
    <tspan>{subtitle}</tspan>
  </text>

  <!-- 时间轴主线 -->
  <line x1="960" y1="280" x2="960" y2="980" class="timeline-line"/>

  <!-- 时间轴事件点 -->

  <!-- 事件 1: 左侧，里程碑标注 -->
  <g>
    <circle cx="960" cy="350" r="12" class="milestone-node"/>
    <line x1="960" y1="350" x2="680" y2="350" class="connector-line"/>
    <rect x="180" y="280" width="480" height="140" rx="0" class="card"/>
    <text x="420" y="325" text-anchor="middle" class="main-title text-primary-color font-sf-pro">
      <tspan>{date_1}</tspan>
    </text>
    <text x="420" y="365" text-anchor="middle" class="content-title text-primary-color font-poppins">
      <tspan>{title_1}</tspan>
    </text>
    <text x="420" y="395" text-anchor="middle" class="small-text text-secondary-color font-inter">
      <tspan>{content_1}</tspan>
    </text>
  </g>

  <!-- 事件 2: 右侧 -->
  <g>
    <circle cx="960" cy="520" r="10" class="timeline-node"/>
    <line x1="960" y1="520" x2="1240" y2="520" class="connector-line"/>
    <rect x="1260" y="450" width="480" height="140" rx="0" class="card"/>
    <text x="1500" y="495" text-anchor="middle" class="main-title text-primary-color font-sf-pro">
      <tspan>{date_2}</tspan>
    </text>
    <text x="1500" y="535" text-anchor="middle" class="content-title text-primary-color font-poppins">
      <tspan>{title_2}</tspan>
    </text>
    <text x="1500" y="565" text-anchor="middle" class="small-text text-secondary-color font-inter">
      <tspan>{content_2}</tspan>
    </text>
  </g>

  <!-- 事件 3: 左侧 -->
  <g>
    <circle cx="960" cy="690" r="10" class="timeline-node"/>
    <line x1="960" y1="690" x2="680" y2="690" class="connector-line"/>
    <rect x="180" y="620" width="480" height="140" rx="0" class="card"/>
    <text x="420" y="665" text-anchor="middle" class="main-title text-primary-color font-sf-pro">
      <tspan>{date_3}</tspan>
    </text>
    <text x="420" y="705" text-anchor="middle" class="content-title text-primary-color font-poppins">
      <tspan>{title_3}</tspan>
    </text>
    <text x="420" y="735" text-anchor="middle" class="small-text text-secondary-color font-inter">
      <tspan>{content_3}</tspan>
    </text>
  </g>

  <!-- 事件 4: 右侧，里程碑标注 -->
  <g>
    <circle cx="960" cy="860" r="12" class="milestone-node"/>
    <line x1="960" y1="860" x2="1240" y2="860" class="connector-line"/>
    <rect x="1260" y="790" width="480" height="140" rx="0" class="card"/>
    <text x="1500" y="835" text-anchor="middle" class="main-title text-primary-color font-sf-pro">
      <tspan>{date_4}</tspan>
    </text>
    <text x="1500" y="875" text-anchor="middle" class="content-title text-primary-color font-poppins">
      <tspan>{title_4}</tspan>
    </text>
    <text x="1500" y="905" text-anchor="middle" class="small-text text-secondary-color font-inter">
      <tspan>{content_4}</tspan>
    </text>
  </g>

  <!-- 背景装饰元素 -->
  <circle cx="1700" cy="150" r="80" fill="#BAE6FD" opacity="0.3"/>
  <circle cx="220" cy="950" r="60" fill="#7DD3FC" opacity="0.2"/>
  <rect x="1600" y="900" width="100" height="100" fill="#3B82F6" opacity="0.1" transform="rotate(45 1650 950)"/>
</svg>