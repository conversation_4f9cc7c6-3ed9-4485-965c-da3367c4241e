<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style type="text/css">
      /* Color Palette */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #3B82F6; }
      .secondary-color { fill: #7DD3FC; }
      .accent-color { fill: #BAE6FD; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }
      .divider-color { stroke: #BAE6FD; }

      /* Font System */
      .font-inter { font-family: 'Inter', Helvetica, Arial, sans-serif; }
      .font-sf-pro { font-family: 'SF Pro Display', system-ui, sans-serif; }
      .font-poppins { font-family: 'Poppins', sans-serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* semibold */
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; } /* semibold */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Specific styles */
      .card-style {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
      }
      .outline-icon {
        stroke: #3B82F6;
        stroke-width: 2;
        fill: none;
      }
      .progress-bar-bg { fill: #E0F2FE; }
      .progress-bar-fill { fill: #3B82F6; }

      /* Gradients (for fill property, not for stroke) */
      .gradient-primary-fill {
        fill: url(#primaryGradient);
      }
      .gradient-accent-fill {
        fill: url(#accentGradient);
      }
      .text-gradient-fill {
        fill: url(#textGradient);
      }

      /* Hover effects (for static SVG, these are illustrative and won't animate without JS) */
      .chapter-item:hover .chapter-title {
        fill: #3B82F6; /* Primary color on hover */
      }
      .chapter-item:hover .chapter-number {
        fill: #3B82F6;
      }
      .chapter-item:hover .chapter-line {
        stroke: #3B82F6;
      }

    </style>

    <!-- Gradients for fills -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7DD3FC;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#BAE6FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>

    <!-- Placeholder for academic icon (e.g., a book or graduation cap) -->
    <g id="icon-book">
      <path class="outline-icon" d="M12 2L2 6v12l10 4 10-4V6L12 2zM12 4.47l8 3.2V17.53l-8 3.2-8-3.2V7.67l8-3.2z" />
      <polyline class="outline-icon" points="12 4.47 12 20.73 4 17.53 4 7.67 12 4.47" />
      <line class="outline-icon" x1="12" y1="9" x2="12" y2="18" />
      <line class="outline-icon" x1="16" y1="7" x2="16" y2="16" />
      <line class="outline-icon" x1="8" y1="7" x2="8" y2="16" />
    </g>

    <!-- Placeholder for research icon (beaker/flask) -->
    <g id="icon-research">
      <path class="outline-icon" d="M14 2H10L4 14H18L14 2Z" />
      <path class="outline-icon" d="M2 18H22" />
      <path class="outline-icon" d="M22 18L20 22H4L2 18" />
      <circle class="outline-icon" cx="12" cy="10" r="2" />
    </g>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Main Layout Container -->
  <g id="main-layout">
    <!-- Header Section -->
    <g id="header">
      <!-- Logo Placeholder -->
      <rect x="80" y="60" width="160" height="40" class="primary-color" />
      <text x="90" y="87" class="small-text font-inter card-background" text-anchor="start">
        <tspan>LOGO {logo_url}</tspan>
      </text>

      <!-- Main Title -->
      <text x="960" y="180" class="hero-title text-primary font-inter" text-anchor="middle">
        <tspan>{title}</tspan>
      </text>
      <text x="960" y="240" class="section-title text-secondary font-poppins" text-anchor="middle">
        <tspan>学术会议目录 和 章节导航</tspan>
      </text>
    </g>

    <!-- Content Area -->
    <g id="content-area">
      <!-- Left Column: Chapter Navigation -->
      <g id="chapter-navigation" transform="translate(240, 360)">
        <text x="0" y="0" class="content-title text-primary font-inter">
          <tspan>目录总览</tspan>
        </text>

        <!-- Chapter 1 -->
        <g class="chapter-item">
          <text x="0" y="60" class="body-text text-secondary font-sf-pro chapter-number">
            <tspan>01.</tspan>
          </text>
          <text x="60" y="60" class="body-text text-primary font-inter chapter-title">
            <tspan>引言：研究背景和意义</tspan>
          </text>
          <line x1="0" y1="80" x2="400" y2="80" class="divider-color chapter-line" stroke-width="1" />
        </g>

        <!-- Chapter 2 (Current) -->
        <g class="chapter-item">
          <text x="0" y="120" class="body-text primary-color font-sf-pro chapter-number">
            <tspan>02.</tspan>
          </text>
          <text x="60" y="120" class="body-text primary-color font-inter chapter-title">
            <tspan>理论框架 和 方法论探讨</tspan>
          </text>
          <line x1="0" y1="140" x2="400" y2="140" class="primary-color chapter-line" stroke-width="2" />
          <text x="410" y="120" class="small-text text-light font-inter">
            <tspan>当前章节</tspan>
          </text>
        </g>

        <!-- Chapter 3 -->
        <g class="chapter-item">
          <text x="0" y="180" class="body-text text-secondary font-sf-pro chapter-number">
            <tspan>03.</tspan>
          </text>
          <text x="60" y="180" class="body-text text-primary font-inter chapter-title">
            <tspan>实验设计 和 数据分析</tspan>
          </text>
          <line x1="0" y1="200" x2="400" y2="200" class="divider-color chapter-line" stroke-width="1" />
        </g>

        <!-- Chapter 4 -->
        <g class="chapter-item">
          <text x="0" y="240" class="body-text text-secondary font-sf-pro chapter-number">
            <tspan>04.</tspan>
          </text>
          <text x="60" y="240" class="body-text text-primary font-inter chapter-title">
            <tspan>研究结果 和 讨论</tspan>
          </text>
          <line x1="0" y1="260" x2="400" y2="260" class="divider-color chapter-line" stroke-width="1" />
        </g>

        <!-- Chapter 5 -->
        <g class="chapter-item">
          <text x="0" y="300" class="body-text text-secondary font-sf-pro chapter-number">
            <tspan>05.</tspan>
          </text>
          <text x="60" y="300" class="body-text text-primary font-inter chapter-title">
            <tspan>结论、局限性 和 未来展望</tspan>
          </text>
          <line x1="0" y1="320" x2="400" y2="320" class="divider-color chapter-line" stroke-width="1" />
        </g>

      </g>

      <!-- Right Column: Content Overview Card -->
      <g id="content-overview" transform="translate(800, 360)">
        <rect x="0" y="0" width="800" height="500" rx="0" ry="0" class="card-style" />
        <text x="40" y="60" class="section-title text-primary font-inter">
          <tspan>本章概述</tspan>
        </text>
        <text x="40" y="100" class="body-text text-secondary font-poppins">
          <tspan>Chapter Overview</tspan>
        </text>

        <!-- Placeholder for content description -->
        <text x="40" y="160" class="body-text text-secondary font-inter">
          <tspan>本章深入探讨了研究的理论框架和所采用的方法论。</tspan>
          <tspan x="40" dy="30">我们详细阐述了概念模型、变量定义以及数据收集</tspan>
          <tspan x="40" dy="30">和分析的具体流程。通过严谨的方法学设计，</tspan>
          <tspan x="40" dy="30">确保研究的科学性和可靠性。</tspan>
          <tspan x="40" dy="50">This chapter delves into the theoretical framework</tspan>
          <tspan x="40" dy="30">and methodology employed in the research. We detail</tspan>
          <tspan x="40" dy="30">the conceptual models, variable definitions, and the</tspan>
          <tspan x="40" dy="30">specific processes for data collection and analysis.</tspan>
          <tspan x="40" dy="30">Rigorous methodological design ensures the scientific</tspan>
          <tspan x="40" dy="30">rigor and reliability of the study. {content}</tspan>
        </text>

        <!-- Decorative element / Icon -->
        <use xlink:href="#icon-research" transform="translate(680, 40) scale(1.5)" />

      </g>
    </g>

    <!-- Progress Indicator and Page Number -->
    <g id="progress-footer">
      <!-- Progress Bar -->
      <rect x="80" y="940" width="1760" height="10" rx="5" ry="5" class="progress-bar-bg" />
      <!-- Current progress (e.g., 20% for page 2 of 10) -->
      <rect x="80" y="940" width="352" height="10" rx="5" ry="5" class="primary-color" />
      <text x="80" y="920" class="small-text text-secondary font-inter">
        <tspan>进度: 20%</tspan>
      </text>

      <!-- Page Number -->
      <text x="1840" y="920" class="body-text text-primary font-inter" text-anchor="end">
        <tspan>页码: 2 / 10</tspan>
      </text>

      <!-- Decorative line -->
      <line x1="80" y1="980" x2="1840" y2="980" class="divider-color" stroke-width="1" />
    </g>

    <!-- Decorative elements (minimalist, geometric, blue tones) -->
    <g id="decorative-elements">
      <!-- Top-right accent shape -->
      <rect x="1600" y="60" width="240" height="80" class="accent-color" opacity="0.3" />
      <rect x="1580" y="40" width="240" height="80" class="accent-color" opacity="0.1" />

      <!-- Bottom-left subtle shape -->
      <circle cx="120" cy="880" r="40" class="secondary-color" opacity="0.2" />
      <circle cx="160" cy="900" r="20" class="accent-color" opacity="0.4" />

      <!-- Geometric pattern near title -->
      <path d="M960 270 L930 300 L990 300 Z" class="primary-color" opacity="0.15" />
      <path d="M960 280 L940 310 L980 310 Z" class="secondary-color" opacity="0.1" />
    </g>

  </g>
</svg>