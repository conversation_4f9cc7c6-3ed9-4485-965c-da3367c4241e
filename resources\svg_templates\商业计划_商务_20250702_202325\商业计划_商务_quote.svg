<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- CSS Styles: 严格禁止使用 & 符号 -->
    <style type="text/css">
      /* Color Palette */
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }

      /* Font Styles */
      /* 字体系统: 使用指定的字体和字重 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .hero-title-style { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title-style { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title-style { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title-style { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text-style { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text-style { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text-style { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Quote specific styles */
      .quote-mark-style { fill: #3B82F6; opacity: 0.1; } /* 强调色结合透明度 */
      .quote-text-main {
        fill: #1E293B; /* text_primary */
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-weight: 700; /* bold */
        font-size: 56px; /* main_title */
        text-anchor: middle;
      }
      .quote-text-en {
        fill: #475569; /* secondary_color */
        font-family: 'Segoe UI', sans-serif;
        font-weight: 400; /* normal */
        font-size: 28px; /* content_title */
        text-anchor: middle;
      }
      .source-text-style {
        fill: #64748B; /* text_secondary */
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-weight: 400; /* normal */
        font-size: 22px; /* body_text */
        text-anchor: middle;
      }
      .logo-text-style {
        fill: #1E40AF; /* primary_color */
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-weight: 700;
        font-size: 28px; /* content_title */
      }
    </style>

    <!-- Gradients: 运用高亮色自身透明度渐变制造科技感 -->
    <linearGradient id="gradientAccent" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <!-- 引号符号的装饰性设计: 简洁的勾线图形化 -->
    <!-- 使用简单的path路径模拟引号，确保其为矢量图形 -->
    <path id="quoteOpen" d="M30 0C13.431 0 0 13.431 0 30V100C0 116.569 13.431 130 30 130H60C76.569 130 90 116.569 90 100V70C90 53.431 76.569 40 60 40H30V0ZM120 0C103.431 0 90 13.431 90 30V100C90 116.569 103.431 130 120 130H150C166.569 130 180 116.569 180 100V70C180 53.431 166.569 40 150 40H120V0Z" />
    <path id="quoteClose" d="M150 130C166.569 130 180 116.569 180 100V30C180 13.431 166.569 0 150 0H120C103.431 0 90 13.431 90 30V60C90 76.569 103.431 90 120 90H150V130ZM60 130C76.569 130 90 116.569 90 100V30C90 13.431 76.569 0 60 0H30C13.431 0 0 13.431 0 30V60C0 76.569 13.431 90 30 90H60V130Z" />

  </defs>

  <!-- 背景: 使用背景色 -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color" />

  <!-- 装饰元素: 几何图形装饰，渐变背景（低透明度）-->
  <!-- 左上角抽象形状 -->
  <path d="M0 0H400C422.091 0 440 17.9086 440 40V200C440 222.091 422.091 240 400 240H0V0Z" class="primary-color" opacity="0.05"/>
  <path d="M0 0H360C382.091 0 400 17.9086 400 40V160C400 182.091 382.091 200 360 200H0V0Z" class="accent-color" opacity="0.08"/>

  <!-- 右下角抽象形状 -->
  <path d="M1920 1080H1520C1497.91 1080 1480 1062.09 1480 1040V880C1480 857.909 1497.91 840 1520 840H1920V1080Z" class="primary-color" opacity="0.05"/>
  <path d="M1920 1080H1560C1537.91 1080 1520 1062.09 1520 1040V920C1520 897.909 1537.91 880 1560 880H1920V1080Z" class="accent-color" opacity="0.08"/>

  <!-- 主内容区域 -->
  <!-- 引号符号: 超大视觉元素强调重点，与小元素的比例形成反差 -->
  <use href="#quoteOpen" transform="translate(480 320) scale(1.5)" class="quote-mark-style" />
  <use href="#quoteClose" transform="translate(1260 560) scale(1.5)" class="quote-mark-style" />

  <!-- 引用内容: 引用内容的突出显示，中英文混用，中文大字体粗体，英文小字作为点缀 -->
  <text x="960" y="480" class="quote-text-main">
    <tspan x="960" y="480">{title}</tspan>
    <tspan x="960" y="550" class="quote-text-en">"The future belongs to those who believe</tspan>
    <tspan x="960" y="600" class="quote-text-en">in the beauty of their dreams."</tspan>
  </text>

  <!-- 来源信息: 来源信息的适当位置 -->
  <text x="960" y="700" class="source-text-style">
    <tspan x="960" y="700">— {subtitle}</tspan>
  </text>

  <!-- 底部装饰线: 渐变分割线 -->
  <rect x="760" y="780" width="400" height="4" rx="2" fill="url(#gradientAccent)" />

  <!-- Logo 占位符 (左上角) -->
  <text x="80" y="100" class="logo-text-style">
    <tspan x="80" y="100">{logo_url}</tspan>
  </text>

  <!-- 页码 (右下角) -->
  <text x="1840" y="1020" text-anchor="end" class="caption-text-style text-light">
    <tspan x="1840" y="1020">9 / 10</tspan>
  </text>

</svg>