<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义：强调色和主色的混合 -->
    <linearGradient id="gradientAccentPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#1E40AF" stop-opacity="0.6"/>
    </linearGradient>

    <!-- 渐变定义：主色和辅助色的混合 -->
    <linearGradient id="gradientPrimarySecondary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" stop-opacity="0.7"/>
      <stop offset="100%" stop-color="#475569" stop-opacity="0.5"/>
    </linearGradient>

    <!-- 渐变定义：纯强调色透明度渐变，用于科技感高亮 -->
    <linearGradient id="gradientHighlight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.4"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.05"/>
    </linearGradient>

    <!-- 医疗十字图标 (简化轮廓) -->
    <symbol id="iconMedicalCross" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM17 13H13V17H11V13H7V11H11V7H13V11H17V13Z" fill="#3B82F6" opacity="0.1"/>
    </symbol>

    <!-- 大脑图标 (简化轮廓) -->
    <symbol id="iconBrain" viewBox="0 0 24 24">
      <path d="M12 2C7.3 2 4 5.3 4 10C4 15 12 22 12 22C12 22 20 15 20 10C20 5.3 16.7 2 12 2ZM12 4C14.76 4 17 6.24 17 9C17 11.76 12 18 12 18C12 18 7 11.76 7 9C7 6.24 9.24 4 12 4ZM10 10L10 8C10 7.45 9.55 7 9 7C8.45 7 8 7.45 8 8L8 10C8 10.55 8.45 11 9 11C9.55 11 10 10.55 10 10ZM14 10L14 8C14 7.45 13.55 7 13 7C12.45 7 12 7.45 12 8L12 10C12 10.55 12.45 11 13 11C13.55 11 14 10.55 14 10Z" fill="#1E40AF" opacity="0.05"/>
    </symbol>

    <!-- 装饰性左引号 -->
    <g id="quoteMarkOpen" fill="#3B82F6" opacity="0.15">
      <path d="M40 0C17.909 0 0 17.909 0 40V120H40V40H120V0H40Z"/>
    </g>
    <!-- 装饰性右引号 -->
    <g id="quoteMarkClose" fill="#3B82F6" opacity="0.15">
      <path d="M80 120C102.091 120 120 102.091 120 80V0H80V80H0V120H80Z"/>
    </g>

    <!-- 用于卡片阴影的滤镜 -->
    <filter id="shadowBlur">
      <feGaussianBlur in="SourceGraphic" stdDeviation="10"/>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="#F8FAFC"/>

  <!-- 装饰性背景元素 (微妙的几何图形) -->
  <circle cx="1700" cy="150" r="100" fill="url(#gradientHighlight)"/>
  <rect x="1600" y="800" width="200" height="200" rx="20" fill="url(#gradientPrimarySecondary)" transform="rotate(15 1600 800)"/>
  <circle cx="200" cy="900" r="120" fill="url(#gradientAccentPrimary)" opacity="0.1"/>
  <rect x="100" y="100" width="150" height="150" rx="15" fill="#1E40AF" opacity="0.05" transform="rotate(-10 100 100)"/>

  <!-- Logo 占位符 (左上角) -->
  <rect x="80" y="60" width="200" height="50" fill="#E0F2FE" rx="8"/>
  <text x="180" y="95" font-family="Microsoft YaHei, Segoe UI, sans-serif" font-size="22" font-weight="600" fill="#1E40AF" text-anchor="middle">
    {logo_url}
  </text>

  <!-- 主要内容区域 - 引用卡片 -->
  <g id="quoteCard">
    <!-- 阴影矩形 (模拟阴影效果) -->
    <rect x="250" y="190" width="1420" height="700" rx="24" fill="#1E293B" opacity="0.05" filter="url(#shadowBlur)"/>
    <!-- 卡片主体 -->
    <rect x="240" y="180" width="1440" height="720" rx="24" fill="#FFFFFF" stroke="#BAE6FD" stroke-width="1"/>
  </g>

  <!-- 卡片内部的装饰性引号 -->
  <use href="#quoteMarkOpen" x="300" y="240" transform="scale(0.8)"/>
  <use href="#quoteMarkClose" x="1500" y="700" transform="scale(0.8)"/>

  <!-- 引用文本 -->
  <text x="960" y="400" font-family="Microsoft YaHei, Segoe UI, sans-serif" font-size="56" font-weight="700" fill="#1E293B" text-anchor="middle">
    <tspan x="960" dy="0">{title}</tspan>
    <tspan x="960" dy="70">推动医学进步，</tspan>
    <tspan x="960" dy="70">改善人类健康。</tspan>
  </text>

  <!-- 副标题/来源信息 -->
  <text x="960" y="680" font-family="Source Han Sans CN, Noto Sans CJK SC, sans-serif" font-size="28" font-weight="400" fill="#475569" text-anchor="middle">
    <tspan x="960" dy="0">{subtitle}</tspan>
    <tspan x="960" dy="40">—— {author}，{date}</tspan>
  </text>

  <!-- 医疗/科学图标 (卡片内部的微妙背景元素) -->
  <use href="#iconMedicalCross" x="400" y="600" width="120" height="120"/>
  <use href="#iconBrain" x="1400" y="300" width="120" height="120"/>

  <!-- 页码 (右下角) -->
  <text x="1840" y="1020" font-family="Source Han Sans CN, Noto Sans CJK SC, sans-serif" font-size="16" fill="#94A3B8" text-anchor="end">
    9 / 10
  </text>
</svg>