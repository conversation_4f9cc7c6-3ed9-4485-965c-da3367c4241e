<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色，用于装饰性元素，遵循蓝色系配色 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <!-- 用于分隔线的渐变，制造科技感和透明度效果 -->
    <linearGradient id="gradientDivider" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#7DD3FC" stop-opacity="0"/>
      <stop offset="50%" stop-color="#7DD3FC" stop-opacity="0.5"/>
      <stop offset="100%" stop-color="#7DD3FC" stop-opacity="0"/>
    </linearGradient>
  </defs>

  <style>
    /* 全局样式和排版定义 */
    .bg-color {
      fill: #F8FAFC; /* 背景色 */
    }

    .text-primary-color {
      fill: #1E293B; /* 主要文字色 */
    }

    .text-secondary-color {
      fill: #64748B; /* 辅助文字色 */
    }

    .text-light-color {
      fill: #94A3B8; /* 浅色文字 */
    }

    .primary-color {
      fill: #3B82F6; /* 主色 */
    }

    .secondary-color {
      fill: #7DD3FC; /* 辅助色 */
    }

    .accent-color {
      fill: #BAE6FD; /* 强调色 */
    }

    /* 字体样式 */
    .font-primary {
      font-family: "Inter", "Helvetica", "Arial", sans-serif;
    }
    .font-secondary {
      font-family: "SF Pro Display", system-ui, sans-serif;
    }
    .font-accent {
      font-family: "Poppins", sans-serif;
    }

    /* 字体大小和字重 */
    .hero-title {
      font-size: 72px;
      font-weight: 700; /* bold */
      line-height: 1.1;
    }
    .main-title {
      font-size: 56px;
      font-weight: 700; /* bold */
      line-height: 1.1;
    }
    .section-title {
      font-size: 36px;
      font-weight: 600; /* semibold */
      line-height: 1.4;
    }
    .content-title {
      font-size: 28px;
      font-weight: 500; /* medium */
      line-height: 1.4;
    }
    .body-text {
      font-size: 22px;
      font-weight: 400; /* normal */
      line-height: 1.6;
    }
    .small-text {
      font-size: 16px;
      font-weight: 400;
      line-height: 1.6;
    }
    .caption {
      font-size: 14px;
      font-weight: 400;
      line-height: 1.6;
    }

    /* 文本对齐 */
    .text-center {
      text-anchor: middle;
    }
    .text-left {
      text-anchor: start;
    }
    .text-end {
      text-anchor: end;
    }
  </style>

  <!-- 页面背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- 装饰性元素 - 简约几何形状，带有透明度，与背景融合 -->
  <!-- 左上角装饰圆 -->
  <circle cx="150" cy="150" r="80" class="accent-color" opacity="0.1"/>
  <!-- 左上角装饰矩形 -->
  <rect x="80" y="80" width="100" height="100" class="secondary-color" opacity="0.05" transform="rotate(15 80 80)"/>

  <!-- 右下角装饰矩形 -->
  <rect x="1600" y="850" width="200" height="100" class="primary-color" opacity="0.05" transform="rotate(-10 1600 850)"/>
  <!-- 右下角装饰圆 -->
  <circle cx="1750" cy="900" r="120" class="accent-color" opacity="0.15"/>

  <!-- 顶部细分隔线，使用渐变效果 -->
  <rect x="480" y="400" width="960" height="2" fill="url(#gradientDivider)"/>

  <!-- 主要内容区域，居中显示 -->
  <!-- 通过transform将整个组的中心点移动到画布中心 (960, 540) -->
  <g transform="translate(960 540)" class="text-center">

    <!-- 大型装饰性起始引号 -->
    <!-- 位置相对于g组的中心点 (0,0) 进行调整，确保与引用内容视觉对齐 -->
    <text x="-480" y="-140" class="font-primary primary-color" font-size="200" font-weight="bold" opacity="0.2">“</text>

    <!-- 引用内容 - 核心强调部分 -->
    <!-- 文本块基于y=0（g组中心）并使用dominant-baseline: central，然后用dy调整行间距 -->
    <text x="0" y="0" class="font-accent main-title text-primary-color" style="dominant-baseline: central;">
      <!-- 中文大字体粗体，英文小字作为点缀 -->
      <tspan x="0" dy="-60">创新是引领发展的第一动力，</tspan>
      <tspan x="0" dy="60">是建设现代化经济体系的战略支撑。</tspan>
      <tspan x="0" dy="60" class="body-text text-secondary-color">Innovation drives growth, a strategic pillar.</tspan>
    </text>

    <!-- 大型装饰性结束引号 -->
    <!-- 位置相对于g组的中心点 (0,0) 进行调整，确保与引用内容视觉对齐 -->
    <text x="480" y="80" class="font-primary primary-color" font-size="200" font-weight="bold" opacity="0.2">”</text>

    <!-- 来源信息 -->
    <!-- 位置相对于引用内容进行定位，确保有足够的间距（至少50px） -->
    <text x="0" y="220" class="font-secondary content-title text-secondary-color">
      <tspan x="0" dy="0">— {author} </tspan>
      <tspan x="0" dy="40" class="small-text text-light-color"> {date} </tspan>
    </text>

  </g>

  <!-- Logo 占位符 (左上角，遵循页面边距) -->
  <g transform="translate(80 60)">
    <!-- 使用文本作为Logo占位符，可替换为实际图片 -->
    <text x="0" y="0" class="font-primary section-title primary-color">
      <tspan dy="30">{logo_url}</tspan>
    </text>
    <!-- Logo下方装饰线 -->
    <rect x="0" y="40" width="150" height="2" fill="#3B82F6"/>
  </g>

  <!-- 页面序号 (右下角，遵循页面边距) -->
  <!-- x = 1920 (画布宽) - 80 (右边距) = 1840 -->
  <!-- y = 1080 (画布高) - 60 (下边距) = 1020 -->
  <text x="1840" y="1020" class="font-secondary caption text-light-color text-end">
    9/10
  </text>
</svg>