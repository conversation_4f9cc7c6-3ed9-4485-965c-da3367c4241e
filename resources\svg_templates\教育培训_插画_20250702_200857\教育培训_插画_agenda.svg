<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- CSS Styles -->
    <style type="text/css">
      <![CDATA[
      /* Basic Reset and Global Styles */
      svg {
        font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* Colors */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }
      .info-color { fill: #0EA5E9; }

      /* Fonts */
      .font-hero-title { font-size: 72px; font-weight: 700; }
      .font-main-title { font-size: 56px; font-weight: 700; }
      .font-section-title { font-size: 36px; font-weight: 700; }
      .font-content-title { font-size: 28px; font-weight: 600; }
      .font-body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
      .font-small-text { font-size: 16px; font-weight: 400; }
      .font-caption { font-size: 14px; font-weight: 400; }
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }

      /* Layout Spacing */
      .margin-horizontal { transform: translateX(80px); }
      .margin-vertical-top { transform: translateY(60px); }

      /* Card Style */
      .card {
        rx: 12px;
        ry: 12px;
        stroke-width: 1px;
        filter: url(#shadow);
      }
      .card-shadow {
        filter: url(#shadow);
      }

      /* Icons */
      .icon-stroke {
        stroke: #4A86E8;
        stroke-width: 2px;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      /* Progress Bar */
      .progress-bar-bg { fill: #E0F2FE; }
      .progress-bar-fill { fill: #0EA5E9; }

      /* Decorative Elements */
      .wave-path {
          fill: #E0F2FE; /* container_background */
      }
      .circle-decoration-1 { fill: #4A86E8; opacity: 0.1; }
      .circle-decoration-2 { fill: #0EA5E9; opacity: 0.08; }

      ]]>
    </style>

    <!-- Drop Shadow Filter for Cards -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="dropShadow" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feBlend in="SourceGraphic" in2="dropShadow" mode="normal"/>
      <feOffset result="offOutSmall" in="SourceAlpha" dx="0" dy="2"/>
      <feGaussianBlur result="blurOutSmall" in="offOutSmall" stdDeviation="2"/>
      <feColorMatrix result="dropShadowSmall" in="blurOutSmall" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.06 0"/>
      <feBlend in="SourceGraphic" in2="dropShadowSmall" mode="normal"/>
    </filter>

    <!-- Accent Gradient for text/elements -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9"/>
      <stop offset="100%" stop-color="#4A86E8"/>
    </linearGradient>

    <!-- Primary Gradient for decorative elements -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- Icon: Book -->
    <symbol id="icon-book" viewBox="0 0 24 24">
      <path class="icon-stroke" d="M4 19.5V14.5M4 7V5C4 3.89543 4.89543 3 6 3H18C19.1046 3 20 3.89543 20 5V19.5M4 19.5H18C19.1046 19.5 20 20.3954 20 21.5C20 22.6046 19.1046 23.5 18 23.5H4C2.89543 23.5 2 22.6046 2 21.5C2 20.3954 2.89543 19.5 4 19.5ZM18 19.5V7"/>
      <path class="icon-stroke" d="M10 7L10 19.5"/>
    </symbol>

    <!-- Icon: Lightbulb -->
    <symbol id="icon-lightbulb" viewBox="0 0 24 24">
      <path class="icon-stroke" d="M9 18H15"/>
      <path class="icon-stroke" d="M12 22C13.1046 22 14 21.1046 14 20C14 18.8954 13.1046 18 12 18C10.8954 18 10 18.8954 10 20C10 21.1046 10.8954 22 12 22Z"/>
      <path class="icon-stroke" d="M12 18V16"/>
      <path class="icon-stroke" d="M12 16C15.866 16 19 12.866 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 12.866 8.13401 16 12 16Z"/>
    </symbol>

    <!-- Icon: Target -->
    <symbol id="icon-target" viewBox="0 0 24 24">
      <path class="icon-stroke" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"/>
      <path class="icon-stroke" d="M12 19C15.866 19 19 15.866 19 12C19 8.13401 15.866 5 12 5C8.13401 5 5 8.13401 5 12C5 15.866 8.13401 19 12 19Z"/>
      <path class="icon-stroke" d="M12 16C13.6569 16 15 14.6569 15 13C15 11.3431 13.6569 10 12 10C10.3431 10 9 11.3431 9 13C9 14.6569 10.3431 16 12 16Z"/>
    </symbol>

    <!-- Icon: Chat Bubble -->
    <symbol id="icon-chat" viewBox="0 0 24 24">
      <path class="icon-stroke" d="M21 15C21 16.6569 19.6569 18 18 18H7L3 21V6C3 4.34315 4.34315 3 6 3H18C19.6569 3 21 4.34315 21 6V15Z"/>
    </symbol>

    <!-- Icon: Graduation Cap -->
    <symbol id="icon-gradcap" viewBox="0 0 24 24">
      <path class="icon-stroke" d="M22 10L12 5L2 10L12 15L22 10Z"/>
      <path class="icon-stroke" d="M6 12L2 10"/>
      <path class="icon-stroke" d="M18 12L22 10"/>
      <path class="icon-stroke" d="M12 15V22"/>
      <path class="icon-stroke" d="M5 19L12 22L19 19"/>
    </symbol>

  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative Elements - Subtle Waves and Circles -->
  <g opacity="0.4">
    <path class="wave-path" d="M0 800 C 400 700, 800 900, 1200 800 C 1600 700, 1920 900, 1920 1080 L 0 1080 Z"/>
    <circle cx="1700" cy="150" r="100" class="circle-decoration-1"/>
    <circle cx="200" cy="900" r="80" class="circle-decoration-2"/>
    <circle cx="960" cy="100" r="60" class="circle-decoration-1"/>
  </g>

  <!-- Header Section -->
  <g class="margin-horizontal">
    <!-- Logo Placeholder (Top Left) -->
    <text x="80" y="80" class="font-content-title text-primary font-bold">
      <tspan fill="url(#primaryGradient)">Edu</tspan>
      <tspan fill="#1E293B">Learn</tspan>
    </text>

    <!-- Page Number (Top Right) -->
    <text x="1760" y="80" text-anchor="end" class="font-small-text text-secondary">
      <tspan class="font-bold accent-color">2</tspan>
      <tspan>/10</tspan>
    </text>
  </g>

  <!-- Main Content - Directory Structure -->
  <g transform="translate(80, 160)">
    <!-- Main Title -->
    <text x="880" y="0" text-anchor="middle" class="font-main-title text-primary">
      <tspan x="880" dy="0">课程目录和学习路线</tspan>
      <tspan x="880" dy="70" class="font-content-title text-secondary">Course Catalog and Learning Path</tspan>
    </text>

    <!-- Directory Cards (Bento Grid Style Interpretation) -->
    <g transform="translate(0, 180)">
      <!-- Card 1: 基础概念 -->
      <rect x="0" y="0" width="550" height="250" class="card card-background card-border"/>
      <use xlink:href="#icon-book" x="30" y="30" width="48" height="48"/>
      <text x="100" y="60" class="font-section-title accent-color">01</text>
      <text x="100" y="100" class="font-content-title text-primary font-bold">基础概念</text>
      <text x="100" y="135" class="font-small-text text-secondary">
        <tspan x="100" dy="0">掌握核心原理和基本知识</tspan>
        <tspan x="100" dy="25">Fundamentals and Core Principles</tspan>
      </text>
      <text x="100" y="200" class="font-caption text-light">3个章节和12个知识点</text>

      <!-- Card 2: 深入实践 -->
      <rect x="600" y="0" width="550" height="250" class="card card-background card-border"/>
      <use xlink:href="#icon-lightbulb" x="630" y="30" width="48" height="48"/>
      <text x="700" y="60" class="font-section-title accent-color">02</text>
      <text x="700" y="100" class="font-content-title text-primary font-bold">深入实践</text>
      <text x="700" y="135" class="font-small-text text-secondary">
        <tspan x="700" dy="0">通过案例提升实战能力</tspan>
        <tspan x="700" dy="25">Practical Applications and Cases</tspan>
      </text>
      <text x="700" y="200" class="font-caption text-light">5个章节和18个案例</text>

      <!-- Card 3: 技能精进 -->
      <rect x="1200" y="0" width="550" height="250" class="card card-background card-border"/>
      <use xlink:href="#icon-target" x="1230" y="30" width="48" height="48"/>
      <text x="1300" y="60" class="font-section-title accent-color">03</text>
      <text x="1300" y="100" class="font-content-title text-primary font-bold">技能精进</text>
      <text x="1300" y="135" class="font-small-text text-secondary">
        <tspan x="1300" dy="0">掌握高级技巧和专业技能</tspan>
        <tspan x="1300" dy="25">Advanced Techniques and Skills</tspan>
      </text>
      <text x="1300" y="200" class="font-caption text-light">4个章节和10项挑战</text>

      <!-- Card 4: 互动和答疑 -->
      <rect x="0" y="300" width="550" height="250" class="card card-background card-border"/>
      <use xlink:href="#icon-chat" x="30" y="330" width="48" height="48"/>
      <text x="100" y="360" class="font-section-title accent-color">04</text>
      <text x="100" y="400" class="font-content-title text-primary font-bold">互动和答疑</text>
      <text x="100" y="435" class="font-small-text text-secondary">
        <tspan x="100" dy="0">在线交流和疑难解答</tspan>
        <tspan x="100" dy="25">Live Q和#38;A and Discussions</tspan>
      </text>
      <text x="100" y="500" class="font-caption text-light">每周直播和专属答疑</text>

      <!-- Card 5: 评估和反馈 -->
      <rect x="600" y="300" width="550" height="250" class="card card-background card-border"/>
      <use xlink:href="#icon-gradcap" x="630" y="330" width="48" height="48"/>
      <text x="700" y="360" class="font-section-title accent-color">05</text>
      <text x="700" y="400" class="font-content-title text-primary font-bold">评估和反馈</text>
      <text x="700" y="435" class="font-small-text text-secondary">
        <tspan x="700" dy="0">个性化评估和学习建议</tspan>
        <tspan x="700" dy="25">Personalized Assessment and Feedback</tspan>
      </text>
      <text x="700" y="500" class="font-caption text-light">结业测试和证书</text>

      <!-- Card 6: 资源下载 -->
      <rect x="1200" y="300" width="550" height="250" class="card card-background card-border"/>
      <use xlink:href="#icon-book" x="1230" y="330" width="48" height="48"/>
      <text x="1300" y="360" class="font-section-title accent-color">06</text>
      <text x="1300" y="400" class="font-content-title text-primary font-bold">资源下载</text>
      <text x="1300" y="435" class="font-small-text text-secondary">
        <tspan x="1300" dy="0">学习资料和辅助工具</tspan>
        <tspan x="1300" dy="25">Downloadable Resources and Tools</tspan>
      </text>
      <text x="1300" y="500" class="font-caption text-light">PDF讲义和代码示例</text>
    </g>
  </g>

  <!-- Progress Indicator (Bottom) -->
  <g transform="translate(0, 960)">
    <rect x="180" y="0" width="1560" height="8" class="progress-bar-bg" rx="4" ry="4"/>
    <!-- Current progress is 2/10, so 20% of 1560 = 312 -->
    <rect x="180" y="0" width="312" height="8" class="progress-bar-fill" rx="4" ry="4"/>
    <circle cx="180" cy="4" r="10" class="progress-bar-fill"/>
    <circle cx="180 + 312" cy="4" r="10" class="progress-bar-fill"/>

    <text x="180" y="40" class="font-small-text text-secondary">章节 1/10</text>
    <text x="180 + 312" y="40" text-anchor="middle" class="font-small-text text-secondary">章节 2/10</text>
    <text x="1740" y="40" text-anchor="end" class="font-small-text text-secondary">章节 10/10</text>
  </g>

  <!-- Placeholder for dynamic content -->
  <text x="960" y="1020" text-anchor="middle" class="font-small-text text-secondary">
    <tspan>{date} - {author}</tspan>
  </text>

</svg>