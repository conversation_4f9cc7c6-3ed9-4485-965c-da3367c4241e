.mcp-settings {
  padding: 1rem;
}

.mcp-settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.mcp-settings-description {
  margin-bottom: 1.5rem;
  display: block;
}

.mcp-settings-table {
  margin-top: 1.5rem;
}

/* 表格样式优化 */
.mcp-settings .ant-table {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

.mcp-settings .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #1f1f1f;
  border-bottom: 2px solid #f0f0f0;
}

.mcp-settings .ant-table-tbody > tr > td {
  padding: 12px 16px;
  vertical-align: middle;
}

.mcp-settings .ant-table-tbody > tr:hover > td {
  background-color: #fafafa;
}

/* 表格单元格内容换行 */
.mcp-settings .ant-table-cell {
  word-break: break-word;
  white-space: normal;
}

/* 配置列特殊处理 */
.mcp-settings .ant-table-tbody > tr > td:nth-child(5) {
  max-width: 400px;
  min-width: 300px;
}

/* 配置显示样式 */
.mcp-settings .ant-table-tbody .ant-typography-code {
  font-size: 12px;
  padding: 1px 4px;
  margin: 0 2px;
  background-color: #f6f6f6;
  border: 1px solid #e8e8e8;
  white-space: normal;
  word-break: break-all;
}

/* 确保整个表格布局合理 */
.mcp-settings .ant-table-wrapper {
  width: 100%;
}

.mcp-settings .ant-table table {
  table-layout: fixed;
  width: 100%;
}

/* 状态标记样式 */
.mcp-settings .ant-badge-status {
  display: inline-flex;
  align-items: center;
}

.mcp-settings .ant-badge-status-text {
  font-size: 13px;
  margin-left: 6px;
}

/* 操作按钮样式 */
.mcp-settings .ant-btn-text {
  padding: 2px 6px;
  height: auto;
}

.mcp-settings .ant-switch {
  min-width: 56px;
}

/* 标签样式 */
.mcp-settings .ant-tag {
  font-size: 12px;
  padding: 0 8px;
  border-radius: 4px;
}

/* 新增样式 */
.mcp-config-editor {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.mcp-config-status-bar {
  background-color: #f5f5f5;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e8e8e8;
}

.mcp-config-error {
  margin-bottom: 16px;
  animation: fadeIn 0.3s;
}

.mcp-config-valid {
  color: #52c41a;
  display: flex;
  align-items: center;
  gap: 6px;
}

.mcp-config-invalid {
  color: #ff4d4f;
  display: flex;
  align-items: center;
  gap: 6px;
}

.mcp-config-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  background-color: rgba(255, 255, 255, 0.8);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* DXT相关样式 */
.mcp-settings .source-tag {
  font-size: 12px;
  border-radius: 4px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.mcp-settings .source-tag-manual {
  background-color: #f0f0f0;
  color: #666;
  border-color: #e0e0e0;
}

.mcp-settings .source-tag-marketplace {
  background-color: #e6f4ff;
  color: #1890ff;
  border-color: #b3d8ff;
}

.mcp-settings .source-tag-dxt {
  background-color: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

/* DXT上传Modal样式优化 */
.ant-modal .dxt-upload-container {
  padding: 16px 0;
}

/* 操作按钮的悬停提示 */
.mcp-settings .ant-btn[disabled] {
  opacity: 0.5;
}

/* 来源列图标对齐 */
.mcp-settings .source-tag .anticon {
  font-size: 12px;
}