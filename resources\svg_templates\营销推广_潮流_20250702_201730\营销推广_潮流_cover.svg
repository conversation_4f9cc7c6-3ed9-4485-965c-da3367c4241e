<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色，用于背景、文本和装饰元素 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9"/>
      <stop offset="100%" stop-color="#06B6D4"/>
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
  </defs>

  <!-- CSS样式定义，严格遵守不使用 & 符号的规则 -->
  <style>
    /* 字体族定义 */
    .font-primary {
      font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    }
    .font-secondary {
      font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif;
    }
    .font-accent {
      font-family: "Times New Roman", serif;
    }

    /* 颜色定义 */
    .bg-color-background { fill: #F8FAFC; }
    .color-primary { fill: #4A86E8; }
    .color-secondary { fill: #3B82F6; }
    .color-accent { fill: #0EA5E9; }
    .color-text-primary { fill: #1E293B; }
    .color-text-secondary { fill: #64748B; }

    /* 字体大小和字重定义 */
    .hero-title {
      font-size: 72px;
      font-weight: 700; /* bold */
    }
    .main-title {
      font-size: 56px;
      font-weight: 700;
    }
    .section-title {
      font-size: 36px;
      font-weight: 600; /* semibold */
    }
    .body-text {
      font-size: 22px;
      font-weight: 400; /* normal */
    }
    .small-text {
      font-size: 16px;
      font-weight: 400;
    }
    .caption {
      font-size: 14px;
      font-weight: 400;
    }

    /* 通用文本样式，确保行高和锚点 */
    text {
      line-height: 1.4; /* 标准行高，防止多行文本重叠 */
    }

    /* 装饰元素样式 */
    .decorative-shape {
      opacity: 0.1; /* 柔和的背景装饰形状 */
    }
    .decorative-line {
      stroke: url(#primaryGradient);
      stroke-width: 2px;
      fill: none;
      opacity: 0.2; /* 细线效果，用于营造科技感 */
    }
    .highlight-element {
      fill: url(#accentGradient);
      opacity: 0.05; /* 大型图标或数字的极低透明度，作为背景视觉焦点 */
    }
  </style>

  <!-- 背景层：使用背景色填充整个画布 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color-background"/>

  <!-- 装饰背景元素组：包含几何图形和波浪线，营造潮流感和视觉冲击力 -->
  <g class="decorative-elements">
    <!-- 大型半透明矩形，使用主渐变色 -->
    <rect x="1500" y="50" width="300" height="300" rx="60" fill="url(#primaryGradient)" class="decorative-shape"/>
    <!-- 大型半透明圆形，使用强调渐变色 -->
    <circle cx="200" cy="900" r="180" fill="url(#accentGradient)" class="decorative-shape"/>
    <!-- 顶部左侧小型半透明矩形 -->
    <rect x="50" y="50" width="150" height="150" rx="30" fill="#4A86E8" class="decorative-shape" opacity="0.05"/>
    <!-- 底部右侧小型半透明矩形 -->
    <rect x="1720" y="900" width="150" height="150" rx="30" fill="#3B82F6" class="decorative-shape" opacity="0.05"/>

    <!-- 装饰性波浪线（勾线图形化），使用渐变色，体现科技感 -->
    <path d="M0 800 C400 700, 800 900, 1200 800 S1600 700, 1920 800" class="decorative-line"/>
    <path d="M0 200 C300 300, 700 100, 1100 200 S1500 300, 1920 200" class="decorative-line"/>
  </g>

  <!-- 主要内容区域组：包含Logo、标题、副标题和行动按钮 -->
  <g>
    <!-- 品牌Logo占位符：预留显著位置，顶部左侧 -->
    <g class="logo-container">
      <!-- JIMU品牌Logo -->
      <g transform="translate(80, 60)">
        <!-- Logo背景 -->
        <rect x="0" y="0" width="180" height="60" rx="8" ry="8" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="1"/>
        <!-- Logo图形：两个重叠圆形代表"积木"概念 -->
        <g transform="translate(10, 15)">
          <circle cx="15" cy="15" r="12" fill="#1E40AF" opacity="0.8"/>
          <circle cx="25" cy="15" r="12" fill="#3B82F6" opacity="0.9"/>
        </g>
        <!-- Logo文字 -->
        <text x="55" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#1E40AF">JIMU</text>
        <text x="55" y="40" font-family="Arial, sans-serif" font-size="10" font-weight="400" fill="#64748B">智能演示</text>
      </g>
    </g>

    <!-- 主标题占位符：居中显示，使用最大字体和渐变色，突出层次和吸引力 -->
    <text x="960" y="450" class="hero-title font-primary" fill="url(#textGradient)" text-anchor="middle">
      {title}
    </text>

    <!-- 副标题占位符：居中显示，位于主标题下方，补充说明 -->
    <!-- y坐标通过计算确保与主标题有足够间距，避免重叠 -->
    <text x="960" y="550" class="section-title font-secondary color-text-primary" text-anchor="middle">
      {subtitle}
    </text>

    <!-- 行动号召按钮：居中显示，突出产品优势和价值主张 -->
    <!-- y坐标通过计算确保与副标题有足够间距，避免重叠 -->
    <rect x="800" y="650" width="320" height="70" rx="10" fill="url(#primaryGradient)"/>
    <text x="960" y="695" class="body-text font-primary" fill="#FFFFFF" text-anchor="middle">
      立即探索产品优势
    </text>

    <!-- 大型装饰元素：超大字体图标，极低透明度，作为背景视觉冲击点 -->
    <!-- y坐标通过计算确保与上方元素有足够间距，避免重叠 -->
    <text x="960" y="850" class="font-accent highlight-element" style="font-size: 300px; font-weight: 900;" text-anchor="middle">
      ✨
    </text>

    <!-- 底部信息占位符：日期和作者，位于右下角，使用小字体 -->
    <!-- y坐标通过计算确保有足够间距，避免重叠 -->
    <text x="1840" y="1000" class="small-text font-secondary color-text-secondary" text-anchor="end">
      {date}
    </text>
    <text x="1840" y="1025" class="caption font-secondary color-text-secondary" text-anchor="end">
      {author}
    </text>

  </g>
</svg>