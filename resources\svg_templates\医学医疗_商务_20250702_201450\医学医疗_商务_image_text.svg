<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color Palette -->
    <style type="text/css">
      <![CDATA[
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border-stroke { stroke: #BAE6FD; }

      /* Font System */
      .font-primary { font-family: "Microsoft YaHei", "Segoe UI", sans-serif; }
      .font-secondary { font-family: "Source Han Sans CN", "Noto Sans CJK SC", sans-serif; }
      .font-accent { font-family: "Times New Roman", serif; }

      /* Font Sizes */
      .hero-title { font-size: 72px; font-weight: 700; }
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 700; }
      .content-title { font-size: 28px; font-weight: 600; }
      .body-text { font-size: 22px; font-weight: 400; }
      .small-text { font-size: 16px; font-weight: 400; }
      .caption { font-size: 14px; font-weight: 400; }

      /* Shadows for cards */
      .card-shadow {
        filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1)) drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.06));
      }

      /* Gradients for decorative elements and text */
      .accent-gradient-fill { fill: url(#accentGradient); }
      .primary-gradient-fill { fill: url(#primaryGradient); }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="accentTransparentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0" />
    </linearGradient>

    <!-- ClipPath for image -->
    <clipPath id="imageClipPath">
      <rect x="80" y="350" width="700" height="525" rx="12"/>
    </clipPath>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-color"/>

  <!-- Decorative Elements: Subtle background patterns / waves -->
  <g opacity="0.1">
    <circle cx="1700" cy="150" r="100" class="primary-color"/>
    <rect x="100" y="900" width="300" height="50" rx="10" class="accent-color" transform="rotate(-5 100 900)"/>
    <!-- Wavy line for organic touch -->
    <path d="M0 800 C 400 700, 800 900, 1200 800 S 1600 700, 1920 800" stroke="#475569" stroke-width="2" fill="none"/>
  </g>

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder (Top-Left) -->
    <rect x="80" y="60" width="200" height="50" rx="8" class="primary-color card-shadow"/>
    <text x="180" y="95" text-anchor="middle" class="font-primary small-text" fill="#FFFFFF">
      {logo_url}
    </text>

    <!-- Main Title -->
    <text x="960" y="190" text-anchor="middle" class="font-primary main-title text-primary">
      {title}
    </text>

    <!-- Subtitle -->
    <text x="960" y="250" text-anchor="middle" class="font-secondary content-title text-secondary">
      {subtitle}
    </text>
  </g>

  <!-- Main Content Section: Image and Text Layout -->
  <g id="main-content">
    <!-- Left Column: Image Area -->
    <g id="image-section" class="card-shadow">
      <rect x="80" y="350" width="700" height="525" rx="12" class="card-background card-border-stroke" stroke-width="1"/>
      <!-- Image Placeholder - Ensure it fits the 700x525 area with clipPath -->
      <image x="80" y="350" width="700" height="525" preserveAspectRatio="xMidYMid slice" clip-path="url(#imageClipPath)"
             xlink:href="{image_url}" />
      <!-- Overlay text for image placeholder if image_url is not replaced -->
      <text x="430" y="600" text-anchor="middle" class="font-primary content-title text-light">
        图片占位符
      </text>
    </g>

    <!-- Right Column: Text Area -->
    <g id="text-section">
      <!-- Section Title (Chinese, large font) -->
      <text x="960" y="386" class="font-primary section-title text-primary">
        {section_title}
      </text>

      <!-- Content Title (English, smaller font) -->
      <text x="960" y="500" class="font-secondary small-text text-secondary">
        Clinical Data and Research Outcomes
      </text>

      <!-- Body Text (Chinese, multiple lines) -->
      <text x="960" y="560" class="font-primary body-text text-primary">
        <tspan x="960" dy="0">{content_line1}</tspan>
        <tspan x="960" dy="40">{content_line2}</tspan>
        <tspan x="960" dy="40">{content_line3}</tspan>
        <tspan x="960" dy="40">{content_line4}</tspan>
      </text>

      <!-- Key Data Point (Large Number) -->
      <text x="960" y="760" class="font-accent hero-title accent-gradient-fill">
        98.7%
      </text>
      <!-- Caption for the large number -->
      <text x="960" y="880" class="font-primary caption text-secondary">
        准确率提升和效果显著
      </text>

      <!-- Supporting Text (English) -->
      <text x="960" y="940" class="font-secondary small-text text-light">
        Significant improvement in accuracy and efficacy.
      </text>
    </g>
  </g>

  <!-- Footer/Meta Info -->
  <g id="footer">
    <text x="80" y="1020" class="font-primary caption text-light">
      日期: {date}
    </text>
    <text x="1840" y="1020" text-anchor="end" class="font-primary caption text-light">
      作者: {author}
    </text>
  </g>

  <!-- Medical Cross Icon (Outline style) -->
  <g id="medical-cross-icon" transform="translate(1700, 60)">
    <circle cx="0" cy="0" r="30" class="accent-color" opacity="0.1"/>
    <path d="M0 -20 L0 20 M-20 0 L20 0" stroke="#3B82F6" stroke-width="4" stroke-linecap="round"/>
  </g>

</svg>