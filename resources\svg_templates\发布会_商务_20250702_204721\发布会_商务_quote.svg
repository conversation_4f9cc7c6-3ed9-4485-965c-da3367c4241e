<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style type="text/css">
      /* Color Palette */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-border-color { stroke: #BAE6FD; }

      /* Font System */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title-style {
        font-size: 72px;
        font-weight: 700; /* bold */
        line-height: 1.1; /* tight */
      }
      .main-title-style {
        font-size: 56px;
        font-weight: 700; /* bold */
        line-height: 1.1;
      }
      .section-title-style {
        font-size: 36px;
        font-weight: 600; /* semibold */
        line-height: 1.4; /* normal */
      }
      .content-title-style {
        font-size: 28px;
        font-weight: 500; /* medium */
        line-height: 1.4;
      }
      .body-text-style {
        font-size: 22px;
        font-weight: 400; /* normal */
        line-height: 1.6; /* relaxed */
      }
      .small-text-style {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.4;
      }
      .caption-text-style {
        font-size: 14px;
        font-weight: 400;
        line-height: 1.4;
      }

      /* Quote specific styles */
      .quote-text {
        fill: #1E293B; /* text-primary */
        text-anchor: middle; /* Center alignment */
      }

      .quote-author {
        fill: #64748B; /* text-secondary */
        text-anchor: middle;
      }

      .quote-source {
        fill: #64748B; /* text-secondary */
        text-anchor: middle;
      }

      .quote-mark {
        fill: #3B82F6; /* accent_color */
        opacity: 0.1; /* Subtle effect */
      }

      /* Gradients */
      .primary-gradient-fill {
        fill: url(#primaryGradient);
      }
      .accent-gradient-fill {
        fill: url(#accentGradient);
      }
      .background-gradient-fill {
        fill: url(#backgroundGradient);
      }

      /* Card style */
      .card-background {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
      }
      .card-shadow {
        filter: url(#drop-shadow);
      }

      /* Bento Grid inspiration: using subtle rectangles for structure */
      .bento-card {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        rx: 12px; /* border_radius */
        ry: 12px;
      }
      .bento-highlight-line {
        stroke: #3B82F6; /* accent_color */
        stroke-width: 2px;
        stroke-linecap: round;
      }

    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="3" flood-color="rgba(0, 0, 0, 0.1)" flood-opacity="1"/>
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0, 0, 0, 0.06)" flood-opacity="1"/>
    </filter>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-gradient-fill" />

  <!-- Decorative elements - Subtle geometric shapes -->
  <circle cx="1700" cy="150" r="100" class="primary-color" opacity="0.05" />
  <rect x="100" y="900" width="150" height="150" rx="20" ry="20" class="accent-color" opacity="0.05" />
  <path d="M0 0 L1920 0 L1920 60 C1600 120, 320 120, 0 60 Z" class="accent-gradient-fill" opacity="0.1"/>
  <path d="M0 1080 L1920 1080 L1920 1020 C1600 960, 320 960, 0 1020 Z" class="primary-gradient-fill" opacity="0.1"/>


  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="200" height="50" rx="8" ry="8" fill="#E0F2FE" />
    <text x="180" y="95" class="content-title-style" fill="#1E40AF" text-anchor="middle">
      <tspan x="180" y="95">品牌标志</tspan>
    </text>

    <!-- Page Number -->
    <text x="1840" y="95" class="section-title-style text-light" text-anchor="end">9/10</text>
  </g>

  <!-- Main Content Area - Quote and Author -->
  <g id="main-quote-section">
    <!-- Quote Card Background (Bento Grid inspired) -->
    <rect x="360" y="220" width="1200" height="640" class="bento-card card-shadow" />

    <!-- Decorative Quote Marks (Super Large Visual Element) -->
    <text x="450" y="380" class="hero-title-style quote-mark" font-size="200px" text-anchor="start">“</text>
    <text x="1470" y="800" class="hero-title-style quote-mark" font-size="200px" text-anchor="end">”</text>

    <!-- Quote Text -->
    <text x="960" y="470" class="main-title-style quote-text font-primary">
      <tspan x="960" y="470">{content}</tspan>
      <tspan x="960" y="550" dy="50">创新是企业发展的核心动力</tspan>
      <tspan x="960" y="630" dy="50">和持续增长的基石。</tspan>
    </text>

    <!-- Author and Source -->
    <text x="960" y="760" class="content-title-style quote-author font-secondary">
      <tspan x="960" y="760">— {author}</tspan>
      <tspan x="960" y="800" dy="40">首席执行官</tspan>
    </text>
    <text x="960" y="840" class="body-text-style quote-source font-secondary">
      <tspan x="960" y="840">{date}</tspan>
      <tspan x="960" y="880" dy="40">产品发布会</tspan>
    </text>

    <!-- Decorative lines/highlights for Bento Grid feel -->
    <line x1="400" y1="260" x2="480" y2="260" class="bento-highlight-line" />
    <line x1="400" y1="260" x2="400" y2="340" class="bento-highlight-line" />

    <line x1="1520" y1="820" x2="1440" y2="820" class="bento-highlight-line" />
    <line x1="1520" y1="820" x2="1520" y2="740" class="bento-highlight-line" />

  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="960" y="1030" class="small-text-style text-light" text-anchor="middle">
      <tspan x="960" y="1030">版权所有 和#xA9; 2023 {title}。保留所有权利。</tspan>
    </text>
  </g>

</svg>