#!/usr/bin/env python3
"""
批量SVG模板生成器 - 多场景多风格批量测试工具
Batch SVG Template Generator - Multi-Scenario Multi-Style Batch Testing Tool

功能特点：
- 一次性生成多种场景×风格组合的模板集合
- 支持自定义测试组合配置
- 生成详细的运行报告和对比分析
- 进度跟踪和错误处理
- 统计分析和质量评估

用法示例：
    python batch_template_generator.py --preset business
    python batch_template_generator.py --preset comprehensive --detail
    python batch_template_generator.py --scenarios 年中总结 商业计划 --styles 商务 简约
    python batch_template_generator.py --all-scenarios

Created: 2025-07-02T14:45:00
Author: <PERSON> Assistant
"""

import asyncio
import json
import logging
import os
import sys
import argparse
import time
from datetime import datetime
from typing import Dict, List, Any, Tuple
from pathlib import Path
import traceback
import threading

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入增强版生成器
from backend.utils.enhanced_svg_template_generator import (
    EnhancedSVGTemplateGenerator,
    EnhancedTemplateConfig,
    ScenarioType,
    StyleType
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('batch_generator.log', encoding='utf-8')
    ],
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

class BatchTemplateGenerator:
    """批量模板生成器管理器"""
    
    def __init__(self, output_dir: str = "./batch_results"):
        """初始化批量生成器

        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 批量运行配置
        self.total_combinations = 0
        self.completed_combinations = 0
        self.failed_combinations = 0
        self.start_time = None
        self.results = []
        self.errors = []

        # 并发控制
        self.max_concurrent_pages = 10  # 最大并发页面数
        self.max_retries_per_page = 3   # 每个页面最大重试次数
        self.lock = threading.Lock()    # 线程锁，用于保护共享数据

        # 创建运行时间戳目录
        self.run_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = self.output_dir / f"batch_run_{self.run_timestamp}"
        self.run_dir.mkdir(exist_ok=True)

        logger.info(f"批量生成器初始化完成，输出目录: {self.run_dir}")
        logger.info(f"并发配置: 最大并发页面数={self.max_concurrent_pages}, 每页最大重试次数={self.max_retries_per_page}")

    def get_preset_combinations(self, preset_name: str) -> List[Tuple[ScenarioType, StyleType]]:
        """获取预设的测试组合
        
        Args:
            preset_name: 预设名称
            
        Returns:
            场景风格组合列表
        """
        presets = {
            "business": [
                # 商务相关场景
                (ScenarioType.BUSINESS_PLAN, StyleType.BUSINESS),
                (ScenarioType.BUSINESS_PLAN, StyleType.MINIMALIST),
                (ScenarioType.COMPANY_INTRO, StyleType.BUSINESS),
                (ScenarioType.ANALYSIS_REPORT, StyleType.BUSINESS),
                (ScenarioType.ANNUAL_SUMMARY, StyleType.BUSINESS),
            ],
            "education": [
                # 教育培训场景
                (ScenarioType.EDUCATION_TRAINING, StyleType.MINIMALIST),
                (ScenarioType.EDUCATION_TRAINING, StyleType.ILLUSTRATION),
                (ScenarioType.UNIVERSITY_ZONE, StyleType.BUSINESS),
                (ScenarioType.UNIVERSITY_ZONE, StyleType.MINIMALIST),
                (ScenarioType.SELF_INTRODUCTION, StyleType.MINIMALIST),
            ],
            "technology": [
                # 科技相关场景
                (ScenarioType.BUSINESS_PLAN, StyleType.TECHNOLOGY),
                (ScenarioType.ANALYSIS_REPORT, StyleType.TECHNOLOGY),
                (ScenarioType.PRESS_CONFERENCE, StyleType.TECHNOLOGY),
                (ScenarioType.COMPANY_INTRO, StyleType.TECHNOLOGY),
                (ScenarioType.MARKETING_PROMOTION, StyleType.TECHNOLOGY),
            ],
            "government": [
                # 政务相关场景  
                (ScenarioType.POLITICAL_PROMOTION, StyleType.GOVERNMENT),
                (ScenarioType.ANNUAL_SUMMARY, StyleType.GOVERNMENT),
                (ScenarioType.SUMMARY_REPORT, StyleType.GOVERNMENT),
                (ScenarioType.PUBLIC_WELFARE, StyleType.GOVERNMENT),
                (ScenarioType.PRESS_CONFERENCE, StyleType.GOVERNMENT),
            ],
            "creative": [
                # 创意相关场景
                (ScenarioType.MARKETING_PROMOTION, StyleType.ILLUSTRATION),
                (ScenarioType.MARKETING_PROMOTION, StyleType.TRENDY),
                (ScenarioType.PRESS_CONFERENCE, StyleType.TRENDY),
                (ScenarioType.PUBLIC_WELFARE, StyleType.ILLUSTRATION),
                (ScenarioType.SELF_INTRODUCTION, StyleType.ILLUSTRATION),
            ],
            "comprehensive": [
                # 全面测试 - 涵盖各种组合
                (ScenarioType.BUSINESS_PLAN, StyleType.BUSINESS),
                (ScenarioType.EDUCATION_TRAINING, StyleType.MINIMALIST),
                (ScenarioType.MEDICAL_HEALTHCARE, StyleType.BUSINESS),
                (ScenarioType.MARKETING_PROMOTION, StyleType.TRENDY),
                (ScenarioType.ANALYSIS_REPORT, StyleType.TECHNOLOGY),
                (ScenarioType.POLITICAL_PROMOTION, StyleType.GOVERNMENT),
                (ScenarioType.UNIVERSITY_ZONE, StyleType.MINIMALIST),
                (ScenarioType.PUBLIC_WELFARE, StyleType.ILLUSTRATION),
                (ScenarioType.PRESS_CONFERENCE, StyleType.BUSINESS),
                (ScenarioType.SELF_INTRODUCTION, StyleType.DIFFUSION),
                (ScenarioType.COMPANY_INTRO, StyleType.THREE_DIMENSIONAL),
                (ScenarioType.ANNUAL_SUMMARY, StyleType.RETRO),
            ],
            "all_scenarios": [
                # 所有13种场景，配合最合适的风格
                (ScenarioType.ANNUAL_SUMMARY, StyleType.BUSINESS),
                (ScenarioType.SUMMARY_REPORT, StyleType.MINIMALIST),
                (ScenarioType.EDUCATION_TRAINING, StyleType.ILLUSTRATION),
                (ScenarioType.MEDICAL_HEALTHCARE, StyleType.BUSINESS),
                (ScenarioType.MARKETING_PROMOTION, StyleType.TRENDY),
                (ScenarioType.BUSINESS_PLAN, StyleType.BUSINESS),
                (ScenarioType.UNIVERSITY_ZONE, StyleType.MINIMALIST),
                (ScenarioType.COMPANY_INTRO, StyleType.BUSINESS),
                (ScenarioType.POLITICAL_PROMOTION, StyleType.GOVERNMENT),
                (ScenarioType.SELF_INTRODUCTION, StyleType.DIFFUSION),
                (ScenarioType.ANALYSIS_REPORT, StyleType.TECHNOLOGY),
                (ScenarioType.PRESS_CONFERENCE, StyleType.BUSINESS),
                (ScenarioType.PUBLIC_WELFARE, StyleType.ILLUSTRATION),
            ]
        }
        
        return presets.get(preset_name, [])

    def get_custom_combinations(self, scenarios: List[str], styles: List[str]) -> List[Tuple[ScenarioType, StyleType]]:
        """获取自定义测试组合
        
        Args:
            scenarios: 场景名称列表
            styles: 风格名称列表
            
        Returns:
            场景风格组合列表
        """
        combinations = []
        
        # 场景名称映射
        scenario_map = {scenario.value: scenario for scenario in ScenarioType}
        style_map = {style.value: style for style in StyleType}
        
        for scenario_name in scenarios:
            for style_name in styles:
                if scenario_name in scenario_map and style_name in style_map:
                    combinations.append((scenario_map[scenario_name], style_map[style_name]))
                else:
                    logger.warning(f"未找到场景或风格: {scenario_name}, {style_name}")
        
        return combinations

    async def _generate_single_template_with_retry(self,
                                                 generator: 'EnhancedSVGTemplateGenerator',
                                                 template_type: Dict[str, Any]) -> Dict[str, Any]:
        """生成单个模板页面，包含重试机制

        Args:
            generator: 增强版SVG模板生成器实例
            template_type: 模板类型配置

        Returns:
            模板生成结果
        """
        for attempt in range(self.max_retries_per_page):
            try:
                logger.info(f"生成模板页面 {template_type['type']} (尝试 {attempt + 1}/{self.max_retries_per_page})")

                # 调用生成器的单个模板生成方法
                result = await generator._generate_enhanced_single_template(template_type)

                if result.get("success"):
                    logger.info(f"✅ 模板页面 {template_type['type']} 生成成功")
                    return result
                else:
                    logger.warning(f"⚠️ 模板页面 {template_type['type']} 生成失败 (尝试 {attempt + 1}): {result.get('error')}")

            except Exception as e:
                logger.error(f"❌ 模板页面 {template_type['type']} 生成异常 (尝试 {attempt + 1}): {str(e)}")

            # 如果不是最后一次尝试，等待一段时间再重试
            if attempt < self.max_retries_per_page - 1:
                await asyncio.sleep(1)

        # 所有重试都失败了
        return {
            "success": False,
            "error": f"模板页面 {template_type['type']} 在 {self.max_retries_per_page} 次尝试后仍然失败"
        }

    async def _generate_template_set_concurrent(self,
                                              scenario: ScenarioType,
                                              style: StyleType,
                                              content_context: str) -> Dict[str, Any]:
        """并发生成单个场景风格组合的模板集合

        Args:
            scenario: 场景类型
            style: 风格类型
            content_context: 内容上下文

        Returns:
            模板集合生成结果
        """
        try:
            logger.info(f"开始并发生成模板集合: {scenario.value} × {style.value}")

            # 创建增强版生成器实例
            config = EnhancedTemplateConfig(scenario=scenario, style=style)
            generator = EnhancedSVGTemplateGenerator(config)

            # 首先生成设计规范
            spec_result = await generator.generate_enhanced_design_specification(content_context)
            if not spec_result.get("success"):
                return {"success": False, "error": "设计规范生成失败"}

            # 确保设计规范正确设置到生成器实例
            generator.design_spec = spec_result["enhanced_design_spec"]

            # 获取所有模板类型
            template_types = generator.TEMPLATE_TYPES

            # 并发生成所有模板页面
            logger.info(f"开始并发生成 {len(template_types)} 个模板页面...")

            # 创建并发任务
            tasks = []
            for template_type in template_types:
                task = self._generate_single_template_with_retry(
                    generator, template_type
                )
                tasks.append(task)

            # 限制并发数量，分批执行
            generated_templates = []
            failed_templates = []

            # 分批处理，每批最多处理 max_concurrent_pages 个
            for i in range(0, len(tasks), self.max_concurrent_pages):
                batch_tasks = tasks[i:i + self.max_concurrent_pages]
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                for j, result in enumerate(batch_results):
                    template_type = template_types[i + j]

                    if isinstance(result, Exception):
                        failed_templates.append({
                            "template_id": template_type["id"],
                            "error": str(result)
                        })
                        logger.error(f"模板 {template_type['type']} 生成异常: {str(result)}")
                    elif result.get("success"):
                        generated_templates.append(result["template"])
                        logger.info(f"✅ 模板 {template_type['type']} 生成成功")
                    else:
                        failed_templates.append({
                            "template_id": template_type["id"],
                            "error": result.get("error", "未知错误")
                        })
                        logger.warning(f"⚠️ 模板 {template_type['type']} 生成失败: {result.get('error')}")

                # 批次间短暂延迟
                if i + self.max_concurrent_pages < len(tasks):
                    await asyncio.sleep(0.5)

            # 保存生成的模板集合
            if generated_templates:
                save_result = generator._save_enhanced_template_set(generated_templates)

                return {
                    "success": True,
                    "template_set": {
                        "scenario": scenario.value,
                        "style": style.value,
                        "total_templates": len(generated_templates),
                        "failed_templates": len(failed_templates),
                        "templates": generated_templates,
                        "save_path": save_result.get("save_path"),
                        "design_specification": spec_result["enhanced_design_spec"]
                    },
                    "failed_templates": failed_templates
                }
            else:
                return {"success": False, "error": "所有模板页面生成失败"}

        except Exception as e:
            logger.error(f"并发模板集合生成失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def run_batch_generation(self,
                                 combinations: List[Tuple[ScenarioType, StyleType]],
                                 content_context: str = "批量测试模板集合",
                                 enable_detailed_report: bool = True) -> Dict[str, Any]:
        """运行批量模板生成

        Args:
            combinations: 场景风格组合列表
            content_context: 内容上下文
            enable_detailed_report: 是否生成详细报告

        Returns:
            批量运行结果
        """
        logger.info(f"开始批量模板生成，共 {len(combinations)} 种组合")
        logger.info(f"使用并发生成模式: 每个模板集合内部并发生成 {self.max_concurrent_pages} 个页面")

        self.total_combinations = len(combinations)
        self.completed_combinations = 0
        self.failed_combinations = 0
        self.start_time = datetime.now()
        self.results = []
        self.errors = []

        try:
            # 逐个生成每种组合（组合间串行，组合内并行）
            for i, (scenario, style) in enumerate(combinations, 1):
                logger.info(f"\n{'='*60}")
                logger.info(f"进度: {i}/{len(combinations)} - {scenario.value} × {style.value}")
                logger.info(f"{'='*60}")

                combination_start_time = time.time()

                try:
                    # 使用新的并发生成方法
                    result = await self._generate_template_set_concurrent(
                        scenario=scenario,
                        style=style,
                        content_context=f"{content_context} - {scenario.value}×{style.value}"
                    )

                    # 如果生成成功，立即保存并复制到批量运行目录
                    if result.get("success") and "template_set" in result:
                        template_set = result["template_set"]
                        original_path = template_set["save_path"]

                        logger.info(f"📁 模板集合已保存到: {original_path}")

                        # 复制到批量运行目录（确保保存在两个位置）
                        import shutil
                        batch_copy_path = os.path.join(self.run_dir, os.path.basename(original_path))

                        try:
                            if os.path.exists(original_path):
                                shutil.copytree(original_path, batch_copy_path, dirs_exist_ok=True)
                                template_set["batch_copy_path"] = batch_copy_path
                                logger.info(f"📋 模板集合已复制到批量目录: {batch_copy_path}")
                            else:
                                logger.warning(f"⚠️ 原始路径不存在，无法复制: {original_path}")
                        except Exception as copy_error:
                            logger.error(f"❌ 复制模板集合失败: {str(copy_error)}")
                            # 复制失败不影响主流程
                    
                    combination_time = time.time() - combination_start_time

                    if result.get("success"):
                        with self.lock:  # 使用线程锁保护共享数据
                            self.completed_combinations += 1

                        template_set = result["template_set"]

                        # 记录成功结果
                        result_info = {
                            "combination_id": f"{scenario.value}_{style.value}",
                            "scenario": scenario.value,
                            "style": style.value,
                            "success": True,
                            "generation_time": combination_time,
                            "total_templates": template_set["total_templates"],
                            "failed_templates": template_set["failed_templates"],
                            "save_path": str(template_set["save_path"]),
                            "timestamp": datetime.now().isoformat(),
                            "concurrent_pages": self.max_concurrent_pages,
                            "retries_per_page": self.max_retries_per_page
                        }

                        if enable_detailed_report:
                            result_info["templates_detail"] = template_set["templates"]
                            result_info["design_specification"] = template_set["design_specification"]

                        with self.lock:
                            self.results.append(result_info)

                        logger.info(f"✅ 成功生成: {template_set['total_templates']} 个模板")
                        logger.info(f"⏱️  耗时: {combination_time:.2f} 秒")
                        logger.info(f"📁 保存路径: {template_set['save_path']}")
                        logger.info(f"🔄 并发配置: {self.max_concurrent_pages} 页面并发, 每页最多 {self.max_retries_per_page} 次重试")

                    else:
                        with self.lock:
                            self.failed_combinations += 1

                        error_info = {
                            "combination_id": f"{scenario.value}_{style.value}",
                            "scenario": scenario.value,
                            "style": style.value,
                            "success": False,
                            "error": result.get("error", "未知错误"),
                            "generation_time": combination_time,
                            "timestamp": datetime.now().isoformat()
                        }

                        with self.lock:
                            self.errors.append(error_info)

                        logger.error(f"❌ 生成失败: {result.get('error')}")
                        
                except Exception as e:
                    with self.lock:
                        self.failed_combinations += 1

                    combination_time = time.time() - combination_start_time

                    error_info = {
                        "combination_id": f"{scenario.value}_{style.value}",
                        "scenario": scenario.value,
                        "style": style.value,
                        "success": False,
                        "error": str(e),
                        "traceback": traceback.format_exc(),
                        "generation_time": combination_time,
                        "timestamp": datetime.now().isoformat()
                    }

                    with self.lock:
                        self.errors.append(error_info)

                    logger.error(f"❌ 组合生成异常: {str(e)}")

                # 输出当前进度
                progress = (i / len(combinations)) * 100
                logger.info(f"📊 当前进度: {progress:.1f}% ({i}/{len(combinations)})")

                # 添加延迟避免API限制（组合间延迟）
                if i < len(combinations):  # 最后一个不需要延迟
                    await asyncio.sleep(2)
            
            # 生成批量运行报告
            batch_result = await self._generate_batch_report(enable_detailed_report)
            
            logger.info(f"\n{'='*60}")
            logger.info(f"🎉 批量生成完成！")
            logger.info(f"✅ 成功: {self.completed_combinations} 个组合")
            logger.info(f"❌ 失败: {self.failed_combinations} 个组合")
            logger.info(f"⏱️  总耗时: {batch_result['total_time']}")
            logger.info(f"🔄 并发配置: {self.max_concurrent_pages} 页面并发, 每页最多 {self.max_retries_per_page} 次重试")
            logger.info(f"📄 报告文件: {batch_result['report_file']}")
            logger.info(f"{'='*60}")
            
            return batch_result
            
        except Exception as e:
            logger.error(f"批量生成过程发生严重错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "completed_combinations": self.completed_combinations,
                "failed_combinations": self.failed_combinations
            }

    async def _generate_batch_report(self, enable_detailed_report: bool = True) -> Dict[str, Any]:
        """生成批量运行报告"""
        try:
            end_time = datetime.now()
            total_time = end_time - self.start_time
            
            # 计算统计信息
            success_rate = (self.completed_combinations / self.total_combinations * 100) if self.total_combinations > 0 else 0
            avg_time_per_combination = sum(r.get("generation_time", 0) for r in self.results + self.errors) / self.total_combinations if self.total_combinations > 0 else 0
            
            # 模板统计
            total_templates = sum(r.get("total_templates", 0) for r in self.results)
            total_failed_templates = sum(r.get("failed_templates", 0) for r in self.results)
            
            # 生成报告数据
            report_data = {
                "batch_info": {
                    "run_id": self.run_timestamp,
                    "start_time": self.start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "total_time": str(total_time),
                    "total_time_seconds": total_time.total_seconds(),
                    "generation_mode": "concurrent_with_retry"
                },
                "concurrent_config": {
                    "max_concurrent_pages": self.max_concurrent_pages,
                    "max_retries_per_page": self.max_retries_per_page,
                    "description": "每个模板集合内部并发生成页面，每页支持重试机制"
                },
                "statistics": {
                    "total_combinations": self.total_combinations,
                    "completed_combinations": self.completed_combinations,
                    "failed_combinations": self.failed_combinations,
                    "success_rate": round(success_rate, 2),
                    "average_time_per_combination": round(avg_time_per_combination, 2),
                    "total_templates_generated": total_templates,
                    "total_failed_templates": total_failed_templates
                },
                "successful_results": self.results,
                "failed_results": self.errors,
                "summary": {
                    "scenarios_tested": list(set(r["scenario"] for r in self.results)),
                    "styles_tested": list(set(r["style"] for r in self.results)),
                    "fastest_combination": min(self.results, key=lambda x: x["generation_time"])["combination_id"] if self.results else None,
                    "slowest_combination": max(self.results, key=lambda x: x["generation_time"])["combination_id"] if self.results else None,
                    "most_templates": max(self.results, key=lambda x: x["total_templates"])["combination_id"] if self.results else None
                }
            }
            
            # 如果不需要详细报告，移除详细信息
            if not enable_detailed_report:
                for result in report_data["successful_results"]:
                    result.pop("templates_detail", None)
                    result.pop("design_specification", None)
            
            # 保存报告文件
            report_file = self.run_dir / "batch_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            # 生成简化的文本报告
            text_report = self._generate_text_report(report_data)
            text_report_file = self.run_dir / "batch_report.txt"
            with open(text_report_file, 'w', encoding='utf-8') as f:
                f.write(text_report)
            
            logger.info(f"批量运行报告已生成: {report_file}")
            
            return {
                "success": True,
                "report_data": report_data,
                "report_file": str(report_file),
                "text_report_file": str(text_report_file),
                "total_time": str(total_time),
                "success_rate": success_rate
            }
            
        except Exception as e:
            logger.error(f"生成批量报告失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _generate_text_report(self, report_data: Dict[str, Any]) -> str:
        """生成文本格式的报告"""
        
        report_lines = [
            "=" * 80,
            "批量SVG模板生成器运行报告 (并发重试版)",
            "=" * 80,
            "",
            "📊 运行统计",
            "-" * 40,
            f"运行ID: {report_data['batch_info']['run_id']}",
            f"开始时间: {report_data['batch_info']['start_time']}",
            f"结束时间: {report_data['batch_info']['end_time']}",
            f"总耗时: {report_data['batch_info']['total_time']}",
            f"生成模式: {report_data['batch_info']['generation_mode']}",
            "",
            "🔄 并发配置",
            "-" * 40,
            f"最大并发页面数: {report_data['concurrent_config']['max_concurrent_pages']}",
            f"每页最大重试次数: {report_data['concurrent_config']['max_retries_per_page']}",
            f"说明: {report_data['concurrent_config']['description']}",
            "",
            "📈 生成统计",
            "-" * 40,
            f"总组合数: {report_data['statistics']['total_combinations']}",
            f"成功组合: {report_data['statistics']['completed_combinations']}",
            f"失败组合: {report_data['statistics']['failed_combinations']}",
            f"成功率: {report_data['statistics']['success_rate']}%",
            f"平均每组合耗时: {report_data['statistics']['average_time_per_combination']:.2f} 秒",
            "",
            f"生成模板总数: {report_data['statistics']['total_templates_generated']}",
            f"失败模板总数: {report_data['statistics']['total_failed_templates']}",
            "",
            "🎯 成功组合详情",
            "-" * 40,
        ]
        
        # 添加成功组合信息
        for result in report_data["successful_results"]:
            concurrent_info = ""
            if "concurrent_pages" in result and "retries_per_page" in result:
                concurrent_info = f" | 并发: {result['concurrent_pages']}页面, 重试: {result['retries_per_page']}次"

            report_lines.extend([
                f"✅ {result['combination_id']}",
                f"   场景: {result['scenario']} | 风格: {result['style']}",
                f"   生成模板: {result['total_templates']} 个 | 耗时: {result['generation_time']:.2f} 秒{concurrent_info}",
                f"   保存路径: {result['save_path']}",
                ""
            ])
        
        # 添加失败组合信息
        if report_data["failed_results"]:
            report_lines.extend([
                "",
                "❌ 失败组合详情",
                "-" * 40,
            ])
            
            for error in report_data["failed_results"]:
                report_lines.extend([
                    f"❌ {error['combination_id']}",
                    f"   场景: {error['scenario']} | 风格: {error['style']}",
                    f"   错误: {error['error']}",
                    f"   耗时: {error['generation_time']:.2f} 秒",
                    ""
                ])
        
        # 添加总结信息
        summary = report_data["summary"]
        report_lines.extend([
            "",
            "📈 运行总结",
            "-" * 40,
            f"测试场景: {', '.join(summary['scenarios_tested'])}",
            f"测试风格: {', '.join(summary['styles_tested'])}",
        ])
        
        if summary['fastest_combination']:
            report_lines.append(f"最快组合: {summary['fastest_combination']}")
        if summary['slowest_combination']:
            report_lines.append(f"最慢组合: {summary['slowest_combination']}")
        if summary['most_templates']:
            report_lines.append(f"模板最多: {summary['most_templates']}")
        
        report_lines.extend([
            "",
            "=" * 80,
            f"报告生成时间: {datetime.now().isoformat()}",
            "=" * 80
        ])
        
        return "\n".join(report_lines)

def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(
        description="批量SVG模板生成器 - 支持多场景多风格组合测试 (并发重试版)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --preset business                    # 使用商务预设
  %(prog)s --preset comprehensive --detail     # 使用全面测试预设，生成详细报告
  %(prog)s --scenarios 年中总结 商业计划 --styles 商务 简约  # 自定义组合
  %(prog)s --all-scenarios                     # 测试所有场景 (并发生成10个页面，每页重试3次)

新功能特性:
  - 每个模板集合内部并发生成10个页面，大幅提升生成速度
  - 每个页面支持最多3次重试，提高成功率
  - 生成一组SVG就立即保存一组，避免数据丢失
  - 线程安全的进度跟踪和错误处理
        """
    )
    
    # 测试组合选项
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--preset", choices=["business", "education", "technology", "government", "creative", "comprehensive", "all_scenarios"],
                      help="使用预设的测试组合")
    group.add_argument("--scenarios", nargs="+", help="自定义场景列表")
    group.add_argument("--all-scenarios", action="store_true", help="测试所有13种场景")
    
    # 风格选项（仅在自定义场景时需要）
    parser.add_argument("--styles", nargs="+", help="自定义风格列表（与--scenarios配合使用）")
    
    # 其他选项
    parser.add_argument("--output-dir", default="./batch_results", help="输出目录 (默认: ./batch_results)")
    parser.add_argument("--context", default="批量测试模板集合", help="内容上下文")
    parser.add_argument("--detail", action="store_true", help="生成详细报告（包含完整模板信息）")
    parser.add_argument("--list-options", action="store_true", help="列出所有可用的场景和风格选项")

    # 并发控制选项
    parser.add_argument("--concurrent-pages", type=int, default=10,
                       help="每个模板集合内最大并发页面数 (默认: 10)")
    parser.add_argument("--max-retries", type=int, default=3,
                       help="每个页面最大重试次数 (默认: 3)")
    
    args = parser.parse_args()
    
    # 列出选项
    if args.list_options:
        print("可用场景:")
        for scenario in ScenarioType:
            print(f"  {scenario.value}")
        print("\n可用风格:")
        for style in StyleType:
            print(f"  {style.value}")
        return
    
    # 初始化批量生成器
    batch_generator = BatchTemplateGenerator(args.output_dir)

    # 应用并发配置
    batch_generator.max_concurrent_pages = args.concurrent_pages
    batch_generator.max_retries_per_page = args.max_retries

    logger.info(f"并发配置已更新: 最大并发页面数={args.concurrent_pages}, 每页最大重试次数={args.max_retries}")
    
    # 确定测试组合
    combinations = []
    
    if args.preset:
        combinations = batch_generator.get_preset_combinations(args.preset)
        logger.info(f"使用预设组合: {args.preset} ({len(combinations)} 种组合)")
        
    elif args.all_scenarios:
        combinations = batch_generator.get_preset_combinations("all_scenarios")
        logger.info(f"测试所有场景 ({len(combinations)} 种组合)")
        
    elif args.scenarios:
        if not args.styles:
            logger.error("使用自定义场景时必须指定风格 (--styles)")
            return
        combinations = batch_generator.get_custom_combinations(args.scenarios, args.styles)
        logger.info(f"自定义组合: {len(args.scenarios)} 场景 × {len(args.styles)} 风格 = {len(combinations)} 种组合")
    
    if not combinations:
        logger.error("未找到有效的测试组合")
        return
    
    # 显示将要测试的组合
    logger.info("将要测试的组合:")
    for i, (scenario, style) in enumerate(combinations, 1):
        logger.info(f"  {i:2d}. {scenario.value} × {style.value}")
    
    print(f"\n开始批量生成，共 {len(combinations)} 种组合...")
    print(f"🔄 并发配置: 每个模板集合内部并发生成 {args.concurrent_pages} 个页面，每页最多重试 {args.max_retries} 次")

    # 运行批量生成
    try:
        result = asyncio.run(batch_generator.run_batch_generation(
            combinations=combinations,
            content_context=args.context,
            enable_detailed_report=args.detail
        ))

        if result.get("success"):
            print(f"\n🎉 批量生成完成！")
            print(f"📄 报告文件: {result['report_file']}")
            print(f"📁 结果目录: {batch_generator.run_dir}")
            print(f"🔄 使用了并发生成模式，大幅提升了生成速度")
        else:
            print(f"\n❌ 批量生成失败: {result.get('error')}")

    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断批量生成")
        logger.info(f"已完成 {batch_generator.completed_combinations}/{batch_generator.total_combinations} 个组合")
    except Exception as e:
        print(f"\n💥 批量生成异常: {str(e)}")
        logger.error(f"批量生成异常: {str(e)}")

if __name__ == "__main__":
    main() 