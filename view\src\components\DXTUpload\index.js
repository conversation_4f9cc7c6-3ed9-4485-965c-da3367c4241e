import React, { useState, useCallback } from 'react';
import { Upload, Button, message, Progress, Card, Typography, Space, Tag, Alert, Modal, Form, Input, InputNumber, Switch, Select } from 'antd';
import { InboxOutlined, UploadOutlined, CheckCircleOutlined, ExclamationCircleOutlined, FolderOpenOutlined, FileOutlined } from '@ant-design/icons';
import './DXTUpload.css';

// {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-17 09:45:00 +08:00
// Reason: 创建DXT扩展文件上传组件，支持拖拽上传和文件管理功能
// Principle_Applied: KISS - 保持组件简洁易用，单一职责 - 专注文件上传处理
// Optimization: 使用React hooks优化性能，Ant Design提供一致的UI体验
// Architectural_Note (AR): 遵循React组件化设计，便于复用和维护
// Documentation_Note (DW): 提供清晰的用户交互提示和错误反馈
// }}

const { Dragger } = Upload;
const { Title, Text } = Typography;

const DXTUpload = ({ onUploadSuccess, onUploadError, disabled = false }) => {
    const [uploading, setUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [selectedFile, setSelectedFile] = useState(null);
    const [validationResult, setValidationResult] = useState(null);
    const [overwriteModalVisible, setOverwriteModalVisible] = useState(false);
    const [pendingFile, setPendingFile] = useState(null);
    const [existingExtensionName, setExistingExtensionName] = useState('');
    const [userConfigModalVisible, setUserConfigModalVisible] = useState(false);
    const [requiredVariables, setRequiredVariables] = useState([]);
    const [configDefinitions, setConfigDefinitions] = useState({});
    const [userConfigForm] = Form.useForm();

    // 文件验证函数
    const validateFile = useCallback(async (file) => {
        // 前端基本验证
        if (!file.name.toLowerCase().endsWith('.dxt')) {
            message.error('只支持.dxt格式的扩展文件');
            return false;
        }

        // 文件大小检查 (50MB)
        const MAX_SIZE = 50 * 1024 * 1024;
        if (file.size > MAX_SIZE) {
            message.error('文件大小不能超过50MB');
            return false;
        }

        // 调用后端验证API
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/dxt/validate', {
                method: 'POST',
                body: formData,
            });

            const result = await response.json();
            
            if (result.success) {
                setValidationResult({
                    valid: true,
                    manifest: result.data
                });
                return true;
            } else {
                setValidationResult({
                    valid: false,
                    error: result.message
                });
                message.error(`文件验证失败: ${result.message}`);
                return false;
            }
        } catch (error) {
            console.error('文件验证错误:', error);
            message.error('文件验证失败，请检查网络连接');
            return false;
        }
    }, []);

    // 文件上传处理
    const handleUpload = useCallback(async (options) => {
        const { file, onProgress, onSuccess, onError } = options;
        
        setUploading(true);
        setUploadProgress(0);
        setSelectedFile(file);
        setValidationResult(null);

        try {
            // 先验证文件
            const isValid = await validateFile(file);
            if (!isValid) {
                setUploading(false);
                onError(new Error('文件验证失败'));
                return;
            }

            // 检查扩展是否已存在
            if (validationResult && validationResult.valid && validationResult.manifest) {
                const extensionName = validationResult.manifest.name;
                try {
                    const checkResponse = await fetch(`/api/dxt/check/${extensionName}`);
                    const checkResult = await checkResponse.json();

                    if (checkResult.success && checkResult.data.exists) {
                        // 扩展已存在，显示覆盖确认对话框
                        setUploading(false);
                        setPendingFile(file);
                        setExistingExtensionName(extensionName);
                        setOverwriteModalVisible(true);
                        return;
                    }
                } catch (error) {
                    console.warn('检查扩展是否存在失败，继续安装:', error);
                }
            }

            // 执行实际上传
            await performUpload(file, false, onProgress, onSuccess, onError);

        } catch (error) {
            setUploading(false);
            setUploadProgress(0);
            console.error('上传错误:', error);
            const errorMsg = `上传失败: ${error.message}`;
            message.error(errorMsg);
            onError(error);
            if (onUploadError) {
                onUploadError(errorMsg);
            }
        }
    }, [validateFile, validationResult, onUploadSuccess, onUploadError]);

    // 执行实际上传的函数
    const performUpload = useCallback(async (file, forceUpdate, onProgress, onSuccess, onError) => {
        try {
            // 创建FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('force_update', forceUpdate.toString());

            // 上传文件
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const percent = Math.round((event.loaded * 100) / event.total);
                    setUploadProgress(percent);
                    onProgress({ percent });
                }
            });

            // 处理上传完成
            xhr.addEventListener('load', () => {
                setUploading(false);
                setUploadProgress(0);
                
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        message.success(`扩展 "${response.data.name}" 安装成功！`);
                        onSuccess(response);
                        if (onUploadSuccess) {
                            onUploadSuccess(response.data);
                        }
                        // 清空选择的文件和验证结果
                        setSelectedFile(null);
                        setValidationResult(null);
                    } else if (response.message === 'NEED_USER_CONFIG') {
                        // 需要用户配置，显示配置对话框
                        setUploading(false);
                        setRequiredVariables(response.data.required_variables);
                        setConfigDefinitions(response.data.config_definitions || {});
                        setPendingFile(file);
                        setUserConfigModalVisible(true);

                        // 初始化表单字段，使用默认值
                        const initialValues = {};
                        response.data.required_variables.forEach(varName => {
                            const configDef = response.data.config_definitions?.[varName];
                            if (configDef && 'default' in configDef) {
                                initialValues[varName] = configDef.default;
                            } else {
                                // 根据类型设置默认值
                                switch (configDef?.type) {
                                    case 'number':
                                        initialValues[varName] = 0;
                                        break;
                                    case 'boolean':
                                        initialValues[varName] = false;
                                        break;
                                    default:
                                        initialValues[varName] = '';
                                }
                            }
                        });
                        userConfigForm.setFieldsValue(initialValues);
                        return;
                    } else {
                        message.error(`安装失败: ${response.message}`);
                        onError(new Error(response.message));
                        if (onUploadError) {
                            onUploadError(response.message);
                        }
                    }
                } else {
                    const errorMsg = '上传失败，请稍后重试';
                    message.error(errorMsg);
                    onError(new Error(errorMsg));
                    if (onUploadError) {
                        onUploadError(errorMsg);
                    }
                }
            });

            // 处理上传错误
            xhr.addEventListener('error', () => {
                setUploading(false);
                setUploadProgress(0);
                const errorMsg = '上传失败，请检查网络连接';
                message.error(errorMsg);
                onError(new Error(errorMsg));
                if (onUploadError) {
                    onUploadError(errorMsg);
                }
            });

            // 开始上传
            xhr.open('POST', '/api/dxt/upload');
            xhr.send(formData);

        } catch (error) {
            setUploading(false);
            setUploadProgress(0);
            console.error('上传错误:', error);
            const errorMsg = `上传失败: ${error.message}`;
            message.error(errorMsg);
            onError(error);
            if (onUploadError) {
                onUploadError(errorMsg);
            }
        }
    }, [onUploadSuccess, onUploadError]);

    // 文件选择前的检查
    const beforeUpload = useCallback((file) => {
        // 基本格式检查
        if (!file.name.toLowerCase().endsWith('.dxt')) {
            message.error('只支持.dxt格式的扩展文件');
            return false;
        }

        // 文件大小检查
        const MAX_SIZE = 50 * 1024 * 1024;
        if (file.size > MAX_SIZE) {
            message.error('文件大小不能超过50MB');
            return false;
        }

        return true;
    }, []);

    // 确认覆盖安装
    const handleOverwriteConfirm = useCallback(async () => {
        if (!pendingFile) return;

        setOverwriteModalVisible(false);
        setUploading(true);
        setUploadProgress(0);

        try {
            await performUpload(pendingFile, true,
                ({ percent }) => setUploadProgress(percent),
                (response) => {
                    message.success(`扩展 "${response.data.name}" 覆盖安装成功！`);
                    if (onUploadSuccess) {
                        onUploadSuccess(response.data);
                    }
                    // 清空状态
                    setSelectedFile(null);
                    setValidationResult(null);
                    setPendingFile(null);
                    setExistingExtensionName('');
                },
                (error) => {
                    const errorMsg = `覆盖安装失败: ${error.message}`;
                    message.error(errorMsg);
                    if (onUploadError) {
                        onUploadError(errorMsg);
                    }
                }
            );
        } catch (error) {
            setUploading(false);
            console.error('覆盖安装错误:', error);
            message.error(`覆盖安装失败: ${error.message}`);
        }
    }, [pendingFile, performUpload, onUploadSuccess, onUploadError]);

    // 取消覆盖安装
    const handleOverwriteCancel = useCallback(() => {
        setOverwriteModalVisible(false);
        setPendingFile(null);
        setExistingExtensionName('');
        setUploading(false);
        setUploadProgress(0);
    }, []);

    // 提交用户配置
    const handleUserConfigSubmit = useCallback(async () => {
        try {
            const values = await userConfigForm.validateFields();
            setUserConfigModalVisible(false);
            setUploading(true);
            setUploadProgress(0);

            // 调用带用户配置的安装接口
            const response = await fetch('/api/dxt/install-with-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    file_path: pendingFile.path || pendingFile.name, // 临时文件路径
                    user_config: values,
                    force_update: false
                })
            });

            const result = await response.json();

            if (result.success) {
                message.success(`扩展 "${result.data.name}" 安装成功！`);
                if (onUploadSuccess) {
                    onUploadSuccess(result.data);
                }
                // 清空状态
                setSelectedFile(null);
                setValidationResult(null);
                setPendingFile(null);
                setRequiredVariables([]);
                setConfigDefinitions({});
                userConfigForm.resetFields();
            } else {
                message.error(`安装失败: ${result.message}`);
                if (onUploadError) {
                    onUploadError(result.message);
                }
            }
        } catch (error) {
            console.error('用户配置提交失败:', error);
            message.error(`配置提交失败: ${error.message}`);
        } finally {
            setUploading(false);
            setUploadProgress(0);
        }
    }, [pendingFile, userConfigForm, onUploadSuccess, onUploadError]);

    // 取消用户配置
    const handleUserConfigCancel = useCallback(() => {
        setUserConfigModalVisible(false);
        setPendingFile(null);
        setRequiredVariables([]);
        setConfigDefinitions({});
        userConfigForm.resetFields();
        setUploading(false);
        setUploadProgress(0);
    }, [userConfigForm]);

    // 根据配置定义渲染表单控件
    const renderConfigInput = useCallback((varName, configDef) => {
        const { type = 'string', title, description, sensitive = false, min, max } = configDef;

        // 构建验证规则
        const rules = [
            { required: true, message: `请输入 ${title || varName}` }
        ];

        if (type === 'number' && (min !== undefined || max !== undefined)) {
            rules.push({
                type: 'number',
                min: min,
                max: max,
                message: `值必须在 ${min || '无限制'} 到 ${max || '无限制'} 之间`
            });
        }

        // 根据类型渲染不同的输入控件
        let inputComponent;
        switch (type) {
            case 'number':
                inputComponent = (
                    <InputNumber
                        style={{ width: '100%' }}
                        placeholder={`请输入 ${title || varName}`}
                        min={min}
                        max={max}
                    />
                );
                break;

            case 'boolean':
                inputComponent = (
                    <Switch
                        checkedChildren="是"
                        unCheckedChildren="否"
                    />
                );
                // 布尔类型不需要必填验证
                rules.splice(0, 1);
                break;

            case 'directory':
                inputComponent = (
                    <Input
                        placeholder={`请选择目录路径`}
                        suffix={<FolderOpenOutlined />}
                        autoComplete="off"
                    />
                );
                break;

            case 'file':
                inputComponent = (
                    <Input
                        placeholder={`请选择文件路径`}
                        suffix={<FileOutlined />}
                        autoComplete="off"
                    />
                );
                break;

            default: // string
                inputComponent = (
                    <Input
                        type={sensitive ? 'password' : 'text'}
                        placeholder={`请输入 ${title || varName}`}
                        autoComplete="off"
                    />
                );
                break;
        }

        return (
            <Form.Item
                key={varName}
                name={varName}
                label={
                    <Space>
                        <span>{title || varName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        {sensitive && <Tag color="orange" size="small">敏感</Tag>}
                    </Space>
                }
                help={description}
                rules={rules}
                valuePropName={type === 'boolean' ? 'checked' : 'value'}
            >
                {inputComponent}
            </Form.Item>
        );
    }, []);

    // 渲染验证结果
    const renderValidationResult = () => {
        if (!validationResult) return null;

        if (validationResult.valid && validationResult.manifest) {
            const { manifest } = validationResult;
            return (
                <Alert
                    type="success"
                    showIcon
                    icon={<CheckCircleOutlined />}
                    message="文件验证通过"
                    description={
                        <Space direction="vertical" size="small">
                            <Text><strong>名称:</strong> {manifest.name}</Text>
                            <Text><strong>版本:</strong> {manifest.version}</Text>
                            <Text><strong>描述:</strong> {manifest.description}</Text>
                            <Text><strong>作者:</strong> {
                                typeof manifest.author === 'string'
                                    ? manifest.author
                                    : (manifest.author?.name || '未知')
                            }</Text>
                        </Space>
                    }
                    style={{ marginTop: 16 }}
                />
            );
        } else {
            return (
                <Alert
                    type="error"
                    showIcon
                    icon={<ExclamationCircleOutlined />}
                    message="文件验证失败"
                    description={validationResult.error}
                    style={{ marginTop: 16 }}
                />
            );
        }
    };

    const uploadProps = {
        name: 'file',
        multiple: false,
        accept: '.dxt',
        disabled: disabled || uploading,
        customRequest: handleUpload,
        beforeUpload: beforeUpload,
        showUploadList: false, // 我们自定义显示逻辑
        onChange: (info) => {
            // 可以在这里处理文件状态变化
        },
        onDrop: (e) => {
            console.log('拖拽文件:', e.dataTransfer.files);
        },
    };

    return (
        <Card title="DXT扩展安装" className="dxt-upload-card">
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {/* 文件上传区域 */}
                <Dragger {...uploadProps} className="dxt-upload-dragger">
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined style={{ color: uploading ? '#ccc' : '#1890ff' }} />
                    </p>
                    <p className="ant-upload-text">
                        {uploading ? '正在上传...' : '点击或拖拽DXT文件到此区域上传'}
                    </p>
                    <p className="ant-upload-hint">
                        支持.dxt格式的扩展文件，文件大小不超过50MB
                        <br />
                        <Tag color="blue">安全验证</Tag>
                        <Tag color="green">自动安装</Tag>
                        <Tag color="orange">MCP集成</Tag>
                    </p>
                </Dragger>

                {/* 上传进度 */}
                {uploading && (
                    <div className="upload-progress">
                        <Text>上传进度:</Text>
                        <Progress 
                            percent={uploadProgress} 
                            status={uploadProgress === 100 ? 'success' : 'active'}
                            showInfo
                        />
                        {selectedFile && (
                            <Text type="secondary">
                                正在上传: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                            </Text>
                        )}
                    </div>
                )}

                {/* 验证结果展示 */}
                {renderValidationResult()}

                {/* 手动选择文件按钮 */}
                <div style={{ textAlign: 'center' }}>
                    <Upload {...uploadProps}>
                        <Button 
                            type="primary" 
                            icon={<UploadOutlined />} 
                            disabled={disabled || uploading}
                            loading={uploading}
                        >
                            {uploading ? '上传中...' : '选择DXT文件'}
                        </Button>
                    </Upload>
                </div>

                {/* 使用说明 */}
                <Alert
                    type="info"
                    showIcon
                    message="使用说明"
                    description={
                        <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                            <li>DXT扩展是包含manifest.json的ZIP格式文件</li>
                            <li>上传的扩展将自动解压并安装到系统</li>
                            <li>安装后的扩展会自动添加到MCP配置中</li>
                            <li>您可以随时在扩展列表中启用或禁用扩展</li>
                        </ul>
                    }
                />

                {/* 覆盖确认模态框 */}
                <Modal
                    title="扩展已存在"
                    open={overwriteModalVisible}
                    onOk={handleOverwriteConfirm}
                    onCancel={handleOverwriteCancel}
                    okText="覆盖安装"
                    cancelText="取消"
                    okButtonProps={{ danger: true }}
                >
                    <p>
                        扩展 <strong>"{existingExtensionName}"</strong> 已经存在。
                    </p>
                    <p>
                        是否要覆盖安装？这将替换现有的扩展文件和配置。
                    </p>
                </Modal>

                {/* 用户配置模态框 */}
                <Modal
                    title="配置扩展参数"
                    open={userConfigModalVisible}
                    onOk={handleUserConfigSubmit}
                    onCancel={handleUserConfigCancel}
                    okText="安装"
                    cancelText="取消"
                    width={600}
                    confirmLoading={uploading}
                >
                    <div style={{ marginBottom: 16 }}>
                        <p>此扩展需要配置以下参数才能正常工作：</p>
                    </div>

                    <Form
                        form={userConfigForm}
                        layout="vertical"
                        autoComplete="off"
                    >
                        {requiredVariables.map(varName => {
                            const configDef = configDefinitions[varName] || {};
                            return renderConfigInput(varName, configDef);
                        })}
                    </Form>

                    <Alert
                        message="提示"
                        description="这些参数将用于配置扩展的运行环境，请确保输入正确的值。"
                        type="info"
                        showIcon
                        style={{ marginTop: 16 }}
                    />
                </Modal>
            </Space>
        </Card>
    );
};

export default DXTUpload; 