<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style type="text/css">
            /* Basic Colors */
            .primary-color { fill: #1E40AF; }
            .secondary-color { fill: #475569; }
            .accent-color { fill: #3B82F6; }
            .background-color { fill: #F8FAFC; }
            .text-primary-color { fill: #1E293B; }
            .text-secondary-color { fill: #64748B; }
            .text-light-color { fill: #94A3B8; }
            .card-border-color { stroke: #BAE6FD; }
            .container-bg-color { fill: #E0F2FE; }

            /* Font Styles */
            .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
            .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
            .font-accent { font-family: 'Times New Roman', serif; }

            /* Text Sizes and Weights */
            .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
            .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
            .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; }
            .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; }
            .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
            .small-text { font-size: 16px; font-weight: 400; line-height: 1.4; }
            .caption-text { font-size: 14px; font-weight: 400; line-height: 1.4; }

            /* Other styles */
            .shadow-effect { filter: drop-shadow(0 4px 6px rgba(0,0,0,0.1)) drop-shadow(0 2px 4px rgba(0,0,0,0.06)); }
        </style>

        <!-- Gradients -->
        <linearGradient id="gradientBackground" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stop-color="#F8FAFC" />
            <stop offset="100%" stop-color="#E0F2FE" />
        </linearGradient>
        <linearGradient id="gradientAccent" x1="0" y1="0" x2="1" y2="1">
            <stop offset="0%" stop-color="#3B82F6" />
            <stop offset="100%" stop-color="#1E40AF" />
        </linearGradient>
        <linearGradient id="gradientPrimary" x1="0" y1="1" x2="1" y2="0">
            <stop offset="0%" stop-color="#1E40AF" />
            <stop offset="100%" stop-color="#475569" />
        </linearGradient>
    </defs>

    <!-- Background -->
    <rect x="0" y="0" width="1920" height="1080" fill="url(#gradientBackground)" />

    <!-- Large Section Number / Transition Element (03 for page 3, 10 for total pages) -->
    <text x="100" y="200" class="font-accent" style="font-size: 200px; font-weight: 900; fill: #BAE6FD; opacity: 0.5;">03</text>
    <text x="1700" y="900" class="font-accent" style="font-size: 200px; font-weight: 900; fill: #BAE6FD; opacity: 0.5; text-anchor: end;">10</text>

    <!-- Main Content Area - Chapter Title and Subtitle -->
    <g class="shadow-effect">
        <!-- Main Title -->
        <text x="960" y="480" text-anchor="middle" class="font-primary main-title text-primary-color">
            <tspan x="960" dy="0">{title}</tspan>
        </text>

        <!-- Subtitle -->
        <text x="960" y="550" text-anchor="middle" class="font-secondary section-title text-secondary-color">
            <tspan x="960" dy="0">{subtitle}</tspan>
        </text>
    </g>

    <!-- Decorative Elements - Medical, Data, Geometric -->

    <!-- Top Left Corner Bars -->
    <rect x="80" y="80" width="150" height="8" rx="4" fill="url(#gradientAccent)" />
    <rect x="80" y="96" width="100" height="8" rx="4" fill="url(#gradientAccent)" opacity="0.7" />

    <!-- Bottom Right Corner Bars (Mirrored Placement) -->
    <rect x="1690" y="992" width="150" height="8" rx="4" fill="url(#gradientAccent)" />
    <rect x="1740" y="976" width="100" height="8" rx="4" fill="url(#gradientAccent)" opacity="0.7" />

    <!-- Abstract Medical Heartbeat Line -->
    <polyline points="200 700 250 700 270 680 290 720 310 690 330 700 380 700"
              stroke="#3B82F6" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round" />
    <circle cx="200" cy="700" r="8" fill="#3B82F6" />
    <circle cx="380" cy="700" r="8" fill="#3B82F6" />

    <!-- Data Points / Network Visualization -->
    <g transform="translate(1500, 200)">
        <circle cx="0" cy="0" r="10" fill="#1E40AF" />
        <circle cx="80" cy="50" r="8" fill="#475569" />
        <circle cx="-50" cy="100" r="12" fill="#3B82F6" />
        <line x1="0" y1="0" x2="80" y2="50" stroke="#475569" stroke-width="2" />
        <line x1="0" y1="0" x2="-50" y2="100" stroke="#475569" stroke-width="2" />
        <line x1="80" y1="50" x2="-50" y2="100" stroke="#475569" stroke-width="2" />
    </g>

    <!-- Stylized DNA Helix (Simplified) -->
    <path d="M 120 900 C 150 850, 180 950, 210 900 S 270 850, 300 900"
          stroke="#1E40AF" stroke-width="3" fill="none" />
    <path d="M 130 900 C 160 855, 190 955, 220 905 S 280 855, 310 905"
          stroke="#3B82F6" stroke-width="3" fill="none" />
    <circle cx="120" cy="900" r="4" fill="#1E40AF" />
    <circle cx="210" cy="900" r="4" fill="#3B82F6" />
    <circle cx="300" cy="900" r="4" fill="#1E40AF" />

    <!-- Abstract Business / Medical Icon Placeholder (Stylized Cross) -->
    <g transform="translate(1600, 750)" stroke="#1E40AF" stroke-width="3" fill="none">
        <path d="M 0 0 L 50 0 L 50 50 L 0 50 Z" fill="#E0F2FE" />
        <line x1="25" y1="0" x2="25" y2="50" />
        <line x1="0" y1="25" x2="50" y2="25" />
    </g>

    <!-- Bento Grid Style Elements for Visual Interest -->
    <rect x="100" y="300" width="250" height="150" rx="12" class="container-bg-color card-border-color" stroke-width="1" />
    <rect x="1570" y="600" width="250" height="150" rx="12" class="container-bg-color card-border-color" stroke-width="1" />
    <rect x="1570" y="780" width="100" height="50" rx="8" fill="#3B82F6" opacity="0.1" />

    <!-- Placeholder for Date and Author -->
    <text x="1840" y="60" text-anchor="end" class="font-secondary small-text text-secondary-color">
        <tspan x="1840" dy="0">{date}</tspan>
        <tspan x="1840" dy="20">{author}</tspan>
    </text>

    <!-- Page Indicator -->
    <text x="80" y="1020" class="font-secondary caption-text text-light-color">
        <tspan x="80" dy="0">页面 3/10</tspan>
    </text>

</svg>