<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Colors -->
    <style type="text/css">
      .bg-color { fill: #F8FAFC; }
      .primary-color-fill { fill: #3B82F6; }
      .primary-color-stroke { stroke: #3B82F6; }
      .secondary-color-fill { fill: #7DD3FC; }
      .accent-color-fill { fill: #BAE6FD; }
      .card-bg { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1px; }

      /* Text Colors */
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }

      /* Fonts */
      .font-primary { font-family: 'Inter', 'Helvetica', 'Arial', sans-serif; }
      .font-secondary { font-family: 'SF Pro Display', system-ui, sans-serif; }
      .font-accent { font-family: 'Poppins', sans-serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; } /* bold */
      .section-title { font-size: 36px; font-weight: 600; line-height: 1.4; } /* semibold */
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; } /* medium */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; } /* normal */
      .font-bold { font-weight: 700; }
      .font-semibold { font-weight: 600; }
      .font-medium { font-weight: 500; }

      /* Outline Icon Style */
      .icon-outline { stroke: #3B82F6; stroke-width: 2px; fill: none; }
    </style>

    <!-- Gradients for subtle effects -->
    <linearGradient id="gradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>
    <linearGradient id="gradientAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="transparentDividerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#BAE6FD" stop-opacity="0"/>
      <stop offset="50%" stop-color="#BAE6FD" stop-opacity="1"/>
      <stop offset="100%" stop-color="#BAE6FD" stop-opacity="0"/>
    </linearGradient>

    <!-- Simple grid pattern for background texture (subtle) -->
    <pattern id="gridPattern" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 L 0 40" fill="none" stroke="#E0F2FE" stroke-width="0.5"/>
    </pattern>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Subtle grid pattern overlay -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#gridPattern)" opacity="0.3"/>

  <!-- Main Content Area - Margins: 80px horizontal, 60px vertical (from canvas edge) -->
  <g transform="translate(80 60)">
    <!-- Page Title -->
    <text x="0" y="60" class="main-title font-primary text-primary-color">
      <tspan x="80" y="60">学术成果分析</tspan>
      <tspan x="80" y="130" class="section-title font-primary text-secondary-color">Academic Research Insights</tspan>
    </text>

    <!-- Decorative line below title -->
    <rect x="80" y="160" width="100" height="4" rx="2" fill="url(#gradientPrimary)"/>

    <!-- Main Data Visualization Area (Left - Bento-like large block) -->
    <g transform="translate(80, 220)">
      <rect x="0" y="0" width="1000" height="700" class="card-bg card-border"/>

      <!-- Chart Title -->
      <text x="40" y="60" class="content-title font-primary text-primary-color">论文发表趋势</text>
      <text x="40" y="95" class="small-text font-primary text-secondary-color">Publication Trend Analysis</text>

      <!-- Placeholder Line Chart (Adjusted for 40px label spacing) -->
      <g transform="translate(100, 150)">
        <!-- X-axis labels -->
        <text x="0" y="380" class="caption-text font-primary text-secondary-color" text-anchor="middle">2018</text>
        <text x="150" y="380" class="caption-text font-primary text-secondary-color" text-anchor="middle">2019</text>
        <text x="300" y="380" class="caption-text font-primary text-secondary-color" text-anchor="middle">2020</text>
        <text x="450" y="380" class="caption-text font-primary text-secondary-color" text-anchor="middle">2021</text>
        <text x="600" y="380" class="caption-text font-primary text-secondary-color" text-anchor="middle">2022</text>
        <text x="750" y="380" class="caption-text font-primary text-secondary-color" text-anchor="middle">2023</text>

        <!-- Y-axis labels -->
        <text x="-40" y="350" text-anchor="end" class="caption-text font-primary text-secondary-color">0</text>
        <text x="-40" y="250" text-anchor="end" class="caption-text font-primary text-secondary-color">50</text>
        <text x="-40" y="150" text-anchor="end" class="caption-text font-primary text-secondary-color">100</text>
        <text x="-40" y="50" text-anchor="end" class="caption-text font-primary text-secondary-color">150</text>

        <!-- Axis lines -->
        <line x1="0" y1="0" x2="0" y2="360" stroke="#BAE6FD" stroke-width="1"/>
        <line x1="0" y1="360" x2="800" y2="360" stroke="#BAE6FD" stroke-width="1"/>

        <!-- Data Line - Simplified path -->
        <path d="M 0 350 L 150 280 L 300 200 L 450 250 L 600 120 L 750 50"
              class="primary-color-stroke" stroke-width="3" fill="none"/>

        <!-- Data Points (circles) -->
        <circle cx="0" cy="350" r="6" class="primary-color-fill"/>
        <circle cx="150" cy="280" r="6" class="primary-color-fill"/>
        <circle cx="300" cy="200" r="6" class="primary-color-fill"/>
        <circle cx="450" cy="250" r="6" class="primary-color-fill"/>
        <circle cx="600" cy="120" r="6" class="primary-color-fill"/>
        <circle cx="750" cy="50" r="6" class="primary-color-fill"/>

        <!-- Key Data Labels - avoid overlap, 40px spacing from chart elements -->
        <text x="600" y="80" class="small-text font-semibold text-primary-color" text-anchor="middle">120</text>
        <text x="750" y="10" class="small-text font-semibold text-primary-color" text-anchor="middle">150</text>
      </g>
    </g>

    <!-- Right Side Data Cards (Bento-like smaller blocks) -->
    <g transform="translate(1100, 220)">
      <!-- Card 1: Total Publications -->
      <rect x="0" y="0" width="700" height="220" class="card-bg card-border"/>
      <text x="40" y="60" class="content-title font-primary text-primary-color">总论文数</text>
      <text x="40" y="95" class="small-text font-primary text-secondary-color">Total Publications</text>
      <text x="40" y="170" class="hero-title font-primary primary-color-fill">
        <tspan>12,345</tspan>
      </text>

      <!-- Card 2: Citation Impact -->
      <rect x="0" y="240" width="700" height="220" class="card-bg card-border"/>
      <text x="40" y="290" class="content-title font-primary text-primary-color">引用影响力</text>
      <text x="40" y="325" class="small-text font-primary text-secondary-color">Citation Impact</text>
      <text x="40" y="400" class="hero-title font-primary primary-color-fill">
        <tspan>4.75</tspan>
      </text>
      <text x="280" y="400" class="section-title font-primary text-secondary-color">平均分</text>
      <text x="280" y="435" class="small-text font-primary text-light-color">Average Score</text>


      <!-- Card 3: Top Research Areas -->
      <rect x="0" y="480" width="700" height="440" class="card-bg card-border"/>
      <text x="40" y="530" class="content-title font-primary text-primary-color">热门研究领域</text>
      <text x="40" y="565" class="small-text font-primary text-secondary-color">Top Research Areas</text>

      <!-- List of areas (dy values adjusted for minimum 30px spacing) -->
      <g transform="translate(40, 620)">
        <text x="0" y="0" class="body-text font-primary text-primary-color">1. 人工智能和机器学习</text>
        <text x="0" y="35" class="small-text font-primary text-light-color">Artificial Intelligence 和#38; Machine Learning</text>

        <text x="0" y="80" class="body-text font-primary text-primary-color">2. 生物信息学和基因组学</text>
        <text x="0" y="115" class="small-text font-primary text-light-color">Bioinformatics 和#38; Genomics</text>

        <text x="0" y="160" class="body-text font-primary text-primary-color">3. 可持续能源技术</text>
        <text x="0" y="195" class="small-text font-primary text-light-color">Sustainable Energy Technologies</text>

        <text x="0" y="240" class="body-text font-primary text-primary-color">4. 材料科学和纳米技术</text>
        <text x="0" y="275" class="small-text font-primary text-light-color">Materials Science 和#38; Nanotechnology</text>

        <text x="0" y="320" class="body-text font-primary text-primary-color">5. 认知神经科学</text>
        <text x="0" y="355" class="small-text font-primary text-light-color">Cognitive Neuroscience</text>
      </g>
    </g>
  </g>
</svg>