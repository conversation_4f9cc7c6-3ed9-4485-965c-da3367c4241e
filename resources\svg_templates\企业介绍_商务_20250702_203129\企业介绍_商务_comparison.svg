<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Primary Gradient for text and highlights -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <!-- Accent Gradient for subtle effects -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <!-- Card Shadow filter to match design principles -->
    <filter id="cardShadow" x="-5%" y="-5%" width="110%" height="110%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="4"/>
      <feFlood flood-color="rgba(0, 0, 0, 0.1)" flood-opacity="1" result="floodColor"/>
      <feComposite in="floodColor" in2="blurOut" operator="in" result="shadowEffect"/>
      <feMerge>
        <feMergeNode in="shadowEffect"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <style>
    /* Global Styles for colors */
    .bg-color { fill: #F8FAFC; }
    .primary-color { fill: #1E40AF; }
    .secondary-color { fill: #475569; }
    .accent-color { fill: #3B82F6; }
    .text-primary-fill { fill: #1E293B; }
    .text-secondary-fill { fill: #64748B; }
    .text-light-fill { fill: #94A3B8; }
    .card-bg-fill { fill: #FFFFFF; }
    .card-border-stroke { stroke: #BAE6FD; }
    .container-bg-fill { fill: #E0F2FE; }

    /* Font Styles based on design norms */
    .font-primary { font-family: 'Microsoft YaHei, Segoe UI, sans-serif'; }
    .font-secondary { font-family: 'Source Han Sans CN, Noto Sans CJK SC, sans-serif'; }

    .main-title {
      font-size: 56px;
      font-weight: 700; /* bold */
      fill: #1E293B; /* text-primary */
    }

    .section-title {
      font-size: 36px;
      font-weight: 700; /* bold */
      fill: #1E293B; /* text-primary */
    }

    .content-title {
      font-size: 28px;
      font-weight: 600; /* semibold */
      fill: #1E293B; /* text-primary */
    }

    .body-text {
      font-size: 22px;
      font-weight: 400; /* normal */
      fill: #64748B; /* text-secondary */
    }

    .small-text {
      font-size: 16px;
      font-weight: 400; /* normal */
      fill: #64748B; /* text-secondary */
    }

    .large-number {
      font-size: 72px; /* hero_title size for numbers */
      font-weight: 900; /* black */
      fill: url(#primaryGradient); /* Gradient fill for emphasis */
    }

    /* Card visual style */
    .card-style {
      filter: url(#cardShadow); /* Apply shadow filter */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1px;
    }

    /* Icon general style */
    .icon-style {
      stroke: #1E40AF; /* primary color for icons */
      stroke-width: 2;
      fill: none;
    }

    /* Highlight style for differences */
    .diff-highlight {
      fill: #3B82F6; /* accent color */
    }
  </style>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- Decorative Elements - Subtle geometric shapes for visual interest -->
  <rect x="1500" y="50" width="300" height="150" rx="15" ry="15" class="primary-color" opacity="0.05"/>
  <circle cx="100" cy="900" r="80" class="accent-color" opacity="0.05"/>
  <rect x="50" y="50" width="100" height="100" rx="10" ry="10" class="secondary-color" opacity="0.03"/>

  <!-- Header Section: Title and Subtitle -->
  <text x="960" y="100" text-anchor="middle" class="main-title font-primary">{title}</text>
  <text x="960" y="150" text-anchor="middle" class="body-text font-secondary">
    <tspan x="960" dy="0">{subtitle}</tspan>
  </text>

  <!-- Main Comparison Area - Uses a Bento Grid like structure for clarity -->
  <g id="comparison-section">
    <!-- Left Card: Our Solution -->
    <rect x="100" y="250" width="750" height="600" rx="12" ry="12" class="card-bg-fill card-style"/>
    <text x="475" y="300" text-anchor="middle" class="content-title font-primary text-primary-fill">我们的方案</text>
    <text x="475" y="335" text-anchor="middle" class="small-text font-secondary text-secondary-fill">Our Solution</text>

    <!-- Left Card Icon (Placeholder: Checkmark in a circle) -->
    <g transform="translate(440, 380)">
      <circle cx="32" cy="32" r="28" class="icon-style" stroke-width="2"/>
      <path d="M22 32L28 38L42 24" class="icon-style" stroke-width="3"/>
    </g>

    <!-- Left Card Content - Each tspan has explicit x and dy for precise positioning -->
    <text x="140" y="450" class="body-text font-primary text-secondary-fill">
      <tspan x="140" dy="0">· {content} 创新技术</tspan>
      <tspan x="140" dy="30">· {content} 高效流程管理</tspan>
      <tspan x="140" dy="30">· {content} 卓越客户服务</tspan>
      <tspan x="140" dy="30">· {content} 灵活部署能力</tspan>
      <tspan x="140" dy="30">· {content} 持续优化迭代</tspan>
    </text>

    <!-- Right Card: Traditional Approach -->
    <rect x="1070" y="250" width="750" height="600" rx="12" ry="12" class="card-bg-fill card-style"/>
    <text x="1445" y="300" text-anchor="middle" class="content-title font-primary text-primary-fill">传统方法</text>
    <text x="1445" y="335" text-anchor="middle" class="small-text font-secondary text-secondary-fill">Traditional Approach</text>

    <!-- Right Card Icon (Placeholder: Cross in a circle) -->
    <g transform="translate(1410, 380)">
      <circle cx="32" cy="32" r="28" class="icon-style" stroke-width="2"/>
      <path d="M42 22L22 42" class="icon-style" stroke-width="3"/>
      <path d="M22 22L42 42" class="icon-style" stroke-width="3"/>
    </g>

    <!-- Right Card Content - Each tspan has explicit x and dy for precise positioning -->
    <text x="1110" y="450" class="body-text font-primary text-secondary-fill">
      <tspan x="1110" dy="0">· {content} 技术陈旧</tspan>
      <tspan x="1110" dy="30">· {content} 流程僵化</tspan>
      <tspan x="1110" dy="30">· {content} 响应缓慢</tspan>
      <tspan x="1110" dy="30">· {content} 部署复杂</tspan>
      <tspan x="1110" dy="30">· {content} 缺乏更新</tspan>
    </text>

    <!-- Central Difference Highlight Area -->
    <rect x="880" y="300" width="160" height="450" rx="10" ry="10" class="container-bg-fill" opacity="0.7"/>
    <text x="960" y="350" text-anchor="middle" class="section-title font-primary diff-highlight">差异</text>
    <text x="960" y="390" text-anchor="middle" class="small-text font-secondary diff-highlight">Key Differences</text>

    <!-- Difference points with large numbers for emphasis -->
    <text x="960" y="470" text-anchor="middle" class="large-number font-primary">1</text>
    <text x="960" y="520" text-anchor="middle" class="body-text font-primary text-primary-fill">
      <tspan x="960" dy="0" text-anchor="middle">性能</tspan>
      <tspan x="960" dy="30" text-anchor="middle" class="small-text text-secondary-fill">Performance</tspan>
    </text>

    <text x="960" y="600" text-anchor="middle" class="large-number font-primary">2</text>
    <text x="960" y="650" text-anchor="middle" class="body-text font-primary text-primary-fill">
      <tspan x="960" dy="0" text-anchor="middle">成本</tspan>
      <tspan x="960" dy="30" text-anchor="middle" class="small-text text-secondary-fill">Cost Efficiency</tspan>
    </text>

    <text x="960" y="730" text-anchor="middle" class="large-number font-primary">3</text>
    <text x="960" y="780" text-anchor="middle" class="body-text font-primary text-primary-fill">
      <tspan x="960" dy="0" text-anchor="middle">未来</tspan>
      <tspan x="960" dy="30" text-anchor="middle" class="small-text text-secondary-fill">Future-Proof</tspan>
    </text>

  </g>

  <!-- Conclusion Section - Prominent call to action -->
  <g id="conclusion-section">
    <rect x="100" y="900" width="1720" height="120" rx="12" ry="12" class="accent-color" opacity="0.8"/>
    <text x="960" y="940" text-anchor="middle" class="section-title font-primary" fill="#FFFFFF">
      <tspan x="960" dy="0">选择我们</tspan>
      <tspan x="960" dy="40" class="small-text" fill="#E0F2FE">Choose Us For Superior Results</tspan>
    </text>
  </g>

  <!-- Logo Placeholder (Bottom Left) -->
  <rect x="80" y="1040" width="100" height="30" fill="#D1D5DB"/>
  <text x="130" y="1060" text-anchor="middle" class="small-text" fill="#6B7280">Logo</text>

  <!-- Date Placeholder (Bottom Right) -->
  <text x="1840" y="1060" text-anchor="end" class="small-text font-secondary text-secondary-fill">{date}</text>
</svg>