<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 统一蓝色系配色与字体样式定义 -->
    <style type="text/css">
      /* 背景色 */
      .bg-color { fill: #F8FAFC; }

      /* 主要文字颜色 */
      .text-primary { fill: #1E293B; }
      /* 辅助文字颜色 */
      .text-secondary { fill: #64748B; }
      /* 浅色文字 */
      .text-light { fill: #94A3B8; }

      /* 主色调 */
      .primary-color { fill: #1E40AF; }
      /* 强调色 */
      .accent-color { fill: #3B82F6; }
      /* 辅助色 */
      .secondary-color { fill: #475569; }

      /* 卡片背景与边框 */
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1px; }
      /* 卡片阴影滤镜引用 */
      .card-shadow { filter: url(#cardShadow); }

      /* 字体族 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* 字体大小与粗细 */
      .hero-title { font-size: 72px; font-weight: 700; }
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 700; }
      .content-title { font-size: 28px; font-weight: 700; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; }
      .small-text { font-size: 16px; font-weight: 400; }
      .caption-text { font-size: 14px; font-weight: 400; }

      /* 图标样式 */
      .icon-stroke { stroke: #3B82F6; stroke-width: 2; fill: none; }
      .icon-fill-accent { fill: #3B82F6; }
      .icon-fill-success { fill: #10B981; } /* 成功色 */
      .icon-fill-error { fill: #EF4444; } /* 错误色，用于强调差异 */

      /* 特定元素样式 */
      .comparison-card {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        border-radius: 12px;
        filter: url(#cardShadow);
      }
      .conclusion-card {
        fill: #E0F2FE; /* container_background */
        stroke: #BAE6FD;
        stroke-width: 1px;
        border-radius: 12px;
        filter: url(#cardShadow);
      }
      .highlight-text-blue {
        fill: #3B82F6;
      }
      .highlight-text-red {
        fill: #EF4444; /* 使用错误色作为差异点的强调色 */
      }
    </style>

    <!-- 卡片阴影滤镜 -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 装饰性渐变（科技感） -->
    <linearGradient id="decorativeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6; stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#1E40AF; stop-opacity:0.2" />
    </linearGradient>

    <!-- 图标定义：成功（对勾） -->
    <symbol id="iconCheck" viewBox="0 0 24 24">
      <path d="M20 6L9 17L4 12" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

    <!-- 图标定义：错误（叉号） -->
    <symbol id="iconCross" viewBox="0 0 24 24">
      <path d="M18 6L6 18M6 6L18 18" class="icon-stroke" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

    <!-- 图标定义：信息（圆圈i） -->
    <symbol id="iconInfo" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" class="icon-stroke"/>
      <line x1="12" y1="8" x2="12" y2="12" class="icon-stroke" stroke-linecap="round"/>
      <line x1="12" y1="16" x2="12" y2="16" class="icon-stroke" stroke-linecap="round"/>
    </symbol>

  </defs>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color"/>

  <!-- 装饰性元素 - 浅色网格/线条（Bento Grid风格的结构感） -->
  <g opacity="0.1">
    <line x1="80" y1="100" x2="1840" y2="100" stroke="#BAE6FD" stroke-width="1"/>
    <line x1="80" y1="1000" x2="1840" y2="1000" stroke="#BAE6FD" stroke-width="1"/>
    <line x1="80" y1="100" x2="80" y2="1000" stroke="#BAE6FD" stroke-width="1"/>
    <line x1="1840" y1="100" x2="1840" y2="1000" stroke="#BAE6FD" stroke-width="1"/>

    <!-- 垂直分割线，用于辅助布局 -->
    <line x1="960" y1="100" x2="960" y2="900" stroke="#BAE6FD" stroke-width="1"/>
    <line x1="520" y1="100" x2="520" y2="900" stroke="#BAE6FD" stroke-width="0.5"/>
    <line x1="1400" y1="100" x2="1400" y2="900" stroke="#BAE6FD" stroke-width="0.5"/>
  </g>


  <!-- 页面头部区 -->
  <g id="header">
    <!-- Logo 占位符 -->
    <rect x="80" y="60" width="120" height="40" fill="#1E40AF" rx="8"/>
    <text x="140" y="88" text-anchor="middle" class="small-text font-primary" fill="#FFFFFF">
      <tspan>LOGO</tspan>
    </text>

    <!-- 标题 -->
    <text x="960" y="100" text-anchor="middle" class="main-title font-primary text-primary">
      <tspan>{title}</tspan>
    </text>
    <!-- 副标题 -->
    <text x="960" y="160" text-anchor="middle" class="section-title font-primary text-secondary">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- 主要内容区 - 对比分析部分 -->
  <g id="comparison-section">
    <!-- 左侧对比卡片 -->
    <rect x="120" y="240" width="780" height="500" rx="12" class="comparison-card"/>
    <text x="510" y="280" text-anchor="middle" class="content-title font-primary text-primary">
      <tspan>方案一</tspan>
      <tspan x="510" dy="36">Solution A</tspan>
    </text>

    <!-- 左侧内容要点 -->
    <g class="body-text font-primary text-secondary">
      <text x="160" y="380">
        <tspan>• 核心优势：{content}</tspan>
      </text>
      <text x="160" y="420">
        <tspan>• 实施周期：{content}</tspan>
      </text>
      <text x="160" y="460">
        <tspan>• 预期收益：{content}</tspan>
      </text>
      <text x="160" y="500">
        <tspan>• 风险评估：{content}</tspan>
      </text>
      <text x="160" y="540">
        <tspan>• 资源需求：{content}</tspan>
      </text>
    </g>

    <!-- 左侧卡片装饰图标/图形 -->
    <circle cx="510" cy="650" r="60" class="icon-stroke" stroke-width="3" stroke-dasharray="8 8"/>
    <use href="#iconInfo" x="486" y="626" width="48" height="48" class="icon-fill-accent"/>

    <!-- 右侧对比卡片 -->
    <rect x="1020" y="240" width="780" height="500" rx="12" class="comparison-card"/>
    <text x="1410" y="280" text-anchor="middle" class="content-title font-primary text-primary">
      <tspan>方案二</tspan>
      <tspan x="1410" dy="36">Solution B</tspan>
    </text>

    <!-- 右侧内容要点 -->
    <g class="body-text font-primary text-secondary">
      <text x="1060" y="380">
        <tspan>• 核心优势：{content}</tspan>
      </text>
      <text x="1060" y="420">
        <tspan>• 实施周期：{content}</tspan>
      </text>
      <text x="1060" y="460">
        <tspan>• 预期收益：{content}</tspan>
      </text>
      <text x="1060" y="500">
        <tspan>• 风险评估：{content}</tspan>
      </text>
      <text x="1060" y="540">
        <tspan>• 资源需求：{content}</tspan>
      </text>
    </g>

    <!-- 右侧卡片装饰图标/图形 -->
    <rect x="1350" y="590" width="120" height="120" rx="20" class="icon-stroke" stroke-width="3" stroke-dasharray="8 8"/>
    <path d="M1380 650 L1400 620 L1440 650 L1400 680 Z" class="icon-fill-accent"/> <!-- 简单箭头形状 -->

  </g>

  <!-- 差异强调区（中间部分） -->
  <g id="difference-emphasis">
    <rect x="915" y="450" width="90" height="180" rx="12" fill="#E0F2FE" stroke="#BAE6FD" stroke-width="1" class="card-shadow"/>
    <text x="960" y="500" text-anchor="middle" class="content-title font-primary highlight-text-blue">
      <tspan>VS</tspan>
    </text>
    <text x="960" y="550" text-anchor="middle" class="small-text font-primary text-secondary">
      <tspan>差异</tspan>
    </text>
    <text x="960" y="580" text-anchor="middle" class="small-text font-primary text-secondary">
      <tspan>Points</tspan>
    </text>

    <!-- 关键差异指标 -->
    <!-- 差异点1: 左侧优势 -->
    <use href="#iconCheck" x="910" y="380" width="48" height="48" class="icon-fill-success"/>
    <text x="960" y="408" class="small-text font-primary text-secondary">
      <tspan>成本更优</tspan>
    </text>

    <!-- 差异点2: 右侧劣势 -->
    <use href="#iconCross" x="960" y="580" width="48" height="48" class="icon-fill-error"/>
    <text x="960" y="608" class="small-text font-primary text-secondary">
      <tspan>周期较长</tspan>
    </text>

  </g>

  <!-- 结论总结区 -->
  <g id="conclusion-section">
    <rect x="120" y="770" width="1680" height="200" rx="12" class="conclusion-card"/>
    <text x="960" y="820" text-anchor="middle" class="section-title font-primary text-primary">
      <tspan>结论和建议</tspan>
      <tspan x="960" dy="40" class="content-title font-primary text-secondary">Conclusion and Recommendations</tspan>
    </text>
    <text x="960" y="900" text-anchor="middle" class="body-text font-primary text-secondary">
      <tspan>{content}</tspan>
    </text>
  </g>

  <!-- 页脚区 -->
  <g id="footer">
    <!-- 日期 -->
    <text x="80" y="1040" class="small-text font-primary text-light">
      <tspan>日期: {date}</tspan>
    </text>
    <!-- 作者 -->
    <text x="1840" y="1040" text-anchor="end" class="small-text font-primary text-light">
      <tspan>作者: {author}</tspan>
    </text>
    <!-- 页码 -->
    <text x="960" y="1040" text-anchor="middle" class="small-text font-primary text-light">
      <tspan>7 / 10</tspan>
    </text>
  </g>

</svg>