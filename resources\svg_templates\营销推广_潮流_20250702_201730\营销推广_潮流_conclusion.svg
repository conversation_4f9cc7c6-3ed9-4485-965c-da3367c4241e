<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主色渐变 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(135)">
      <stop stop-color="#4A86E8"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- 强调色渐变 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(45)">
      <stop stop-color="#0EA5E9"/>
      <stop offset="1" stop-color="#4A86E8"/>
    </linearGradient>

    <!-- 文本渐变 (用于特殊标题或装饰) -->
    <linearGradient id="textGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(135)">
      <stop stop-color="#1E3A8A"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.2 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- CSS 样式定义 -->
    <style type="text/css">
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .bg-primary-color { fill: #4A86E8; }
      .bg-accent-color { fill: #0EA5E9; }
      .bg-card { fill: #FFFFFF; }
      .border-card { stroke: #BAE6FD; stroke-width: 1px; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      .shadowed-card { filter: url(#cardShadow); }
      .shadowed-text { filter: url(#textShadow); }

      /* 号召行动按钮样式 */
      .cta-button {
        fill: url(#accentGradient);
        cursor: pointer;
      }
      .cta-button-text {
        fill: #FFFFFF;
        font-size: 28px;
        font-weight: 600;
        text-anchor: middle;
      }

      /* 图标样式 */
      .icon-style {
        stroke: #4A86E8;
        stroke-width: 2;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      /* 针对静态SVG，hover效果仅为CSS语法示例，不直接在SVG中交互 */
      .cta-button:hover {
        opacity: 0.9;
      }
      .card-item:hover .card-bg {
        /* 在实际交互中，此处可以定义更复杂的阴影或变换 */
        filter: url(#cardShadow); /* 示例：保持相同阴影或定义新滤镜 */
      }
    </style>
  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 装饰性几何图形 - 潮流感与科技感 -->
  <g opacity="0.1">
    <circle cx="1750" cy="150" r="100" fill="url(#primaryGradient)"/>
    <rect x="1600" y="800" width="300" height="300" rx="40" fill="url(#accentGradient)"/>
    <polygon points="100 950, 400 1080, 0 1080" fill="#4A86E8"/>
    <path d="M0 0 L 300 0 C 400 50 200 150 300 200 L 0 200 Z" fill="#3B82F6"/>
  </g>

  <!-- 主内容区域 -->
  <g transform="translate(80 60)">
    <!-- 头部 -->
    <image x="0" y="0" width="160" height="80" href="{logo_url}" />
    <text x="960" y="60" text-anchor="middle" class="main-title font-primary text-primary-color shadowed-text">核心总结和未来展望</text>
    <text x="960" y="120" text-anchor="middle" class="body-text font-secondary text-secondary-color shadowed-text">Core Summary and Future Outlook</text>

    <!-- 第一部分: 主要结论 (Bento Grid 风格布局) -->
    <g transform="translate(0 180)">
      <text x="0" y="0" class="section-title font-primary text-primary-color">主要结论</text>
      <text x="0" y="40" class="small-text font-secondary text-secondary-color">Key Takeaways from Our Analysis</text>

      <!-- 结论卡片 1 -->
      <g transform="translate(0 100)" class="card-item">
        <rect x="0" y="0" width="550" height="200" rx="12" class="bg-card border-card shadowed-card card-bg"/>
        <text x="30" y="50" class="content-title font-primary text-primary-color">01. 市场领先</text>
        <text x="30" y="90" class="body-text font-secondary text-secondary-color">产品在细分市场中表现卓越和持续增长。</text>
        <text x="30" y="130" class="small-text font-secondary text-light-color">Market Leadership: Our product excels and grows continuously in its niche.</text>
      </g>

      <!-- 结论卡片 2 -->
      <g transform="translate(600 100)" class="card-item">
        <rect x="0" y="0" width="550" height="200" rx="12" class="bg-card border-card shadowed-card card-bg"/>
        <text x="30" y="50" class="content-title font-primary text-primary-color">02. 创新优势</text>
        <text x="30" y="90" class="body-text font-secondary text-secondary-color">独特技术和用户体验是核心竞争力。</text>
        <text x="30" y="130" class="small-text font-secondary text-light-color">Innovation Edge: Unique technology and user experience are core strengths.</text>
      </g>

      <!-- 结论卡片 3 -->
      <g transform="translate(1200 100)" class="card-item">
        <rect x="0" y="0" width="550" height="200" rx="12" class="bg-card border-card shadowed-card card-bg"/>
        <text x="30" y="50" class="content-title font-primary text-primary-color">03. 用户价值</text>
        <text x="30" y="90" class="body-text font-secondary text-secondary-color">为客户创造显著价值和满意度高。</text>
        <text x="30" y="130" class="small-text font-secondary text-light-color">User Value: Delivering significant value and high satisfaction to customers.</text>
      </g>
    </g>

    <!-- 第二部分: 行动要点 -->
    <g transform="translate(0 500)">
      <text x="0" y="0" class="section-title font-primary text-primary-color">行动要点</text>
      <text x="0" y="40" class="small-text font-secondary text-secondary-color">Strategic Steps for Future Growth</text>

      <!-- 行动要点 1 (超大字体强调) -->
      <g transform="translate(0 100)">
        <rect x="0" y="0" width="860" height="150" rx="12" class="bg-primary-color shadowed-card"/>
        <text x="430" y="75" text-anchor="middle" class="hero-title font-primary" fill="#FFFFFF">拓展市场</text>
        <text x="430" y="120" text-anchor="middle" class="body-text font-secondary" fill="#E0F2FE">Expand Market Reach Globally</text>
      </g>

      <!-- 行动要点 2 (卡片风格) -->
      <g transform="translate(900 100)" class="card-item">
        <rect x="0" y="0" width="850" height="150" rx="12" class="bg-card border-card shadowed-card card-bg"/>
        <text x="30" y="50" class="content-title font-primary text-primary-color">优化产品</text>
        <text x="30" y="90" class="body-text font-secondary text-secondary-color">持续迭代和增强用户体验。</text>
        <text x="30" y="130" class="small-text font-secondary text-light-color">Optimize Product: Continuous iteration and UX enhancement.</text>
        <!-- 图标占位符 (简单勾线图形) -->
        <circle cx="800" cy="75" r="30" class="icon-style"/>
        <path d="M785 75 L815 75 M800 60 L800 90" class="icon-style"/>
      </g>
    </g>

    <!-- 第三部分: 号召行动和联系信息 -->
    <g transform="translate(0 750)">
      <text x="0" y="0" class="section-title font-primary text-primary-color">联系我们</text>
      <text x="0" y="40" class="small-text font-secondary text-secondary-color">Get in Touch with Our Team</text>

      <!-- 联系信息卡片 -->
      <g transform="translate(0 100)" class="card-item">
        <rect x="0" y="0" width="860" height="150" rx="12" class="bg-card border-card shadowed-card card-bg"/>
        <text x="30" y="50" class="content-title font-primary text-primary-color">电子邮件: info和#64;example.com</text>
        <text x="30" y="90" class="body-text font-secondary text-secondary-color">电话: +86 123 4567 8901</text>
        <text x="30" y="130" class="small-text font-secondary text-light-color">地址: 某市某区某路123号</text>
      </g>

      <!-- 号召行动按钮 -->
      <g transform="translate(900 100)">
        <rect x="0" y="0" width="850" height="150" rx="12" class="cta-button"/>
        <text x="425" y="75" class="cta-button-text font-primary">立即体验产品</text>
        <text x="425" y="120" class-name="small-text font-secondary" fill="#E0F2FE" text-anchor="middle">Experience the Product Now</text>
      </g>
    </g>

    <!-- 感谢语/结束语 -->
    <text x="960" y="980" text-anchor="middle" class="body-text font-primary text-secondary-color">感谢您的时间！我们期待与您合作。</text>
    <text x="960" y="1010" text-anchor="middle" class="small-text font-secondary text-light-color">Thank you for your time! We look forward to collaborating with you.</text>
  </g>
</svg>