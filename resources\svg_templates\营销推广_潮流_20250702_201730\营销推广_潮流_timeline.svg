<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Styles -->
    <style type="text/css">
      /* Colors */
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .background-color { fill: #F8FAFC; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes */
      .hero-title { font-size: 72px; }
      .main-title { font-size: 56px; }
      .section-title { font-size: 36px; }
      .content-title { font-size: 28px; }
      .body-text { font-size: 22px; }
      .small-text { font-size: 16px; }
      .caption { font-size: 14px; }

      /* Font Weights */
      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }

      /* Other styles */
      .timeline-line {
        stroke: #BAE6FD; /* card-border color for consistency */
        stroke-width: 4;
        stroke-linecap: round;
      }
      .timeline-node {
        fill: #4A86E8; /* primary_color */
        stroke: #FFFFFF;
        stroke-width: 4;
      }
      .milestone-node {
        fill: #0EA5E9; /* accent_color */
        stroke: #FFFFFF;
        stroke-width: 6;
      }
      .icon-stroke {
        stroke: #4A86E8; /* primary_color */
        stroke-width: 2;
        fill: none;
      }
    </style>

    <!-- Filters for shadows -->
    <filter id="cardShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4" result="offsetblur"/>
      <feGaussianBlur in="offsetblur" stdDeviation="3" result="shadow"/>
      <feColorMatrix in="shadow" mode="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <filter id="milestoneShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="8" result="offsetblur"/>
      <feGaussianBlur in="offsetblur" stdDeviation="5" result="shadow"/>
      <feColorMatrix in="shadow" mode="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.15 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9"/>
      <stop offset="100%" stop-color="#4A86E8"/>
    </linearGradient>

    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative elements (geometric shapes, subtle patterns) -->
  <circle cx="1800" cy="100" r="80" fill="#0EA5E9" opacity="0.1"/>
  <circle cx="100" cy="980" r="120" fill="#4A86E8" opacity="0.08"/>
  <path d="M 0 0 L 300 0 L 150 150 L 0 100 Z" fill="#3B82F6" opacity="0.05"/>
  <path d="M 1920 1080 L 1620 1080 L 1770 930 L 1920 980 Z" fill="#0EA5E9" opacity="0.05"/>

  <!-- Main Content Area -->
  <g transform="translate(80 60)"> <!-- Apply page margins -->
    <!-- Header Section -->
    <text x="880" y="60" text-anchor="middle" class="section-title font-bold text-primary font-primary">
      <tspan x="880" dy="0">{title}：产品发展里程碑</tspan>
      <tspan x="880" dy="45" class="content-title font-normal text-secondary font-primary">{subtitle}: Our Product Development Milestones</tspan>
    </text>

    <!-- Timeline Layout -->
    <!-- Central Timeline Line -->
    <line x1="880" y1="200" x2="880" y2="950" class="timeline-line"/>

    <!-- Timeline Nodes and Events -->

    <!-- Event 1: Initial Concept and Research -->
    <circle cx="880" cy="250" r="12" class="timeline-node"/>
    <rect x="980" y="210" width="400" height="120" rx="12" ry="12" class="card-background" filter="url(#cardShadow)"/>
    <text x="1000" y="245" class="content-title font-semibold text-primary font-primary">
      <tspan x="1000" dy="0">{date}：概念启动</tspan>
      <tspan x="1000" dy="35" class="body-text font-normal text-secondary font-primary">Initial Concept 和#38; Market Research</tspan>
      <tspan x="1000" dy="30" class="small-text font-normal text-light font-primary">{content}奠定产品基础，分析市场需求和用户痛点。</tspan>
    </text>

    <!-- Event 2: Core Technology Development -->
    <circle cx="880" cy="400" r="12" class="timeline-node"/>
    <rect x="430" y="360" width="400" height="120" rx="12" ry="12" class="card-background" filter="url(#cardShadow)"/>
    <text x="450" y="395" text-anchor="start" class="content-title font-semibold text-primary font-primary">
      <tspan x="450" dy="0">{date}：核心技术攻克</tspan>
      <tspan x="450" dy="35" class="body-text font-normal text-secondary font-primary">Core Technology Breakthrough</tspan>
      <tspan x="450" dy="30" class="small-text font-normal text-light font-primary">{content}自主研发关键技术，实现产品核心功能。</tspan>
    </text>

    <!-- Event 3: Beta Launch Milestone -->
    <circle cx="880" cy="550" r="18" class="milestone-node" filter="url(#milestoneShadow)"/>
    <rect x="980" y="510" width="400" height="140" rx="12" ry="12" class="card-background" filter="url(#cardShadow)"/>
    <text x="1000" y="545" class="content-title font-bold text-primary font-primary">
      <tspan x="1000" dy="0">{date}：内测版本发布</tspan>
      <tspan x="1000" dy="35" class="body-text font-normal text-secondary font-primary">Beta Version Launch</tspan>
      <tspan x="1000" dy="30" class="small-text font-normal text-light font-primary">{content}首批用户反馈收集，产品迭代优化。</tspan>
      <tspan x="1000" dy="30" class="small-text font-normal text-light font-primary">里程碑事件：产品可用性大大提升。</tspan>
    </text>

    <!-- Event 4: Official Product Launch -->
    <circle cx="880" cy="700" r="12" class="timeline-node"/>
    <rect x="430" y="660" width="400" height="120" rx="12" ry="12" class="card-background" filter="url(#cardShadow)"/>
    <text x="450" y="695" text-anchor="start" class="content-title font-semibold text-primary font-primary">
      <tspan x="450" dy="0">{date}：产品正式上线</tspan>
      <tspan x="450" dy="35" class="body-text font-normal text-secondary font-primary">Official Product Launch</tspan>
      <tspan x="450" dy="30" class="small-text font-normal text-light font-primary">{content}面向市场全面推广，用户量快速增长。</tspan>
    </text>

    <!-- Event 5: Feature Expansion and Growth -->
    <circle cx="880" cy="850" r="12" class="timeline-node"/>
    <rect x="980" y="810" width="400" height="120" rx="12" ry="12" class="card-background" filter="url(#cardShadow)"/>
    <text x="1000" y="845" class="content-title font-semibold text-primary font-primary">
      <tspan x="1000" dy="0">{date}：功能迭代升级</tspan>
      <tspan x="1000" dy="35" class="body-text font-normal text-secondary font-primary">Feature Expansion 和#38; Growth</tspan>
      <tspan x="1000" dy="30" class="small-text font-normal text-light font-primary">{content}不断创新，满足用户多样化需求。</tspan>
    </text>

    <!-- Visual guidance: Arrow at the end of the timeline -->
    <polygon points="880,950 870,940 890,940" fill="#BAE6FD"/>

    <!-- Call to Action / Footer -->
    <g transform="translate(0 950)">
      <text x="880" y="60" text-anchor="middle" class="body-text font-normal text-secondary font-primary">
        <tspan x="880" dy="0">我们持续创新，为您提供卓越的产品和体验。</tspan>
        <tspan x="880" dy="30" class="small-text font-normal text-light font-primary">We continue to innovate to provide you with excellent products 和#38; experiences.</tspan>
      </text>
    </g>

  </g>

  <!-- Logo Placeholder (top-left) -->
  <text x="80" y="80" class="content-title font-bold text-primary font-primary">
    <tspan>{logo_url}LOGO</tspan>
  </text>
  <rect x="70" y="50" width="100" height="40" stroke="#4A86E8" stroke-width="2" fill="none" rx="5" ry="5"/>

</svg>