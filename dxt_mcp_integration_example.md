# DXT扩展与MCP配置集成示例

## 概述

现在DXT管理器已经完全支持将DXT扩展的MCP配置自动集成到项目的mcp.json文件中，这样您就可以在MCP管理页面直接管理这些扩展。

## 功能特性

### ✅ 自动MCP配置集成
- DXT扩展安装后自动添加到项目的mcp.json配置文件
- 支持DXT规范的路径占位符 `${__dirname}` 自动替换
- 环境变量中的路径占位符也会被正确处理
- 扩展卸载后自动从MCP配置中移除

### ✅ 完整的生命周期管理
- 安装时：验证 → 解压 → 配置MCP → 启动服务器
- 卸载时：停止服务器 → 清理配置 → 删除文件

### ✅ MCP管理页面兼容
- 生成的配置完全兼容现有的MCP管理界面
- 支持启用/禁用、查看状态、重启等操作

## DXT Manifest示例

创建一个包含MCP配置的DXT扩展manifest.json：

```json
{
  "dxt_version": "0.1",
  "name": "my-awesome-extension",
  "version": "1.0.0",
  "description": "我的超棒DXT扩展",
  "author": {
    "name": "Your Name",
    "email": "<EMAIL>"
  },
  "server": {
    "type": "node",
    "entry_point": "server/index.js",
    "mcp_config": {
      "command": "node",
      "args": [
        "${__dirname}/server/index.js",
        "--config=${__dirname}/config.json"
      ],
      "env": {
        "DEBUG": "true",
        "DATA_PATH": "${__dirname}/data"
      }
    }
  },
  "tools": [
    {
      "name": "my_tool",
      "description": "我的工具"
    }
  ]
}
```

## 路径占位符处理

DXT管理器会自动处理以下路径占位符：

### `${__dirname}` 占位符
- **原始路径**: `${__dirname}/server/index.js`
- **替换后**: `/path/to/extensions/my-extension/server/index.js`

### 支持的位置
1. **命令参数** (`args`)
2. **环境变量** (`env`)
3. **命令本身** (`command`) - 如果需要

## 生成的MCP配置

安装上述DXT扩展后，会在mcp.json中生成如下配置：

```json
{
  "mcpServers": {
    "my-awesome-extension": {
      "command": "node",
      "args": [
        "/path/to/extensions/my-awesome-extension/server/index.js",
        "--config=/path/to/extensions/my-awesome-extension/config.json"
      ],
      "env": {
        "DEBUG": "true",
        "DATA_PATH": "/path/to/extensions/my-awesome-extension/data"
      },
      "enabled": true,
      "description": "DXT扩展: 我的超棒DXT扩展",
      "isActive": false
    }
  }
}
```

## 使用流程

### 1. 上传DXT文件
通过 `/api/dxt/upload` 接口上传.dxt文件：

```bash
curl -X POST \
  -F "file=@my-extension.dxt" \
  -F "force_update=false" \
  http://localhost:8000/api/dxt/upload
```

### 2. 自动配置集成
- ✅ 扩展文件自动解压到安装目录
- ✅ MCP配置自动添加到mcp.json
- ✅ 路径占位符自动替换为实际路径
- ✅ MCP服务器自动启动（如果配置正确）

### 3. MCP管理页面操作
现在您可以在MCP管理页面看到新安装的扩展：
- 查看扩展状态（运行中/已停止）
- 启用/禁用扩展
- 重启扩展服务器
- 查看扩展工具列表

### 4. 卸载扩展
通过 `/api/dxt/uninstall` 接口卸载扩展：

```bash
curl -X DELETE \
  http://localhost:8000/api/dxt/extensions/my-awesome-extension
```

- ✅ MCP服务器自动停止
- ✅ 配置自动从mcp.json移除
- ✅ 扩展文件自动删除

## 错误处理

### 常见问题及解决方案

1. **MCP配置更新失败**
   - 检查mcp.json文件权限
   - 确保配置文件格式正确

2. **MCP服务器启动失败**
   - 检查扩展的entry_point文件是否存在
   - 验证命令和参数是否正确
   - 查看服务器日志获取详细错误信息

3. **路径占位符未替换**
   - 确保使用正确的DXT规范占位符 `${__dirname}`
   - 检查manifest.json中的server.mcp_config配置

## 最佳实践

1. **使用相对路径**: 在manifest中使用 `${__dirname}` 占位符而不是绝对路径
2. **完整测试**: 在打包DXT前确保MCP服务器可以正常运行
3. **错误处理**: 在MCP服务器代码中添加适当的错误处理
4. **文档说明**: 为扩展提供清晰的使用说明和工具文档

## 技术细节

### 支持的DXT规范版本
- DXT版本: 0.1
- 必需字段: `dxt_version`, `name`, `version`, `description`, `author`, `server`

### MCP配置兼容性
- 完全兼容现有MCP管理系统
- 支持stdio类型的MCP服务器
- 自动处理服务器生命周期管理

这个集成功能让DXT扩展的管理变得更加简单和统一，用户可以通过熟悉的MCP管理界面来操作所有的扩展。
