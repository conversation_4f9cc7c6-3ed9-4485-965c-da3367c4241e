<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义颜色和渐变 -->
    <style type="text/css"><![CDATA[
      /* 基础颜色 */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E3A8A; }
      .secondary-color { fill: #1E40AF; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-bg { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }

      /* 字体定义 */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* 字体大小和粗细 */
      .hero-title { font-size: 72px; font-weight: 700; } /* bold */
      .main-title { font-size: 56px; font-weight: 700; } /* bold */
      .section-title { font-size: 36px; font-weight: 600; } /* semibold */
      .content-title { font-size: 28px; font-weight: 500; } /* medium */
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.4; } /* normal */
      .small-text { font-size: 16px; font-weight: 400; } /* normal */
      .caption-text { font-size: 14px; font-weight: 400; } /* normal */

      /* 渐变填充样式 */
      .primary-gradient-fill {
        fill: url(#primaryGradient);
      }
      .accent-gradient-fill {
        fill: url(#accentGradient);
      }
      .bg-gradient-fill {
        fill: url(#backgroundGradient);
      }
      .text-gradient-fill {
        fill: url(#textGradient);
      }
      .hover-color-fill { fill: #7DD3FC; }

      /* 卡片阴影样式 */
      .card-shadow {
        filter: url(#cardShadowFilter);
      }

      /* 图标描边样式 */
      .icon-stroke { stroke: #4A86E8; stroke-width: 2; }
      .icon-fill { fill: #4A86E8; }

      /* 特定元素样式 */
      .main-title-style {
        font-size: 56px;
        font-weight: 700; /* bold */
        fill: #1E3A8A; /* 主色用于标题 */
        text-anchor: middle;
      }
      .subtitle-style {
        font-size: 28px;
        font-weight: 500; /* medium */
        fill: #64748B; /* 辅助文字色 */
        text-anchor: middle;
      }
      .body-text-style {
        font-size: 22px;
        font-weight: 400;
        fill: #1E293B; /* 主要文字色 */
      }
      .list-item-style {
        font-size: 22px;
        font-weight: 400;
        fill: #1E293B;
      }
      .accent-text {
        fill: #3B82F6; /* 强调色 */
      }
      .emphasis-large {
        font-size: 120px; /* 超大字体用于强调 */
        font-weight: 900; /* 最粗 */
        fill: #1E3A8A; /* 主蓝色 */
        text-anchor: start;
      }
      .emphasis-label {
        font-size: 36px;
        font-weight: 600;
        fill: #1E293B;
      }
      .emphasis-en {
        font-size: 20px;
        font-weight: 300; /* 细体 */
        fill: #64748B;
        text-anchor: start;
      }

    ]]></style>

    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E3A8A" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- 滤镜：卡片/元素阴影 -->
    <filter id="cardShadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4" result="blur" />
      <feOffset dx="0" dy="4" result="offsetBlur" />
      <feFlood flood-color="rgba(0, 0, 0, 0.1)" flood-opacity="1" result="color" />
      <feComposite in="color" in2="offsetBlur" operator="in" result="shadow" />
      <feMerge>
        <feMergeNode in="shadow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- 滤镜：文字/Logo阴影 -->
    <filter id="textShadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="1" result="blur" />
      <feOffset dx="0" dy="1" result="offsetBlur" />
      <feFlood flood-color="rgba(0, 0, 0, 0.08)" flood-opacity="1" result="color" />
      <feComposite in="color" in2="offsetBlur" operator="in" result="shadow" />
      <feMerge>
        <feMergeNode in="shadow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

    <!-- 背景网格图案 -->
    <pattern id="gridPattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="#E0F2FE" stroke-width="1" />
    </pattern>

  </defs>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-gradient-fill" />
  <!-- 细微网格图案叠加 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#gridPattern)" opacity="0.4" />

  <!-- 装饰元素（几何图形，细微渐变） -->
  <g opacity="0.6">
    <rect x="0" y="0" width="300" height="1080" fill="#1E3A8A" opacity="0.05" />
    <rect x="1620" y="0" width="300" height="1080" fill="#1E3A8A" opacity="0.05" />
    <circle cx="1700" cy="100" r="150" fill="#3B82F6" opacity="0.08" />
    <circle cx="220" cy="980" r="120" fill="#1E40AF" opacity="0.07" />
    <path d="M 0 0 L 400 0 L 0 400 Z" fill="#1E3A8A" opacity="0.1" />
    <path d="M 1920 0 L 1520 0 L 1920 400 Z" fill="#1E3A8A" opacity="0.1" />
    <path d="M 0 1080 L 400 1080 L 0 680 Z" fill="#1E3A8A" opacity="0.1" />
    <path d="M 1920 1080 L 1520 1080 L 1920 680 Z" fill="#1E3A8A" opacity="0.1" />
  </g>

  <!-- 页眉 - Logo和页面信息 -->
  <g id="header">
    <image href="{logo_url}" x="80" y="60" width="160" height="auto" filter="url(#textShadowFilter)" />
    <text x="1840" y="90" class="caption-text text-secondary font-primary" text-anchor="end">页面 4/10</text>
  </g>

  <!-- 主要内容区域 -->
  <g id="main-content">
    <!-- 标题 -->
    <text x="960" y="200" class="main-title-style font-primary">{title}</text>
    <text x="960" y="260" class="subtitle-style font-secondary">{subtitle}</text>

    <!-- 内容卡片区域 -->
    <rect x="160" y="360" width="1600" height="600" rx="24" ry="24" class="card-bg card-shadow" />
    <rect x="160" y="360" width="1600" height="600" rx="24" ry="24" class="card-border" stroke-width="1" fill="none" />

    <!-- 左侧区域: 主要段落内容 -->
    <g id="text-section">
      <text x="240" y="420" class="content-title font-primary text-primary">
        <tspan>政策解读和工作成果</tspan>
      </text>

      <text x="240" y="480" class="body-text-style font-secondary">
        <tspan x="240" dy="0em">在党中央的坚强领导下，我们深入贯彻落实各项政策方针，</tspan>
        <tspan x="240" dy="30px">取得了显著的工作成果。这些成果凝聚了全体党员干部和</tspan>
        <tspan x="240" dy="30px">广大人民群众的智慧和汗水，为社会发展奠定了坚实基础。</tspan>
        <tspan x="240" dy="30px">我们将持续推进改革创新，不断提升治理能力，确保各项</tspan>
        <tspan x="240" dy="30px">事业高质量发展，为实现中华民族伟大复兴的中国梦贡献力量。</tspan>
      </text>
    </g>

    <!-- 右侧区域: 要点列表和强调区域 -->
    <g id="list-section">
      <text x="1000" y="420" class="content-title font-primary text-primary">
        <tspan>核心要点</tspan>
      </text>

      <!-- 要点列表 -->
      <text x="1000" y="480" class="list-item-style font-secondary">
        <tspan x="1000" dy="0em">
          <tspan class="accent-text">• </tspan>
          <tspan>强化理论武装，坚定理想信念</tspan>
        </tspan>
        <tspan x="1000" dy="35px">
          <tspan class="accent-text">• </tspan>
          <tspan>深化改革开放，激发发展活力</tspan>
        </tspan>
        <tspan x="1000" dy="35px">
          <tspan class="accent-text">• </tspan>
          <tspan>坚持以民为本，增进民生福祉</tspan>
        </tspan>
        <tspan x="1000" dy="35px">
          <tspan class="accent-text">• </tspan>
          <tspan>加强党的建设，提升执政能力</tspan>
        </tspan>
      </text>

      <!-- 强调区域（借鉴Bento风格，但使用蓝色系） -->
      <g id="emphasis-block">
        <rect x="1000" y="700" width="680" height="200" rx="12" ry="12" fill="#E0F2FE" stroke="#BAE6FD" stroke-width="1" />
        <text x="1040" y="790" class="emphasis-large font-primary">
          <tspan>95<tspan class="accent-text">%</tspan></tspan>
        </text>
        <text x="1040" y="820" class="emphasis-en font-accent">
          <tspan>SUCCESS RATE</tspan>
        </text>
        <text x="1040" y="860" class="emphasis-label font-primary">
          <tspan>政策落实率</tspan>
        </text>

        <!-- 小型装饰图标 -->
        <circle cx="1600" cy="740" r="30" fill="#3B82F6" opacity="0.15" />
        <path d="M 1585 740 L 1615 740 M 1600 725 L 1600 755" stroke="#3B82F6" stroke-width="3" stroke-linecap="round" />
      </g>
    </g>
  </g>

  <!-- 页脚 - 日期和作者 -->
  <g id="footer">
    <text x="1840" y="1020" class="small-text text-secondary font-primary" text-anchor="end">
      <tspan>{date} · {author}</tspan>
    </text>
  </g>

</svg>