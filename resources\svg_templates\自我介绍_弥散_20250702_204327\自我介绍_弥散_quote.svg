<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 背景渐变，用于弥散风格的柔和背景 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主色渐变，用于装饰元素和强调 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(135)">
      <stop stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 强调色渐变，用于视觉高亮 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(45)">
      <stop stop-color="#06B6D4"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- 柔和模糊滤镜，创建弥散效果 -->
    <filter id="softBlur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="80" result="blur"/>
      <feOffset in="blur" dx="0" dy="0" result="offsetBlur"/>
      <feComposite in="SourceGraphic" in2="offsetBlur" operator="over"/>
    </filter>

    <!-- 文本渐变，用于重要文本的视觉增强 -->
    <linearGradient id="textGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(135)">
      <stop stop-color="#1E3A8A"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 卡片阴影滤镜 -->
    <filter id="cardShadow">
      <feOffset dx="0" dy="4" in="SourceAlpha" result="offset1"/>
      <feGaussianBlur stdDeviation="3" in="offset1" result="blur1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" in="blur1" result="shadow1"/>

      <feOffset dx="0" dy="2" in="SourceAlpha" result="offset2"/>
      <feGaussianBlur stdDeviation="2" in="offset2" result="blur2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0" in="blur2" result="shadow2"/>

      <feMerge>
        <feMergeNode in="shadow1"/>
        <feMergeNode in="shadow2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 装饰性引用符号路径 - 左引号 -->
    <path id="quoteOpen" d="M30 0C13.4315 0 0 13.4315 0 30V100C0 116.569 13.4315 130 30 130H100V60H60C60 43.4315 46.5685 30 30 30V0ZM180 0C163.431 0 150 13.4315 150 30V100C150 116.569 163.431 130 180 130H250V60H210C210 43.4315 196.569 30 180 30V0Z"/>
    <!-- 装饰性引用符号路径 - 右引号 -->
    <path id="quoteClose" d="M220 130C236.569 130 250 116.569 250 100V30C250 13.4315 236.569 0 220 0H150V70H190C190 86.5685 203.431 100 220 100V130ZM70 130C86.5685 130 100 116.569 100 100V30C100 13.4315 86.5685 0 70 0H0V70H40C40 86.5685 53.4315 100 70 100V130Z"/>

  </defs>

  <style>
    /* 全局字体设置 */
    svg {
      font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
      fill: #1E293B; /* 默认文本颜色 */
    }

    /* 文本样式定义 */
    .main-title {
      font-size: 56px; /* main_title */
      font-weight: 700; /* bold */
      fill: #1E293B; /* text_primary */
      text-anchor: middle;
    }
    .subtitle-text {
      font-size: 22px; /* body_text, for subtitle context */
      font-weight: 400; /* normal */
      fill: #64748B; /* text_secondary */
      text-anchor: middle;
    }
    .quote-text {
      font-size: 48px; /* 突出引用内容 */
      font-weight: 600; /* semibold */
      fill: #1E293B; /* text_primary */
      text-anchor: middle;
      line-height: 1.6; /* relaxed */
    }
    .author-text {
      font-size: 28px; /* content_title size */
      font-weight: 500; /* medium */
      fill: #64748B; /* text_secondary */
      text-anchor: middle;
    }
    .page-info-text {
      font-size: 16px; /* small_text */
      font-weight: 400;
      fill: #94A3B8; /* text_light */
    }

    /* 卡片样式 */
    .card {
      fill: #FFFFFF; /* card_background */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1;
      rx: 12; /* border_radius */
      ry: 12;
      filter: url(#cardShadow); /* 应用阴影 */
    }

    /* 装饰性元素样式 */
    .decorative-shape-1 {
      fill: url(#primaryGradient);
      opacity: 0.3; /* 柔和性 */
      filter: url(#softBlur); /* 弥散效果 */
    }
    .decorative-shape-2 {
      fill: url(#accentGradient);
      opacity: 0.2; /* 柔和性 */
      filter: url(#softBlur); /* 弥散效果 */
    }
    .quote-mark-style {
      fill: #BAE6FD; /* 与边框色协调 */
      opacity: 0.6; /* 装饰性，不抢内容 */
    }

    /* Logo样式 */
    .logo-style {
      filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.1)); /* subtle drop shadow */
    }

  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 弥散风格装饰性形状 (背景) -->
  <circle cx="200" cy="150" r="180" class="decorative-shape-1"/>
  <ellipse cx="1700" cy="950" rx="200" ry="120" class="decorative-shape-2"/>
  <rect x="50" y="700" width="250" height="150" rx="75" ry="75" class="decorative-shape-1" transform="rotate(20 170 775)"/>
  <circle cx="1000" cy="100" r="100" class="decorative-shape-2"/>
  <rect x="1550" y="50" width="150" height="200" rx="50" ry="50" class="decorative-shape-2" transform="rotate(10 1625 150)"/>

  <!-- 主内容区域 - 居中引用卡片 -->
  <!-- 卡片尺寸: 宽度1200px, 高度580px, 居中 -->
  <rect x="360" y="250" width="1200" height="580" class="card"/>

  <!-- 顶部标题和副标题 (居中) -->
  <text x="960" y="160" class="main-title">
    <tspan x="960" dy="0">{title}</tspan>
  </text>
  <text x="960" y="200" class="subtitle-text">
    <tspan x="960" dy="0">{subtitle}</tspan>
  </text>

  <!-- 装饰性引用符号 -->
  <use xlink:href="#quoteOpen" x="420" y="300" class="quote-mark-style" transform="scale(0.8)"/>
  <use xlink:href="#quoteClose" x="1480" y="700" class="quote-mark-style" transform="scale(0.8)"/>

  <!-- 引用内容文本 -->
  <!-- 使用tspan分行，并确保足够行间距 (dy至少50px) -->
  <text x="960" y="470" class="quote-text">
    <tspan x="960" dy="0">{content_line1}</tspan>
    <tspan x="960" dy="50">{content_line2}</tspan>
    <tspan x="960" dy="50">{content_line3}</tspan>
    <tspan x="960" dy="50">{content_line4}</tspan>
  </text>

  <!-- 作者或来源信息 -->
  <text x="960" y="750" class="author-text">— {author}</text>

  <!-- 顶部左侧Logo -->
  <!-- 假设Logo高度最大80px，宽度自适应，这里使用固定值150x80作为示例 -->
  <image xlink:href="{logo_url}" x="80" y="60" width="150" height="80" class="logo-style"/>

  <!-- 底部右侧页码信息 -->
  <text x="1760" y="1020" text-anchor="end" class="page-info-text">9/10</text>

  <!-- 其他占位符（如果需要，可以在此页面添加或在其他页面使用） -->
  <!-- <text x="..." y="..." class="caption-text">{date}</text> -->
  <!-- <image xlink:href="{image_url}" x="..." y="..." width="..." height="..."/> -->

</svg>