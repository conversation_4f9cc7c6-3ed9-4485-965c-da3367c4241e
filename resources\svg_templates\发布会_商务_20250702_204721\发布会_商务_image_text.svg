<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="accentGradientTransparent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.6"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0"/>
    </linearGradient>

    <!-- 定义阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="rgba(0, 0, 0, 0.1)"/>
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="rgba(0, 0, 0, 0.06)"/>
    </filter>
  </defs>

  <style>
    /* 颜色调色板 */
    .bg-primary { fill: #1E40AF; }
    .bg-secondary { fill: #475569; }
    .bg-accent { fill: #3B82F6; }
    .bg-background { fill: #F8FAFC; }
    .text-primary-color { fill: #1E293B; }
    .text-secondary-color { fill: #64748B; }
    .text-light-color { fill: #94A3B8; }
    .card-bg { fill: #FFFFFF; }
    .card-border { stroke: #BAE6FD; }

    /* 字体样式 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    .text-hero-title { font-size: 72px; font-weight: 700; } /* 粗体 */
    .text-main-title { font-size: 56px; font-weight: 700; } /* 粗体 */
    .text-section-title { font-size: 36px; font-weight: 600; } /* 半粗体 */
    .text-content-title { font-size: 28px; font-weight: 500; } /* 中等粗细 */
    .text-body { font-size: 22px; font-weight: 400; line-height: 35px;} /* 普通 */
    .text-small { font-size: 16px; font-weight: 400; } /* 普通 */
    .text-caption { font-size: 14px; font-weight: 400; } /* 普通 */

    /* 通用样式 */
    .shadow-effect { filter: url(#shadow); }
    
    /* 图片边框 */
    .image-frame {
      stroke: #BAE6FD; /* card_border */
      stroke-width: 2px;
      fill: none; /* 边框本身无填充 */
    }
  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-background"/>

  <!-- 头部区域 -->
  <g id="header">
    <!-- Logo 占位符 -->
    <image href="{logo_url}" x="80" y="60" width="150" height="40" preserveAspectRatio="xMidYMid meet"/>
    <!-- 日期占位符 -->
    <text x="1840" y="85" text-anchor="end" class="font-secondary text-small text-secondary-color">
      <tspan x="1840" y="85">{date}</tspan>
    </text>
  </g>

  <!-- 主要内容区域 -->
  <g id="main-content">
    <!-- 图片栏 (左侧) -->
    <g id="image-column">
      <!-- 图片容器背景 (卡片样式) -->
      <rect x="130" y="200" width="700" height="525" rx="12" ry="12" class="card-bg shadow-effect"/>
      <!-- 图片占位符 (在卡片内部) -->
      <image href="{image_url}" x="140" y="210" width="680" height="505" preserveAspectRatio="xMidYMid slice" />
      <!-- 图片边框 -->
      <rect x="130" y="200" width="700" height="525" rx="12" ry="12" class="image-frame"/>

      <!-- 装饰元素: 简洁的轮廓图形/数据可视化占位符 -->
      <g id="data-chart" class="text-secondary-color" stroke-width="2">
        <line x1="160" y1="835" x2="800" y2="835" stroke="#BAE6FD"/> <!-- X轴 -->
        <line x1="160" y1="835" x2="160" y2="775" stroke="#BAE6FD"/> <!-- Y轴 -->

        <!-- 柱状图条形 -->
        <rect x="200" y="815" width="40" height="20" fill="#3B82F6"/>
        <rect x="260" y="795" width="40" height="40" fill="#3B82F6"/>
        <rect x="320" y="785" width="40" height="50" fill="#3B82F6"/>
        <rect x="380" y="805" width="40" height="30" fill="#3B82F6"/>
        <rect x="440" y="775" width="40" height="60" fill="#3B82F6"/>
        <rect x="500" y="800" width="40" height="35" fill="#3B82F6"/>
        <rect x="560" y="765" width="40" height="70" fill="#3B82F6"/>
        <rect x="620" y="820" width="40" height="15" fill="#3B82F6"/>
        <rect x="680" y="790" width="40" height="45" fill="#3B82F6"/>

        <text x="160" y="865" class="font-secondary text-caption text-secondary-color">数据亮点概览 / Data Overview</text>
      </g>
    </g>

    <!-- 文本栏 (右侧) -->
    <g id="text-column">
      <text x="910" y="280" class="font-primary text-hero-title text-primary-color">
        <tspan x="910" y="280">{title}</tspan>
      </text>

      <text x="910" y="402" class="font-secondary text-content-title text-secondary-color">
        <tspan x="910" y="402">{subtitle}</tspan>
      </text>

      <!-- 正文内容 (5行，每行间距35px，确保大于30px) -->
      <text x="910" y="480" class="font-secondary text-body text-primary-color">
        <tspan x="910" y="480">
          {content}
        </tspan>
        <tspan x="910" dy="35">
          这是关于产品亮点、重要信息以及媒体关注点的详细说明。
        </tspan>
        <tspan x="910" dy="35">
          我们致力于提供卓越的解决方案，以满足客户的需求和期望。
        </tspan>
        <tspan x="910" dy="35">
          创新和可靠性是我们的核心价值，推动我们不断前进。
        </tspan>
        <tspan x="910" dy="35">
          感谢媒体朋友和公众的关注和支持。
        </tspan>
      </text>

      <!-- 装饰元素: 强调线 -->
      <rect x="910" y="692" width="80" height="4" fill="url(#accentGradient)"/>

      <!-- 核心功能亮点 (卡片形式，1行2列) -->
      <g id="features">
        <rect x="910" y="746" width="280" height="80" rx="8" ry="8" class="card-bg shadow-effect card-border" stroke-width="1"/>
        <text x="930" y="791" class="font-secondary text-small text-primary-color">
          <tspan x="930" y="791">核心功能一：卓越性能</tspan>
        </text>

        <rect x="1220" y="746" width="280" height="80" rx="8" ry="8" class="card-bg shadow-effect card-border" stroke-width="1"/>
        <text x="1240" y="791" class="font-secondary text-small text-primary-color">
          <tspan x="1240" y="791">核心功能二：智能集成</tspan>
        </text>
      </g>
    </g>
  </g>

  <!-- 底部区域 -->
  <g id="footer">
    <text x="960" y="929" text-anchor="middle" class="font-secondary text-caption text-secondary-color">
      <tspan x="960" y="929">© 2023 {author}。保留所有权利。</tspan>
    </text>
  </g>

  <!-- 装饰性几何元素 (微妙点缀) -->
  <g id="decorative-elements">
    <!-- 右上角强调 -->
    <circle cx="1800" cy="100" r="60" fill="url(#accentGradientTransparent)"/>
    <rect x="1750" y="50" width="100" height="10" rx="5" ry="5" fill="#3B82F6" opacity="0.5"/>

    <!-- 左下角强调 -->
    <rect x="50" y="950" width="120" height="10" rx="5" ry="5" fill="#3B82F6" opacity="0.5" transform="rotate(-45 50 950)"/>
    <circle cx="150" cy="980" r="40" fill="url(#accentGradientTransparent)"/>
  </g>
</svg>