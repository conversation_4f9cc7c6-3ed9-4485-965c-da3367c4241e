#!/usr/bin/env node

/**
 * Test DXT Extension Server
 * 用于测试用户配置功能的简单MCP服务器
 */

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} = require('@modelcontextprotocol/sdk/types.js');

// 从环境变量读取配置
const config = {
  apiKey: process.env.API_KEY,
  databaseUrl: process.env.DATABASE_URL,
  port: parseInt(process.env.PORT) || 3000,
  debug: process.env.DEBUG === 'true',
  allowedPaths: process.env.ALLOWED_PATHS ? process.env.ALLOWED_PATHS.split(',') : []
};

console.error('Test Extension Config:', JSON.stringify(config, null, 2));

class TestExtensionServer {
  constructor() {
    this.server = new Server(
      {
        name: 'test-user-config',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'test_tool',
            description: 'Test tool that shows current configuration',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name } = request.params;

      if (name === 'test_tool') {
        return {
          content: [
            {
              type: 'text',
              text: `Test Extension Configuration:
API Key: ${config.apiKey ? '***' : 'Not set'}
Database URL: ${config.databaseUrl || 'Not set'}
Port: ${config.port}
Debug: ${config.debug}
Allowed Paths: ${config.allowedPaths.join(', ') || 'None'}`,
            },
          ],
        };
      }

      throw new Error(`Unknown tool: ${name}`);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Test Extension Server running...');
  }
}

const server = new TestExtensionServer();
server.run().catch(console.error);
