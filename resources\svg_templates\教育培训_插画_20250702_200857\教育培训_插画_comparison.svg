<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style>
      /* Base Styles */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1; }
      .container-background { fill: #E0F2FE; }

      /* Font Styles */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: '<PERSON> <PERSON>s <PERSON>N', '<PERSON>o Sans CJK SC', sans-serif; }

      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Card Styles */
      .card {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1;
        rx: 12; /* border-radius */
        ry: 12;
        filter: url(#card-shadow);
      }

      /* Gradients */
      /* Primary gradient: linear-gradient(135deg, #4A86E8, #3B82F6) */
      .gradient-primary-fill { fill: url(#primaryGradient); }
      /* Accent gradient: linear-gradient(45deg, #0EA5E9, #4A86E8) */
      .gradient-accent-fill { fill: url(#accentGradient); }
      /* Background gradient: linear-gradient(180deg, #F8FAFC, #E0F2FE) */
      .gradient-bg-fill { fill: url(#backgroundGradient); }
      /* Text gradient: linear-gradient(135deg, #1E3A8A, #1E40AF) */
      .gradient-text-fill { fill: url(#textGradient); }

      /* Specific element styles for comparison page */
      .comparison-card-header {
        font-size: 36px; /* section_title */
        font-weight: 700;
        fill: #1E293B; /* text_primary */
      }
      .comparison-item-title {
        font-size: 28px; /* content_title */
        font-weight: 600;
        fill: #1E293B; /* text_primary */
      }
      .comparison-item-text {
        font-size: 22px; /* body_text */
        font-weight: 400;
        fill: #64748B; /* text_secondary */
      }
      .conclusion-title {
        font-size: 36px; /* section_title */
        font-weight: 700;
        fill: #1E293B; /* text_primary */
      }
      .conclusion-text {
        font-size: 22px; /* body_text */
        font-weight: 400;
        fill: #64748B; /* text_secondary */
      }
      .accent-highlight-text {
        font-size: 56px; /* main_title */
        font-weight: 700;
        fill: #0EA5E9; /* accent_color */
      }
      .icon-style {
        stroke: #4A86E8; /* primary_color */
        stroke-width: 2;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
      }
      .highlight-icon-style {
        stroke: #0EA5E9; /* accent_color */
        stroke-width: 3;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

    </style>

    <!-- Filters for shadows -->
    <filter id="card-shadow" x="-5%" y="-5%" width="110%" height="110%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="6"></feGaussianBlur>
      <feOffset dx="0" dy="4" result="offsetblur"></feOffset>
      <feFlood flood-color="rgba(0,0,0,0.1)"></feFlood>
      <feComposite in2="offsetblur" operator="in"></feComposite>
      <feMerge>
        <feMergeNode></feMergeNode>
        <feMergeNode in="SourceGraphic"></feMergeNode>
      </feMerge>
      <!-- Smaller shadow to mimic the combined shadow effect -->
      <feGaussianBlur in="SourceAlpha" stdDeviation="2"></feGaussianBlur>
      <feOffset dx="0" dy="2" result="offsetblur2"></feOffset>
      <feFlood flood-color="rgba(0,0,0,0.06)"></feFlood>
      <feComposite in2="offsetblur2" operator="in"></feComposite>
      <feMerge>
        <feMergeNode></feMergeNode>
        <feMergeNode in="SourceGraphic"></feMergeNode>
      </feMerge>
    </filter>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E3A8A" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <!-- Reusable Icon Definitions -->
    <!-- Icon for Learning (Book) -->
    <symbol id="icon-book" viewBox="0 0 24 24">
      <path d="M4 19.5V10.5C4 9.94772 4.44772 9.5 5 9.5H19C19.5523 9.5 20 9.94772 20 10.5V19.5M4 19.5C4 20.0523 4.44772 20.5 5 20.5H19C19.5523 20.5 20 20.0523 20 19.5M4 19.5V4.5C4 3.94772 4.44772 3.5 5 3.5H12M20 19.5V4.5C20 3.94772 19.5523 3.5 19 3.5H12M12 3.5V20.5" />
    </symbol>
    <!-- Icon for Innovation (Bulb) -->
    <symbol id="icon-bulb" viewBox="0 0 24 24">
      <path d="M9 18H15M12 2C15.866 2 19 5.13401 19 9C19 11.451 17.6534 13.5615 15.6983 14.8879C15.2215 15.2078 14.9997 15.7601 15 16.3262V18H9V16.3262C9.00028 15.7601 8.77853 15.2078 8.30173 14.8879C6.3466 13.5615 5 11.451 5 9C5 5.13401 8.13401 2 12 2ZM12 22V18" />
    </symbol>
    <!-- Icon for Growth (Chart) -->
    <symbol id="icon-growth" viewBox="0 0 24 24">
      <path d="M3 17V7C3 6.44772 3.44772 6 4 6H20C20.5523 6 21 6.44772 21 7V17M3 17H21M3 17V21M21 17V21M7 10V14M12 8V16M17 12V16" />
    </symbol>
    <!-- Icon for Checkmark -->
    <symbol id="icon-check" viewBox="0 0 24 24">
      <path d="M5 13L9 17L19 7" />
    </symbol>
    <!-- Icon for Cross -->
    <symbol id="icon-cross" viewBox="0 0 24 24">
      <path d="M6 6L18 18M6 18L18 6" />
    </symbol>
    <!-- Icon for Arrow Right -->
    <symbol id="icon-arrow-right" viewBox="0 0 24 24">
      <path d="M5 12H19M19 12L12 5M19 12L12 19" />
    </symbol>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="gradient-bg-fill" />

  <!-- Decorative Elements - Subtle geometric shapes -->
  <circle cx="1700" cy="150" r="80" fill="#4A86E8" opacity="0.05" />
  <rect x="100" y="900" width="150" height="80" rx="12" ry="12" fill="#3B82F6" opacity="0.05" />
  <!-- A subtle wave-like decorative element at the bottom -->
  <path d="M0 700 Q 400 650 800 700 T 1600 700 T 1920 700 V 1080 H 0 Z" fill="#4A86E8" opacity="0.03" />

  <!-- Header Section -->
  <g>
    <!-- Logo Placeholder -->
    <image href="{logo_url}" x="80" y="60" width="120" height="60" preserveAspectRatio="xMidYMid meet"/>
    <!-- Title -->
    <text x="960" y="100" text-anchor="middle" class="main-title font-primary text-primary">
      <tspan x="960" dy="0">对比分析：传统教学 和 互动学习</tspan>
    </text>
    <!-- Subtitle -->
    <text x="960" y="160" text-anchor="middle" class="content-title font-secondary text-secondary">
      <tspan x="960" dy="0">深入探讨两种教学模式的优缺点</tspan>
    </text>
  </g>

  <!-- Main Content - Comparison Section -->
  <g>
    <!-- Left Comparison Card: 传统教学 (Traditional Teaching) -->
    <rect x="80" y="240" width="792" height="500" class="card" />
    <text x="120" y="290" class="comparison-card-header font-primary">
      <tspan x="120" dy="0">传统教学</tspan>
      <tspan x="120" dy="40" class="small-text text-secondary">Traditional Teaching</tspan>
    </text>

    <!-- Left Card Content -->
    <g transform="translate(120, 390)">
      <use xlink:href="#icon-book" x="0" y="0" width="32" height="32" class="icon-style" />
      <text x="40" y="24" class="comparison-item-title font-primary">
        <tspan x="40" dy="0">知识单向传递</tspan>
      </text>
      <text x="40" y="56" class="comparison-item-text font-secondary">
        <tspan x="40" dy="0">教师主导，学生被动接收。</tspan>
      </text>

      <use xlink:href="#icon-cross" x="0" y="100" width="32" height="32" class="icon-style" />
      <text x="40" y="124" class="comparison-item-title font-primary">
        <tspan x="40" dy="0">互动性较弱</tspan>
      </text>
      <text x="40" y="156" class="comparison-item-text font-secondary">
        <tspan x="40" dy="0">缺乏讨论和实践机会。</tspan>
      </text>

      <use xlink:href="#icon-growth" x="0" y="200" width="32" height="32" class="icon-style" />
      <text x="40" y="224" class="comparison-item-title font-primary">
        <tspan x="40" dy="0">学习成果评估</tspan>
      </text>
      <text x="40" y="256" class="comparison-item-text font-secondary">
        <tspan x="40" dy="0">侧重记忆和标准化测试。</tspan>
      </text>
    </g>

    <!-- Right Comparison Card: 互动学习 (Interactive Learning) -->
    <rect x="1048" y="240" width="792" height="500" class="card" />
    <text x="1088" y="290" class="comparison-card-header font-primary">
      <tspan x="1088" dy="0">互动学习</tspan>
      <tspan x="1088" dy="40" class="small-text text-secondary">Interactive Learning</tspan>
    </text>

    <!-- Right Card Content -->
    <g transform="translate(1088, 390)">
      <use xlink:href="#icon-bulb" x="0" y="0" width="32" height="32" class="icon-style" />
      <text x="40" y="24" class="comparison-item-title font-primary">
        <tspan x="40" dy="0">双向知识交流</tspan>
      </text>
      <text x="40" y="56" class="comparison-item-text font-secondary">
        <tspan x="40" dy="0">师生共同参与，共同探索。</tspan>
      </text>

      <use xlink:href="#icon-check" x="0" y="100" width="32" height="32" class="icon-style" />
      <text x="40" y="124" class="comparison-item-title font-primary">
        <tspan x="40" dy="0">高度互动参与</tspan>
      </text>
      <text x="40" y="156" class="comparison-item-text font-secondary">
        <tspan x="40" dy="0">小组讨论、项目实践、提问。</tspan>
      </text>

      <use xlink:href="#icon-growth" x="0" y="200" width="32" height="32" class="icon-style" />
      <text x="40" y="224" class="comparison-item-title font-primary">
        <tspan x="40" dy="0">多元化评估方式</tspan>
      </text>
      <text x="40" y="256" class="comparison-item-text font-secondary">
        <tspan x="40" dy="0">注重过程、能力和创新。</tspan>
      </text>
    </g>

    <!-- Central Difference Highlight -->
    <g transform="translate(960, 480)">
      <rect x="-88" y="-80" width="176" height="160" rx="12" ry="12" class="container-background" />
      <use xlink:href="#icon-arrow-right" x="-40" y="-40" width="80" height="80" class="highlight-icon-style" />
    </g>
    <text x="960" y="620" text-anchor="middle" class="accent-highlight-text font-primary">
        <tspan x="960" dy="0">更高效</tspan>
    </text>
    <text x="960" y="660" text-anchor="middle" class="content-title font-secondary text-secondary">
        <tspan x="960" dy="0">More Effective</tspan>
    </text>

  </g>

  <!-- Conclusion Section -->
  <g>
    <rect x="80" y="780" width="1760" height="240" rx="12" ry="12" class="card" />
    <text x="960" y="830" text-anchor="middle" class="conclusion-title font-primary">
      <tspan x="960" dy="0">结论总结：赋能未来学习</tspan>
    </text>
    <text x="960" y="880" text-anchor="middle" class="conclusion-text font-secondary">
      <tspan x="960" dy="0">互动学习模式通过提升参与度、鼓励批判性思维和提供个性化反馈，</tspan>
      <tspan x="960" dy="30">显著优于传统教学。这不仅能提高学习效率，更能培养终身学习者。</tspan>
      <tspan x="960" dy="30">它为学生提供了更丰富的学习体验，帮助他们更好地应对未来挑战。</tspan>
    </text>
  </g>

  <!-- Footer -->
  <text x="1840" y="1040" text-anchor="end" class="caption font-secondary text-light">
    <tspan x="1840" dy="0">Page 7/10</tspan>
  </text>
  <text x="80" y="1040" text-anchor="start" class="caption font-secondary text-light">
    <tspan x="80" dy="0">{date} by {author}</tspan>
  </text>

</svg>