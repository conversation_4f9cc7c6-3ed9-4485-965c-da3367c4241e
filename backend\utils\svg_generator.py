"""
AI演示文稿SVG生成器
基于Gemini-2.5-Flash模型和结构化提示词的两阶段SVG幻灯片生成系统
"""
import asyncio
import json
import logging
import time
import os
import re
import base64
import urllib.parse
import urllib.request
import aiohttp
from math import log
from typing import Dict, List, Any, Optional, Callable, Union, Awaitable
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

from backend.ai.openai_service import OpenAIService
from backend.utils.prompts import PPT_SVG
from backend.utils.image_processor import image_processor

# 配置日志
logger = logging.getLogger(__name__)

# 图片下载和转换设置
MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB
DOWNLOAD_TIMEOUT = 10  # 10秒
#修改缓存目录为根目录下的cache/images
CACHE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "cache", "images")

# 确保缓存目录存在
os.makedirs(CACHE_DIR, exist_ok=True)

class GenerationStage(Enum):
    """生成阶段枚举"""
    INITIAL = "initial"
    DESIGN_SPEC = "design_spec"
    CONFIRMED = "confirmed"
    GENERATING_PAGES = "generating_pages"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class DesignSpecification:
    """设计规范数据结构"""
    content_outline: List[Dict[str, Any]]
    theme_selection: Dict[str, Any]
    color_palette: Dict[str, str]
    layout_principles: Dict[str, Any]
    typography_system: Dict[str, Any]
    canvas_size: Dict[str, int]
    created_at: str
    raw_spec: str
    # 新增：原文内容映射
    content_mapping: Optional[Dict[int, Dict[str, Any]]] = None

@dataclass
class PageContent:
    """页面内容数据结构"""
    page_number: int
    title: str
    layout_type: str
    content: Dict[str, Any]
    svg_code: Optional[str] = None
    generated_at: Optional[str] = None
    # 新增：页面具体内容提取
    extracted_content: Optional[Dict[str, Any]] = None

@dataclass
class GenerationConfig:
    """生成配置"""
    model: str = "gemini-2.5-flash"
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    thinking_budget: Optional[int] = 2048
    canvas_width: int = 1920
    canvas_height: int = 1080
    enable_streaming: bool = False
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 2.0  # 基础重试延迟（秒）
    max_retry_delay: float = 30.0  # 最大重试延迟（秒）
    
class AIPresenterSVGGenerator:
    """AI演示文稿SVG生成器
    
    基于两阶段协作模式的SVG幻灯片生成系统：
    1. 第一阶段：深度分析与设计规范构建
    2. 第二阶段：逐页精细化SVG生成
    """
    
    def __init__(self, config: Optional[GenerationConfig] = None):
        """初始化生成器
        
        Args:
            config: 生成配置，为None时使用默认配置
        """
        self.config = config or GenerationConfig()
        self.ai_service = OpenAIService()
        self.current_stage = GenerationStage.INITIAL
        self.design_spec: Optional[DesignSpecification] = None
        self.pages: List[PageContent] = []
        self.source_content = ""
        self.progress_callback: Optional[Callable] = None
        self.is_cancelled_callback: Optional[Callable] = None
        
        # {{CHENGQI:
        # Action: Added
        # Timestamp: 2025-01-16 09:45:00 +08:00
        # Reason: 初始化extracted_images属性，用于存储从源文档中提取的图像信息
        # Principle_Applied: 单一职责 - 集中管理图像信息; YAGNI - 只在需要时使用
        # Optimization: 提供图像信息的统一访问接口
        # Architectural_Note (AR): 图像信息作为生成器状态的一部分，确保全流程可访问
        # Documentation_Note (DW): 明确图像信息的存储结构和用途
        # }}
        # 图像信息存储
        self.extracted_images: List[Dict[str, str]] = []
        
        # 缓存管理
        self._cache = {}
        
        logger.info(f"AI演示文稿SVG生成器初始化完成，使用模型: {self.config.model}")
    
    def set_progress_callback(self, callback: Union[Callable[[str, float], None], Callable[[str, float, str], None], Callable[[str, float], Callable], Callable[[str, float, str], Callable], Callable[[str, float, str], Awaitable[None]], Callable[[str, float, str, Optional[Dict]], Awaitable[None]]]):
        """设置进度回调函数
        
        Args:
            callback: 回调函数，可以是同步或异步，接收(stage_name: str, progress: float, message: str = "", extra_data: Optional[Dict] = None)参数
        """
        self.progress_callback = callback
    
    def set_cancellation_callback(self, callback: Callable[[], bool]):
        """设置取消检查回调函数
        
        Args:
            callback: 回调函数，返回True表示应该取消操作
        """
        self.is_cancelled_callback = callback
    
    def _notify_progress(self, stage: str, progress: float, message: str = "", extra_data: Optional[Dict] = None):
        """通知进度更新"""
        if self.progress_callback:
            try:
                # 检查是否是异步回调
                import asyncio
                import inspect
                
                if asyncio.iscoroutinefunction(self.progress_callback):
                    # 异步回调，需要在事件循环中执行
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 如果事件循环正在运行，创建一个任务
                            # 传递额外数据给异步回调
                            if extra_data:
                                asyncio.create_task(self.progress_callback(stage, progress, message, extra_data))
                            else:
                                asyncio.create_task(self.progress_callback(stage, progress, message))
                        else:
                            # 如果事件循环没有运行，使用run_until_complete
                            if extra_data:
                                loop.run_until_complete(self.progress_callback(stage, progress, message, extra_data))
                            else:
                                loop.run_until_complete(self.progress_callback(stage, progress, message))
                    except Exception as e:
                        logger.warning(f"异步进度回调执行失败: {e}")
                else:
                    # 同步回调
                    # 检查回调函数的签名
                    sig = inspect.signature(self.progress_callback)
                    param_count = len(sig.parameters)
                    
                    if param_count >= 4 and extra_data:
                        # 支持extra_data参数
                        self.progress_callback(stage, progress, message, extra_data)
                    elif param_count >= 3:
                        # 支持message参数
                        self.progress_callback(stage, progress, message)
                    else:
                        # 只支持stage和progress参数
                        self.progress_callback(stage, progress)
            except Exception as e:
                logger.warning(f"进度回调执行失败: {e}")
    
    def _check_cancellation(self) -> bool:
        """检查是否需要取消操作"""
        if self.is_cancelled_callback:
            try:
                return self.is_cancelled_callback()
            except Exception as e:
                logger.warning(f"取消检查回调执行失败: {e}")
        return False
    
    def _build_style_requirements(self, style_config: Dict[str, str]) -> str:
        """构建风格配置的文本描述
        
        Args:
            style_config: 包含风格配置的字典
            
        Returns:
            风格要求的文本描述
        """
        requirements = []
        
        # SVG风格配置
        svg_style = style_config.get('svg_style', 'modern')
        style_descriptions = {
            'modern': "现代简约风格：采用简洁的线条、充足的留白、无衬线字体，强调功能性和可读性，避免过度装饰",
            'business': "商务专业风格：使用正式的布局、传统的配色、清晰的层次结构，体现专业性和权威性，适合商业演示",
            'creative': "创意活泼风格：运用不规则形状、渐变色彩、创新布局，营造活泼有趣的氛围，鼓励创新思维",
            'academic': "学术严谨风格：采用规整的布局、稳重的配色、清晰的逻辑结构，确保信息准确传达，适合学术场合",
            'future': "科技未来感风格：采用未来感的布局、科技感的配色、科技感的字体，营造未来感和科技感，适合科技和创新主题"
        }
        requirements.append(style_descriptions.get(svg_style, style_descriptions['modern']))
        
        # 配色主题配置 - 支持用户自定义的9种颜色
        svg_color_theme = style_config.get('svg_color_theme', 'blue')
        color_descriptions = {
            'golden': "配色主题为金黄色系：主色调使用金黄色系（#FFD700, #FFA500, #DAA520），营造富贵典雅的氛围，适合高端商务和庆典主题",
            'bright_yellow': "配色主题为亮黄色系：主色调使用亮黄色系（#FFFF00, #FFD700, #FFF700），展现活力和创新精神，适合创意和营销主题",
            'light_green': "配色主题为浅绿色系：主色调使用浅绿色系（#90EE90, #98FB98, #00FA9A），传达清新和自然的理念，适合环保和健康主题",
            'pink': "配色主题为粉红色系：主色调使用粉红色系（#FFC0CB, #FFB6C1, #FF69B4），体现温馨和浪漫，适合时尚和生活主题",
            'cyan': "配色主题为青蓝色系：主色调使用青蓝色系（#00CED1, #40E0D0, #48D1CC），营造清新和科技感，适合科技和创新主题",
            'purple': "配色主题为紫色系：主色调使用紫色系（#8A2BE2, #9370DB, #BA55D3），体现优雅和神秘，适合设计和艺术主题",
            'blue': "配色主题为蓝色系：主色调使用蓝色系（#4169E1, #1E90FF, #0000FF），营造专业可信的氛围，适合商务和科技主题",
            'brown': "配色主题为棕色系：主色调使用棕色系（#A0522D, #8B4513, #D2691E），传达稳重和自然的感觉，适合传统和文化主题",
            'red': "配色主题为红色系：主色调使用红色系（#DC143C, #FF0000, #B22222），展现热情和力量，适合重要和紧急主题"
        }
        requirements.append(color_descriptions.get(svg_color_theme, color_descriptions['blue']))
        
        # 布局要求
        svg_layout = style_config.get('svg_layout', 'standard')
        layout_descriptions = {
            'standard': "使用标准16:9比例布局，适合大多数演示场景",
            'wide': "使用宽屏21:9比例布局，适合超宽屏显示，提供更多水平空间",
            'square': "使用方形1:1比例布局，适合社交媒体分享和移动端显示"
        }
        requirements.append(layout_descriptions.get(svg_layout, layout_descriptions['standard']))
        
        return "\n".join([f"- {req}" for req in requirements])
    
    def _get_canvas_config(self, layout: str) -> Dict[str, int]:
        """根据布局类型获取画布配置
        
        Args:
            layout: 布局类型 (standard/wide/square)
            
        Returns:
            包含width和height的字典
        """
        layout_configs = {
            'standard': {'width': 1920, 'height': 1080},  # 16:9
            'wide': {'width': 2560, 'height': 1080},       # 21:9
            'square': {'width': 1080, 'height': 1080}      # 1:1
        }
        
        return layout_configs.get(layout, layout_configs['standard'])
    
    async def _call_ai_model(self, messages: List[Dict], stream_callback: Optional[Callable] = None) -> Dict:
        """调用AI模型的统一接口（带重试功能）
        
        Args:
            messages: 消息列表
            stream_callback: 流式响应回调
            
        Returns:
            AI响应结果
        """
        last_error = None
        retry_errors = []
        # 设置模型并调用
        self.ai_service.model = self.config.model
        for attempt in range(self.config.max_retries + 1):  # +1 因为第一次不算重试
            try:
                # 检查是否被取消
                if self._check_cancellation():
                    return {"error": "操作已取消", "cancelled": True}
                
                # 如果不是第一次尝试，记录重试信息
                if attempt > 0:
                    logger.info(f"AI模型调用重试 {attempt}/{self.config.max_retries}...")
                    #self._notify_progress("重试中", 0.0, f"第{attempt}次重试...")
                    
                    # 计算重试延迟（简单的固定延迟，用户要求2秒）
                    #delay = self.config.retry_delay
                    #logger.info(f"等待 {delay} 秒后重试...")
                    #await asyncio.sleep(delay)
                    if attempt==2:
                        logger.info("尝试使用gemini-2.5-flash-preview-04-17模型")
                        self.ai_service.model = "gemini-2.5-flash-preview-04-17"
                    # 再次检查是否被取消
                    if self._check_cancellation():
                        return {"error": "操作已取消", "cancelled": True}
                
                
                logger.info(f"尝试调用AI模型 (第{attempt + 1}次)...")
                
                result = await self.ai_service.chat_completion(
                    messages=messages,
                    stream_callback=stream_callback,
                    is_cancelled=self.is_cancelled_callback
                )
                
                # 检查结果是否成功
                if "error" not in result:
                    if attempt > 0:
                        logger.info(f"AI模型调用在第{attempt + 1}次尝试后成功")
                        #self._notify_progress("重试成功", 1.0, f"第{attempt + 1}次尝试成功")
                    return result
                else:
                    # 记录错误但不立即返回，继续重试
                    error_msg = result.get('error', '未知错误')
                    retry_errors.append(f"第{attempt + 1}次尝试: {error_msg}")
                    logger.warning(f"AI模型调用失败 (第{attempt + 1}次): {error_msg}")
                    last_error = result

                    # 如果达到最大重试次数，跳出循环
                    if attempt >= self.config.max_retries:
                        break
                    
            except Exception as e:
                error_msg = f"AI模型调用异常: {str(e)}"
                retry_errors.append(f"第{attempt + 1}次尝试: {error_msg}")
                logger.error(f"{error_msg} (第{attempt + 1}次)", exc_info=True)
                last_error = {"error": error_msg}
                
                # 如果达到最大重试次数，跳出循环
                if attempt >= self.config.max_retries:
                    break
        
        # 所有重试都失败了
        self.current_stage = GenerationStage.ERROR
        
        # 构建详细的错误信息
        final_error = {
            "error": f"AI模型调用在{self.config.max_retries + 1}次尝试后全部失败",
            "last_error": last_error.get('error') if last_error else "未知错误",
            "retry_attempts": self.config.max_retries + 1,
            "all_errors": retry_errors
        }
        
        logger.error(f"AI模型调用最终失败: {final_error}")
        return final_error
    
    async def generate_design_specification(self, content: str, custom_requirements: str = "", style_config: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """第一阶段：生成设计规范与内容大纲

        Args:
            content: 源文档内容
            custom_requirements: 自定义需求描述
            style_config: 风格配置，包含svg_style, svg_color_theme, svg_layout等

        Returns:
            包含设计规范的字典或错误信息
        """
        logger.info("开始生成设计规范与内容大纲...")
        self.current_stage = GenerationStage.DESIGN_SPEC
        self.source_content = content
        # 保存原始内容供后续页面内容丰富使用
        self.original_content = content

        # 保存用户的颜色主题选择
        if style_config and 'svg_color_theme' in style_config:
            self.user_color_theme = style_config['svg_color_theme']
            logger.info(f"保存用户颜色主题选择: {self.user_color_theme}")

        self._notify_progress("设计规范生成", 0.0, "开始分析源文档内容...")
        
        # {{CHENGQI:
        # Action: Added
        # Timestamp: 2025-01-16 09:45:00 +08:00
        # Reason: 在设计规范生成阶段提取图像信息，确保图片能被正确识别和传递给AI
        # Principle_Applied: 单一职责 - 集中处理图像提取; DRY - 复用现有的图像提取方法
        # Optimization: 早期提取图像信息，为后续SVG生成做准备
        # Architectural_Note (AR): 将图像信息集成到设计规范中，保证设计一致性
        # Documentation_Note (DW): 记录图像提取过程，确保可追溯性
        # }}
        # 提取文档中的图像信息
        logger.info("提取源文档中的图像信息...")
        extracted_images = self._extract_images_from_text(content)
        if extracted_images:
            logger.info(f"发现 {len(extracted_images)} 张图片: {[img['url'] for img in extracted_images]}")

            # 立即验证所有图片，避免后续重复验证
            logger.info("🔍 开始验证所有图片...")
            validated_images = await image_processor.validate_and_filter_images(extracted_images, use_cache=True)
            logger.info(f"✅ 图片验证完成: {len(validated_images)}/{len(extracted_images)} 张图片有效")

            self.extracted_images = validated_images
        else:
            logger.info("源文档中未发现图片")
            self.extracted_images = []
        
        if self._check_cancellation():
            return {"error": "操作已取消", "cancelled": True}
        
        # 构建风格需求
        style_requirements = self._build_style_requirements(style_config or {})
        
        # 合并用户自定义需求和风格需求
        final_requirements = []
        if style_requirements:
            final_requirements.append(f"**风格与主题要求：**\n{style_requirements}")
        if custom_requirements:
            final_requirements.append(f"**用户自定义需求：**\n{custom_requirements}")
        
        combined_requirements = "\n\n".join(final_requirements) if final_requirements else "无特殊要求，请根据内容特点进行最佳设计。"
        
        # 根据布局配置设置画布尺寸
        canvas_config = self._get_canvas_config(style_config.get('svg_layout', 'standard') if style_config else 'standard')
        self.config.canvas_width = canvas_config['width']
        self.config.canvas_height = canvas_config['height']
        
        # 构建第一阶段提示词
        stage_one_prompt = f"""
{PPT_SVG}

现在开始第一阶段工作：深度内容分析与设计规范构建

**源文档内容：**
{content}

**风格与需求配置：**
{combined_requirements}

**画布配置：**
- 宽度: {self.config.canvas_width}px
- 高度: {self.config.canvas_height}px

**重要内容提取要求：**
请在生成页面序列时，为每个页面详细提取原文中的具体内容，确保：
1. 每个页面都有充实的原文内容支撑，避免空洞的描述
2. 提取原文中的关键数据、案例、具体细节
3. 为每个页面规划丰富的视觉元素和内容布局
4. 除封面和目录页外，每页都应包含大量实质性内容

**页面内容要求：**
- 封面页：标题、副标题、作者信息、关键视觉元素
- 目录页：完整的章节结构和页面导航
- 内容页：必须包含原文中的具体内容，如数据、图表描述、案例分析、详细说明等
- 每个内容页都要充分利用画布空间，避免大面积空白

**严格输出格式要求（必须遵守）：**

**1. 页面序列必须使用以下严格格式：**
```
**页面序列：**
`页面1`: 页面标题 - 页面简要描述
`页面2`: 页面标题 - 页面简要描述
`页面3`: 页面标题 - 页面简要描述
...
`页面N`: 页面标题 - 页面简要描述
```

**2. 每个页面的详细内容必须紧随页面定义后，使用以下格式：**
```
`页面X`内容要点：
• 具体要点1（来源于原文）
• 具体要点2（来源于原文）
• 具体要点3（来源于原文）
• 具体要点4（来源于原文）
```

**3. 页面编号要求：**
- 必须从1开始连续编号，不能跳号
- 页面编号格式：`页面1`、`页面2`、`页面3`...
- 不要使用其他格式如"第1页"、"页面一"等

**4. 内容分布要求：**
- 每个内容页面都必须有独立的内容要点列表
- 每个页面至少包含3-5个实质性要点
- 要点必须来源于原文，不得虚构

**示例格式：**
```
**页面序列：**
`页面1`: 封面页 - 华为汽车业务研究报告
`页面2`: 目录页 - 报告结构一览
`页面3`: 华为汽车业务概览 - 技术全景与战略定位

`页面1`内容要点：
• 报告标题：华为汽车业务深度分析
• 研究时间：2024年度数据分析
• 核心主题：智能汽车技术与市场策略
• 视觉元素：华为Logo、汽车科技图标

`页面2`内容要点：
• 第一章：华为汽车业务概览
• 第二章：技术解决方案分析
• 第三章：市场表现与竞争格局
• 第四章：未来发展策略

`页面3`内容要点：
• 华为智能汽车解决方案四大领域
• 智能驾驶技术栈完整性
• 智能座舱生态建设成果
• 市场合作模式创新突破
```

**颜色代码要求：**
- 必须使用完整的6位十六进制代码，如：#FF0000
- 避免使用颜色名称或简写

**数值要求：**
- 所有尺寸必须包含px单位，如：100px

请严格按照上述格式要求，完成第一阶段的深度分析与设计蓝图构建工作。确保页面序列清晰、编号连续、内容要点丰富。每个页面都要有对应的内容要点列表。
当前时间是：{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
"""
        
        messages = [
            {"role": "system", "content": "你是一位顶尖的AI演示文稿设计师，具备分析师的洞察力、策略师的规划能力和品牌设计师的审美眼光。"},
            {"role": "user", "content": stage_one_prompt}
        ]
        
        self._notify_progress("设计规范生成", 0.3, "正在调用AI模型分析内容...")
        
        # 调用AI模型
        result = await self._call_ai_model(messages)
        #logger.info(f"result: {result}")
        
        if "error" in result:
            # 如果是重试失败，提供更详细的错误信息
            if "retry_attempts" in result:
                logger.error(f"设计规范生成失败，经过{result['retry_attempts']}次尝试后仍然失败")
                return {
                    "error": f"设计规范生成失败：{result['last_error']}",
                    "retry_info": f"经过{result['retry_attempts']}次尝试",
                    "all_errors": result.get('all_errors', [])
                }
            return result
        
        if self._check_cancellation():
            return {"error": "操作已取消", "cancelled": True}
        
        self._notify_progress("设计规范生成", 0.8, "正在解析设计规范...")
        #logger.info(f"result['content']: {result['content']}")
        # 解析设计规范
        try:
            design_spec = self._parse_design_specification(result["content"])
            
            # 新增：提取每个页面的具体内容
            content_mapping = self._extract_content_for_pages(result["content"], design_spec.content_outline)
            design_spec.content_mapping = content_mapping
            
            self.design_spec = design_spec
            
            self._notify_progress("设计规范生成", 1.0, "设计规范生成完成")
            
            logger.info(f"设计规范生成完成，包含 {len(design_spec.content_outline)} 个页面，已提取内容映射")
            
            return {
                "success": True,
                "design_spec": asdict(design_spec),
                "raw_response": result["content"],
                "stage": "design_spec_generated"
            }
            
        except Exception as e:
            logger.error(f"解析设计规范失败: {str(e)}", exc_info=True)
            return {"error": f"解析设计规范失败: {str(e)}"}
    
    def _extract_content_for_pages(self, ai_response: str, content_outline: List[Dict[str, Any]]) -> Dict[int, Dict[str, Any]]:
        """从AI响应中提取每个页面的具体内容

        Args:
            ai_response: AI响应文本
            content_outline: 内容大纲

        Returns:
            页码到内容的映射字典
        """
        content_mapping = {}

        try:
            # 首先保存原始内容作为全局上下文
            original_content = self.original_content if hasattr(self, 'original_content') else ""

            # 尝试提取页面内容
            current_page = None
            current_content = []

            for line in ai_response.split('\n'):
                # 检查是否是页面标记
                page_match = re.match(r'^\s*`?页面\s*(\d+)`?\s*[:：]', line)
                if page_match:
                    # 如果找到新页面，保存之前的内容
                    if current_page is not None:
                        page_text = '\n'.join(current_content)
                        # 确保页面内容足够丰富
                        enriched_content = self._enrich_page_content(page_text, current_page, content_outline, original_content)

                        # 提取页面特定的图片链接
                        page_specific_images = self._extract_images_from_text(enriched_content)
                        # 合并页面特定图片和全局图片
                        all_images = page_specific_images + self.extracted_images
                        # 去重（基于URL）
                        unique_images = []
                        seen_urls = set()
                        for img in all_images:
                            if img['url'] not in seen_urls:
                                unique_images.append(img)
                                seen_urls.add(img['url'])

                        content_mapping[current_page] = {
                            'text': enriched_content,
                            'original_text': page_text,  # 保留原始提取的文本
                            'images': unique_images
                        }

                    # 开始新页面
                    current_page = int(page_match.group(1))
                    current_content = []
                    continue

                # 将行添加到当前内容
                if current_page is not None:
                    current_content.append(line)

            # 保存最后一页内容
            if current_page is not None:
                page_text = '\n'.join(current_content)
                # 确保页面内容足够丰富
                enriched_content = self._enrich_page_content(page_text, current_page, content_outline, original_content)

                # 提取页面特定的图片链接
                page_specific_images = self._extract_images_from_text(enriched_content)
                # 合并页面特定图片和全局图片
                all_images = page_specific_images + self.extracted_images
                # 去重（基于URL）
                unique_images = []
                seen_urls = set()
                for img in all_images:
                    if img['url'] not in seen_urls:
                        unique_images.append(img)
                        seen_urls.add(img['url'])

                content_mapping[current_page] = {
                    'text': enriched_content,
                    'original_text': page_text,  # 保留原始提取的文本
                    'images': unique_images
                }
            
            # 如果没有提取到任何内容，使用大纲作为备用
            if not content_mapping:
                logger.info("未提取到页面内容，使用大纲作为备用")
                for page in content_outline:
                    page_num = page.get('page_number')
                    if page_num:
                        # 构建基础页面文本
                        page_text = page.get('title', '') + '\n' + page.get('content', '')
                        # 丰富页面内容
                        enriched_content = self._enrich_page_content(page_text, page_num, content_outline, original_content)

                        # 提取页面特定的图片链接
                        page_specific_images = self._extract_images_from_text(enriched_content)
                        # 合并页面特定图片和全局图片
                        all_images = page_specific_images + self.extracted_images
                        # 去重（基于URL）
                        unique_images = []
                        seen_urls = set()
                        for img in all_images:
                            if img['url'] not in seen_urls:
                                unique_images.append(img)
                                seen_urls.add(img['url'])

                        content_mapping[page_num] = {
                            'text': enriched_content,
                            'original_text': page_text,  # 保留原始文本
                            'images': unique_images
                        }
            
            return content_mapping

        except Exception as e:
            logger.warning(f"提取页面内容失败: {e}")
            # 返回空映射
            return {}

    def _enrich_page_content(self, page_text: str, page_number: int, content_outline: List[Dict[str, Any]], original_content: str) -> str:
        """丰富页面内容，确保有足够的上下文信息

        Args:
            page_text: 原始页面文本
            page_number: 页面编号
            content_outline: 内容大纲
            original_content: 原始文档内容

        Returns:
            丰富后的页面内容
        """
        try:
            # 如果页面内容太少（少于50个字符），需要丰富内容
            if len(page_text.strip()) < 50:
                logger.info(f"页面{page_number}内容太少({len(page_text)}字符)，开始丰富内容...")

                # 从大纲中获取页面信息
                page_info = None
                for page in content_outline:
                    if page.get('page_number') == page_number:
                        page_info = page
                        break

                if page_info:
                    # 构建丰富的页面内容
                    enriched_parts = []

                    # 1. 页面标题和描述
                    if page_info.get('title'):
                        enriched_parts.append(f"页面标题: {page_info['title']}")
                    if page_info.get('description'):
                        enriched_parts.append(f"页面描述: {page_info['description']}")

                    # 2. 原始提取的内容
                    if page_text.strip():
                        enriched_parts.append(f"页面内容: {page_text.strip()}")

                    # 3. 从原始文档中提取相关内容
                    if original_content and page_info.get('title'):
                        relevant_content = self._extract_relevant_content(original_content, page_info['title'])
                        if relevant_content:
                            enriched_parts.append(f"相关原文内容: {relevant_content}")

                    # 4. 页面类型和布局信息
                    if page_info.get('layout_type'):
                        enriched_parts.append(f"页面类型: {page_info['layout_type']}")

                    # 5. 添加上下文信息
                    context_info = self._get_page_context(page_number, content_outline)
                    if context_info:
                        enriched_parts.append(f"页面上下文: {context_info}")

                    enriched_content = '\n\n'.join(enriched_parts)
                    logger.info(f"页面{page_number}内容已丰富: {len(page_text)} -> {len(enriched_content)}字符")
                    return enriched_content
                else:
                    logger.warning(f"未找到页面{page_number}的大纲信息")

            # 如果内容足够，直接返回原内容
            return page_text

        except Exception as e:
            logger.error(f"丰富页面{page_number}内容失败: {e}")
            return page_text  # 返回原始内容

    def _extract_relevant_content(self, original_content: str, page_title: str) -> str:
        """从原始文档中提取与页面标题相关的内容

        Args:
            original_content: 原始文档内容
            page_title: 页面标题

        Returns:
            相关内容片段
        """
        try:
            # 将标题分词，查找相关内容
            title_keywords = page_title.lower().split()
            content_lines = original_content.split('\n')

            relevant_lines = []
            for line in content_lines:
                line_lower = line.lower()
                # 如果行中包含标题关键词，认为是相关内容
                if any(keyword in line_lower for keyword in title_keywords if len(keyword) > 1):
                    relevant_lines.append(line.strip())

            # 限制相关内容长度，避免过长
            relevant_content = '\n'.join(relevant_lines[:10])  # 最多10行
            if len(relevant_content) > 500:  # 最多500字符
                relevant_content = relevant_content[:500] + "..."

            return relevant_content

        except Exception as e:
            logger.error(f"提取相关内容失败: {e}")
            return ""

    def _get_page_context(self, page_number: int, content_outline: List[Dict[str, Any]]) -> str:
        """获取页面的上下文信息

        Args:
            page_number: 当前页面编号
            content_outline: 内容大纲

        Returns:
            上下文信息
        """
        try:
            context_parts = []

            # 获取前一页和后一页的标题
            prev_page = None
            next_page = None
            current_page = None

            for page in content_outline:
                if page.get('page_number') == page_number - 1:
                    prev_page = page
                elif page.get('page_number') == page_number:
                    current_page = page
                elif page.get('page_number') == page_number + 1:
                    next_page = page

            if prev_page:
                context_parts.append(f"前一页: {prev_page.get('title', '')}")
            if next_page:
                context_parts.append(f"后一页: {next_page.get('title', '')}")

            # 添加总页数信息
            total_pages = len(content_outline)
            context_parts.append(f"当前是第{page_number}页，共{total_pages}页")

            return '; '.join(context_parts)

        except Exception as e:
            logger.error(f"获取页面上下文失败: {e}")
            return ""
    
    def _extract_images_from_text(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取图片链接
        
        支持Markdown和HTML格式的图片
        
        Args:
            text: 文本内容
            
        Returns:
            图片信息列表，每个图片包含url和alt属性
        """
        images = []
            
        # 提取Markdown格式图片: ![alt](url)
        md_pattern = r'!\[(.*?)\]\((.*?)\)'
        for match in re.finditer(md_pattern, text):
            alt_text = match.group(1)
            image_url = match.group(2)
            images.append({
                'url': image_url,
                'alt': alt_text
            })
        
        # 提取HTML格式图片: <img src="url" alt="alt" />
        html_pattern = r'<img[^>]*src="([^"]+)"[^>]*alt="([^"]*)"[^>]*/?>|<img[^>]*alt="([^"]*)"[^>]*src="([^"]+)"[^>]*/?>|<img[^>]*src="([^"]+)"[^>]*/?>|<img[^>]*src=\'([^\']+)\'[^>]*/?>|<img[^>]*src=([^\s>]+)[^>]*/?>|<img[^>]*src="([^"]+)"[^>]*>'
        for match in re.finditer(html_pattern, text):
            # 处理不同的匹配组合
            if match.group(1) and match.group(2):  # src="url" alt="alt"
                image_url = match.group(1)
                alt_text = match.group(2)
            elif match.group(3) and match.group(4):  # alt="alt" src="url"
                image_url = match.group(4)
                alt_text = match.group(3)
            elif match.group(5):  # src="url" 没有alt
                image_url = match.group(5)
                alt_text = ""
            elif match.group(6):  # src='url' 单引号
                image_url = match.group(6)
                alt_text = ""
            elif match.group(7):  # src=url 没有引号
                image_url = match.group(7)
                alt_text = ""
            elif match.group(8):  # src="url" 没有结束标签
                image_url = match.group(8)
                alt_text = ""
            else:
                continue
                
            images.append({
                'url': image_url,
                'alt': alt_text
            })
        
        return images

    def _get_color_palette_by_theme(self, color_theme: str, mode: str = "auto") -> Dict[str, str]:
        """根据颜色主题生成对应的调色板

        Args:
            color_theme: 颜色主题名称
            mode: 主题模式 (dark/light/auto)

        Returns:
            颜色调色板字典
        """
        # 定义9种颜色主题的调色板
        color_palettes = {
            'golden': {
                'primary': '#FFD700',
                'accent': '#FFA500',
                'secondary': '#DAA520',
                'highlight': '#FFFF99'
            },
            'bright_yellow': {
                'primary': '#FFFF00',
                'accent': '#FFD700',
                'secondary': '#FFF700',
                'highlight': '#FFFFE0'
            },
            'light_green': {
                'primary': '#90EE90',
                'accent': '#98FB98',
                'secondary': '#00FA9A',
                'highlight': '#F0FFF0'
            },
            'pink': {
                'primary': '#FFC0CB',
                'accent': '#FFB6C1',
                'secondary': '#FF69B4',
                'highlight': '#FFF0F5'
            },
            'cyan': {
                'primary': '#00CED1',
                'accent': '#40E0D0',
                'secondary': '#48D1CC',
                'highlight': '#E0FFFF'
            },
            'purple': {
                'primary': '#8A2BE2',
                'accent': '#9370DB',
                'secondary': '#BA55D3',
                'highlight': '#F8F8FF'
            },
            'blue': {
                'primary': '#4169E1',
                'accent': '#1E90FF',
                'secondary': '#0000FF',
                'highlight': '#F0F8FF'
            },
            'brown': {
                'primary': '#A0522D',
                'accent': '#8B4513',
                'secondary': '#D2691E',
                'highlight': '#FDF5E6'
            },
            'red': {
                'primary': '#DC143C',
                'accent': '#FF0000',
                'secondary': '#B22222',
                'highlight': '#FFF5F5'
            }
        }

        # 获取基础颜色
        base_colors = color_palettes.get(color_theme, color_palettes['blue'])

        # 根据主题模式调整背景和文字颜色
        if mode == "dark":
            background = "#1A1A2E"
            text = "#F8F8F8"
            card_background = "#2E2E4A"
        elif mode == "light":
            background = "#FFFFFF"
            text = "#333333"
            card_background = "#F8F9FA"
        else:  # auto
            background = "#FFFFFF"
            text = "#333333"
            card_background = "#F8F9FA"

        # 组合完整的调色板
        return {
            **base_colors,
            'background': background,
            'text': text,
            'card_background': card_background
        }

    def _parse_design_specification(self, ai_response: str) -> DesignSpecification:
        """解析AI生成的设计规范
        
        Args:
            ai_response: AI响应内容
            
        Returns:
            结构化的设计规范对象
        """
        content_outline = []
        theme_selection = {}
        color_palette = {}
        layout_principles = {}
        typography_system = {}
        
        logger.info("开始解析设计规范...")
        
        # 查找页面序列部分
        page_sequence_pattern = r'\*\*页面序列[：:]\*\*(.*?)(?=\*\*[^页面]|$)'
        sequence_match = re.search(page_sequence_pattern, ai_response, re.DOTALL)
        
        page_matches = []
        if sequence_match:
            sequence_text = sequence_match.group(1)
            logger.info("找到页面序列部分，开始提取页面...")
            
            # 从页面序列中提取页面信息
            page_patterns = [
                r'`页面(\d+)`:\s*([^-\n]+?)\s*-\s*([^\n]+)',  # `页面1`: 标题 - 描述
                r'`页面(\d+)`[：:]\s*([^-\n]+?)\s*-\s*([^\n]+)',  # `页面1`：标题 - 描述
            ]
            
            for pattern in page_patterns:
                page_matches = re.findall(pattern, sequence_text, re.IGNORECASE)
                if page_matches:
                    logger.info(f"使用模式 {pattern} 在页面序列中找到 {len(page_matches)} 个页面")
                    break
        
        if not page_matches:
            logger.warning("在页面序列中未找到页面，尝试在整个响应中搜索...")
            # 在整个响应中搜索页面信息
            page_patterns = [
                r'`页面(\d+)`:\s*([^-\n]+?)\s*-\s*([^\n`]+)',  # `页面1`: 标题 - 描述
                r'`页面(\d+)`[：:]\s*([^-\n]+?)\s*-\s*([^\n`]+)',  # `页面1`：标题 - 描述
                r'页面(\d+)[：:]\s*([^-\n]+?)\s*-\s*([^\n]+)',   # 页面1：标题 - 描述
            ]
            
            for pattern in page_patterns:
                page_matches = re.findall(pattern, ai_response, re.IGNORECASE)
                if page_matches:
                    logger.info(f"使用模式 {pattern} 在整个响应中找到 {len(page_matches)} 个页面")
                    break
        
        if not page_matches:
            logger.error("无法提取页面信息，AI响应格式可能不符合要求")
            # 尝试更宽松的匹配
            loose_patterns = [
                r'页面\s*(\d+)[：:]*\s*([^-\n]+?)[-–—]\s*([^\n]+)',
                r'(\d+)\.\s*([^-\n]+?)[-–—]\s*([^\n]+)',
            ]
            
            for pattern in loose_patterns:
                page_matches = re.findall(pattern, ai_response, re.IGNORECASE)
                if page_matches:
                    logger.info(f"使用宽松模式 {pattern} 找到 {len(page_matches)} 个页面")
                    break
        
        # 解析页面信息
        for page_num, title, description in page_matches:
            # 清理标题和描述
            clean_title = re.sub(r'[`\'""\*]+', '', title.strip())
            clean_description = re.sub(r'[`\'""\*\(\)]+.*$', '', description.strip())
            
            content_outline.append({
                "page_number": int(page_num),
                "title": clean_title,
                "description": clean_description,
                "layout_type": "auto"
            })
            logger.info(f"解析页面 {page_num}: {clean_title} - {clean_description}")
        
        # 验证页面编号的连续性
        page_numbers = [page["page_number"] for page in content_outline]
        if page_numbers:
            expected_numbers = list(range(1, max(page_numbers) + 1))
            missing_numbers = set(expected_numbers) - set(page_numbers)
            if missing_numbers:
                logger.warning(f"检测到缺失的页面编号: {sorted(missing_numbers)}")
        
        logger.info(f"总共解析出 {len(content_outline)} 个页面")
        
        # 从布局建议中提取更多信息
        if content_outline:
            layout_suggestions = re.findall(r'`页面(\d+)`.*?布局建议.*?`([^`]+)`', ai_response, re.DOTALL)
            for page_num, layout_info in layout_suggestions:
                page_idx = int(page_num) - 1
                if 0 <= page_idx < len(content_outline):
                    if "行" in layout_info and "列" in layout_info:
                        content_outline[page_idx]["layout_type"] = layout_info
                        logger.info(f"页面 {page_num} 设置布局类型: {layout_info}")
        
        # 提取主题选择
        if "深色主题" in ai_response or "Dark Mode" in ai_response:
            theme_selection["mode"] = "dark"
            logger.info("检测到深色主题")
        elif "亮色主题" in ai_response or "Light Mode" in ai_response:
            theme_selection["mode"] = "light"
            logger.info("检测到亮色主题")
        else:
            theme_selection["mode"] = "auto"
            logger.info("使用自动主题")
        
        # 提取色彩方案 - 更精确的提取
        color_palette = {}
        
        # 提取主导色
        primary_pattern = r'主导色[^#]*#([0-9A-Fa-f]{6})'
        primary_match = re.search(primary_pattern, ai_response)
        if primary_match:
            color_palette["primary"] = f"#{primary_match.group(1)}"
            logger.info(f"提取主导色: {color_palette['primary']}")
        
        # 提取所有颜色代码
        all_colors = re.findall(r'#([0-9A-Fa-f]{6})', ai_response)
        logger.info(f"找到颜色代码: {all_colors}")
        
        # 设置完整的色彩方案 - 优先使用用户配置的颜色主题
        user_color_theme = getattr(self, 'user_color_theme', None)
        if user_color_theme:
            color_palette = self._get_color_palette_by_theme(user_color_theme, theme_selection.get("mode", "auto"))
            logger.info(f"使用用户配置的颜色主题: {user_color_theme}")
        elif all_colors:
            color_palette.update({
                "primary": f"#{all_colors[0]}" if len(all_colors) > 0 else "#4A86E8",
                "accent": f"#{all_colors[1]}" if len(all_colors) > 1 else "#FF6B6B",
                "secondary": f"#{all_colors[2]}" if len(all_colors) > 2 else "#8A2BE2",
                "background": "#1A1A2E" if theme_selection.get("mode") == "dark" else "#FFFFFF",
                "text": "#F8F8F8" if theme_selection.get("mode") == "dark" else "#333333",
                "card_background": "#2E2E4A" if theme_selection.get("mode") == "dark" else "#F8F9FA"
            })
        else:
            # 默认色彩方案
            color_palette = {
                "primary": "#4A86E8",
                "accent": "#FF6B6B",
                "background": "#1A1A2E" if theme_selection.get("mode") == "dark" else "#FFFFFF",
                "text": "#F8F8F8" if theme_selection.get("mode") == "dark" else "#333333",
                "card_background": "#2E2E4A" if theme_selection.get("mode") == "dark" else "#F8F9FA"
            }
        
        # 提取布局原则 - 增加默认间距避免重叠
        layout_principles = {
            "canvas_width": self.config.canvas_width,
            "canvas_height": self.config.canvas_height,
            "margin_horizontal": 120,  # 增加边距
            "margin_vertical": 100,    # 增加边距
            "module_spacing": 80       # 增加模块间距
        }
        
        # 尝试从文本中提取具体数值，但确保最小间距
        margin_match = re.search(r'水平\s*`?(\d+)px`?', ai_response)
        if margin_match:
            layout_principles["margin_horizontal"] = max(100, int(margin_match.group(1)))  # 最小边距100px
        
        vertical_margin_match = re.search(r'垂直\s*`?(\d+)px`?', ai_response)
        if vertical_margin_match:
            layout_principles["margin_vertical"] = max(80, int(vertical_margin_match.group(1)))  # 最小边距80px
        
        spacing_match = re.search(r'间距[：:]\s*`?(\d+)px`?', ai_response)
        if spacing_match:
            layout_principles["module_spacing"] = max(80, int(spacing_match.group(1)))  # 最小间距80px
        
        # 提取排版系统
        typography_system = {
            "primary_font": "system-ui, sans-serif",
            "secondary_font": "Roboto Mono",
            "title_size": 64,
            "subtitle_size": 48,
            "body_size": 24,
            "small_size": 18
        }
        
        # 尝试从文本中提取字体大小
        font_sizes = re.findall(r'(\d+)px', ai_response)
        if font_sizes:
            font_sizes = [int(size) for size in font_sizes if 10 <= int(size) <= 100]
            if font_sizes:
                font_sizes.sort(reverse=True)
                typography_system.update({
                    "title_size": font_sizes[0] if len(font_sizes) > 0 else 64,
                    "subtitle_size": font_sizes[1] if len(font_sizes) > 1 else 48,
                    "body_size": font_sizes[2] if len(font_sizes) > 2 else 24,
                    "small_size": font_sizes[3] if len(font_sizes) > 3 else 18
                })
        
        logger.info(f"解析完成: {len(content_outline)} 个页面, 主题: {theme_selection.get('mode')}, 主色: {color_palette.get('primary')}")
        
        return DesignSpecification(
            content_outline=content_outline,
            theme_selection=theme_selection,
            color_palette=color_palette,
            layout_principles=layout_principles,
            typography_system=typography_system,
            canvas_size={"width": self.config.canvas_width, "height": self.config.canvas_height},
            created_at=datetime.now().isoformat(),
            raw_spec=ai_response
        )
    
    async def confirm_design_specification(self, user_feedback: str = "", modifications: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """确认或修改设计规范
        
        Args:
            user_feedback: 用户反馈意见
            modifications: 具体的修改项
            
        Returns:
            确认结果
        """
        if not self.design_spec:
            return {"error": "尚未生成设计规范，请先调用 generate_design_specification"}
        
        if not user_feedback and not modifications:
            # 直接确认现有设计规范
            self.current_stage = GenerationStage.CONFIRMED
            logger.info("用户确认设计规范，准备进入第二阶段")
            return {"success": True, "message": "设计规范已确认，可以开始生成SVG页面"}
        
        # 有修改需求，重新生成设计规范
        logger.info("用户要求修改设计规范，正在重新生成...")
        
        modification_prompt = f"""
基于之前生成的设计规范，用户提出了以下反馈和修改要求：

**用户反馈：**
{user_feedback}

**具体修改项：**
{json.dumps(modifications, ensure_ascii=False, indent=2) if modifications else "无"}

**当前设计规范：**
{self.design_spec.raw_spec}

请根据用户反馈调整设计规范，输出修改后的完整《演示文稿设计规范与内容大纲》。
"""
        
        messages = [
            {"role": "system", "content": "你是一位顶尖的AI演示文稿设计师，需要根据用户反馈调整设计规范。"},
            {"role": "user", "content": modification_prompt}
        ]
        
        result = await self._call_ai_model(messages)
        
        if "error" in result:
            # 如果是重试失败，提供更详细的错误信息
            if "retry_attempts" in result:
                logger.error(f"设计规范修改失败，经过{result['retry_attempts']}次尝试后仍然失败")
                return {
                    "error": f"设计规范修改失败：{result['last_error']}",
                    "retry_info": f"经过{result['retry_attempts']}次尝试",
                    "all_errors": result.get('all_errors', [])
                }
            return result
        
        # 更新设计规范
        try:
            updated_spec = self._parse_design_specification(result["content"])
            self.design_spec = updated_spec
            self.current_stage = GenerationStage.CONFIRMED
            
            logger.info("设计规范修改完成")
            
            return {
                "success": True,
                "message": "设计规范已根据反馈修改完成",
                "updated_spec": asdict(updated_spec),
                "raw_response": result["content"]
            }
            
        except Exception as e:
            logger.error(f"解析修改后的设计规范失败: {str(e)}")
            return {"error": f"解析修改后的设计规范失败: {str(e)}"}
    
    async def generate_svg_page(self, page_number: int, custom_adjustments: str = "") -> Dict[str, Any]:
        """生成指定页面的SVG
        
        Args:
            page_number: 页面编号（从1开始）
            custom_adjustments: 自定义调整要求
            
        Returns:
            包含SVG代码的字典或错误信息
        """
        if not self.design_spec:
            return {"error": "尚未生成设计规范，请先调用 generate_design_specification"}
        
        if self.current_stage != GenerationStage.CONFIRMED and self.current_stage != GenerationStage.GENERATING_PAGES:
            return {"error": "设计规范尚未确认，请先调用 confirm_design_specification"}
        
        # 查找对应页面信息
        page_info = None
        for page in self.design_spec.content_outline:
            if page["page_number"] == page_number:
                page_info = page
                break
        
        if not page_info:
            return {"error": f"未找到页面 {page_number} 的信息"}
        
        logger.info(f"开始生成第 {page_number} 页 SVG: {page_info['title']}")
        
        self.current_stage = GenerationStage.GENERATING_PAGES
        self._notify_progress(f"生成第{page_number}页", 0.0, f"开始生成: {page_info['title']}")
        
        if self._check_cancellation():
            return {"error": "操作已取消", "cancelled": True}
        
                # 获取页面具体内容
        page_content_info = {}
        if self.design_spec.content_mapping and page_number in self.design_spec.content_mapping:
            page_content_info = self.design_spec.content_mapping[page_number]
        
        # 分析页面内容类型和特征
        content_analysis = self._analyze_page_content_type(page_info, page_content_info)
        
        # 构建内容感知的视觉效果指导
        content_aware_prompt = self._build_content_aware_prompt_section(content_analysis, page_number)

        # 构建详细的内容提示
        content_details = ""
        if page_content_info:
            content_details = f"""
**本页面具体内容要求（必须使用以下原文内容）：**

**页面标题：** {page_content_info.get('title', page_info['title'])}
**页面描述：** {page_content_info.get('description', page_info['description'])}

**关键要点（必须全部包含在SVG中）：**
{chr(10).join([f"• {point}" for point in page_content_info.get('key_points', [])])}

**原文相关内容（必须基于此内容设计）：**
{page_content_info.get('source_content', '').strip()}

**详细内容说明：**
{page_content_info.get('detailed_content', '').strip()}

**内容填充要求：**
- 必须使用上述原文内容，不得自行创造内容
- 页面内容必须充实，充分利用画布空间
- 避免大面积空白，确保信息密度合理
- 除必要的视觉间距外，不得留有明显的空白区域
- 如果是数据页面，必须包含具体的数值和图表描述
- 如果是概念页面，必须包含详细的定义和解释
"""
        else:
            # 如果没有提取到具体内容，从原文中动态提取
            content_details = f"""
**本页面内容要求：**
请从以下原文中提取与"{page_info['title']}"和"{page_info['description']}"相关的具体内容：

**原文内容：**
{self.source_content}

**提取要求：**
- 必须提取原文中与本页主题相关的具体内容
- 包含数据、案例、定义、详细说明等实质性信息
- 确保页面内容充实，避免空洞的描述
- 充分利用画布空间，避免大面积空白
"""
        
        # {{CHENGQI:
        # Action: Modified
        # Timestamp: 2025-01-16 10:15:00 +08:00
        # Reason: 优化图像使用策略，将图片使用从建议改为强制要求，增加图片与页面内容的匹配分析
        # Principle_Applied: 单一职责 - 专注于图片匹配; DRY - 避免重复的图片分析; SOLID开闭原则 - 易于扩展新的匹配策略
        # Optimization: 提高AI使用图片的概率，让生成的SVG更美观直观
        # Architectural_Note (AR): 图像匹配逻辑作为独立模块，便于维护和测试
        # Documentation_Note (DW): 明确图片匹配策略和使用要求
        # }}
        # 构建图像信息部分 - 强化版本
        images_info = ""
        if self.extracted_images:
            # 分析当前页面内容，为图片使用提供匹配建议
            page_content_lower = str(page_info).lower() + " " + str(page_content_info).lower()
            
            # 为每张图片分析相关性
            matched_images = []
            for i, img in enumerate(self.extracted_images, 1):
                img_desc = img.get('alt', '').lower()
                img_url_parts = img['url'].lower().split('/')[-1]  # 从URL提取文件名关键词
                
                # 计算相关性评分（简单版本）
                relevance_score = 0
                relevance_reasons = []
                
                # 检查图片描述与页面内容的关联
                if img_desc:
                    desc_words = img_desc.split()
                    for word in desc_words:
                        if len(word) > 2 and word in page_content_lower:
                            relevance_score += 1
                            relevance_reasons.append(f"描述关键词'{word}'与页面内容匹配")
                
                # 检查URL关键词
                if any(keyword in img_url_parts for keyword in ['logo', 'brand', 'company'] if keyword in page_content_lower):
                    relevance_score += 2
                    relevance_reasons.append("品牌/公司相关图片")
                
                # 添加到匹配列表
                matched_images.append({
                    'index': i,
                    'image': img,
                    'relevance_score': relevance_score,
                    'relevance_reasons': relevance_reasons
                })
            
            # 按相关性排序
            matched_images.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            images_info = f"""

**🎨 强制要求：图片使用指导（必须执行）**

⚠️ **重要：本页面必须使用至少一张图片来增强视觉效果！**

**📷 当前页面可用图片（按相关性排序）：**
本页面从源文档中发现 {len(self.extracted_images)} 张图片，以下是与当前页面内容的匹配分析：

"""
            
            for i, match_info in enumerate(matched_images, 1):
                img = match_info['image']
                score = match_info['relevance_score']
                reasons = match_info['relevance_reasons']
                
                priority = "🔥 高优先级" if score >= 2 else "⭐ 推荐使用" if score >= 1 else "💡 可选使用"
                
                images_info += f"""
**图片 {match_info['index']} - {priority}**
- **URL**: {img['url']}
- **描述**: {img.get('alt', '无描述')}
- **相关性评分**: {score}/5
- **匹配原因**: {'; '.join(reasons) if reasons else '通用配图，可用于视觉装饰'}
- **建议用途**: {'主要配图或背景' if score >= 2 else '辅助配图或装饰' if score >= 1 else '背景装饰或填充空白区域'}

"""
            
            images_info += """

**🎯 图片使用强制要求：**

1. **必须选择至少一张图片**：从上述图片中选择最相关的图片在SVG中使用
2. **优先选择高相关性图片**：优先使用相关性评分高的图片
3. **合理的布局位置**：根据页面内容选择合适的图片位置

**📐 具体使用方案（选择其中一种或多种）：**

**方案A - 页面背景图**（推荐用于封面页或主题页）：
```svg
<defs>
  <pattern id="backgroundImage" patternUnits="userSpaceOnUse" width="100%" height="100%">
    <image href="{选择的图片URL}" x="0" y="0" width="{canvas_width}" height="{canvas_height}" 
           opacity="0.2" preserveAspectRatio="xMidYMid slice"/>
  </pattern>
</defs>
<rect width="100%" height="100%" fill="url(#backgroundImage)"/>
```

**方案B - 侧边配图**（推荐用于内容页）：
```svg
<image href="{选择的图片URL}" x="{canvas_width*0.6}" y="{canvas_height*0.2}" 
       width="{canvas_width*0.35}" height="{canvas_height*0.6}" 
       preserveAspectRatio="xMidYMid meet" opacity="0.9"/>
```

**方案C - 头部横幅**（推荐用于标题页）：
```svg
<image href="{选择的图片URL}" x="50" y="50" 
       width="{canvas_width-100}" height="{canvas_height*0.3}" 
       preserveAspectRatio="xMidYMid meet" opacity="0.8"/>
```

**方案D - 内容区域配图**（推荐用于详细内容页）：
```svg
<image href="{选择的图片URL}" x="{canvas_width*0.05}" y="{canvas_height*0.4}" 
       width="{canvas_width*0.4}" height="{canvas_height*0.45}" 
       preserveAspectRatio="xMidYMid meet"/>
```

**🎨 样式增强建议：**
- 可以为图片添加圆角：`rx="10" ry="10"`
- 可以添加边框：`stroke="#CCCCCC" stroke-width="2"`
- 可以添加阴影效果：在defs中定义filter并应用
- 背景图要设置合适的透明度，确保文字清晰可读

**⚠️ 图片使用验证标准：**
生成的SVG将被检查是否包含`<image>`元素，如果没有使用任何图片且有可用图片，将被视为不合格的生成结果。

**注意**: 系统将自动处理图片的下载和Base64转换，你只需在SVG中正确引用图片URL即可。
"""
        else:
            images_info = f"""

**📷 图片信息**: 源文档中未发现图片，本页面将主要使用文字、图形元素和渐变背景进行设计。
请确保通过其他视觉元素（如图标、几何图形、渐变效果）来增强页面的视觉吸引力。

"""
        
        # 构建第二阶段提示词
        stage_two_prompt = f"""
{PPT_SVG}

现在开始第二阶段工作：基于原文内容的精细化SVG生成

{images_info}

**已确认的设计规范摘要：**
- 主题模式: {self.design_spec.theme_selection.get('mode', 'auto')}
- 主色调: {self.design_spec.color_palette.get('primary', '#4A86E8')}
- 画布尺寸: {self.design_spec.canvas_size['width']}x{self.design_spec.canvas_size['height']}
- 页面总数: {len(self.design_spec.content_outline)}

**当前生成页面信息：**
- 页面编号: {page_number}
- 页面标题: {page_info['title']}
- 页面描述: {page_info['description']}
- 布局类型: {page_info.get('layout_type', 'auto')}

{content_details}

**自定义调整要求：**
{custom_adjustments if custom_adjustments else "严格按照设计规范生成，无特殊调整。"}

**完整设计规范：**
{self.design_spec.raw_spec}

**🎨 内容感知视觉效果专项要求（必须严格遵循）：**

**📊 当前页面智能分析结果：**
- 页面类型: {content_analysis.get('page_type', 'general')}
- 视觉风格: {content_analysis.get('visual_style', 'standard')}
- 渐变强度: {content_analysis.get('gradient_intensity', 'medium')}
- 装饰类型: {content_analysis.get('decoration_type', 'minimal')}
- 高亮级别: {content_analysis.get('highlight_level', 'normal')}
- 特殊效果: {', '.join(content_analysis.get('special_effects', []))}

{content_aware_prompt}

**1. 页面类型智能识别与样式应用：**
根据页面内容自动判断页面类型并应用对应的视觉效果：

*封面页面检测*：如果页面标题包含"封面"、"标题"、或页面编号为1且包含总标题信息
→ 应用向上渐变背景（从深到浅体现积极前景）
→ 主标题使用用户配色的最亮变体进行高亮显示
→ 添加向上流动的装饰元素

*数据展示页面检测*：如果页面内容包含数字、百分比、"数据"、"统计"、"指标"等关键词
→ 底部30%区域使用特殊渐变背景突出（透明度0.2-0.4）
→ 数据卡片采用渐变边框设计（stroke="url(#cardBorderGradient)"）
→ 关键数值使用accent色高亮并加大字号

*概念介绍页面检测*：如果页面包含3个或更多要点、特性、优势等并列内容
→ 主要板块使用半透明卡片承载（opacity: 0.85-0.95）
→ 卡片边框使用渐变色效果
→ 关键词汇使用亮色高亮显示（用户配色+25%亮度）

*总结页面检测*：如果页面标题包含"总结"、"结论"、"展望"、"未来"等词汇
→ 强化向上渐变效果，使用两种渐变叠加
→ 采用对称平衡的布局设计
→ 添加向上箭头、渐进图标等正面符号装饰

**2. 用户配置与内容特征融合：**
- **基础配色方案**：严格使用用户配置的color_theme作为基础色彩
- **智能色彩增强**：在用户配色基础上生成渐变变体和高亮色
- **风格保持一致**：在用户选择的style（现代/商务/创意/学术）框架内进行视觉增强

**3. 必须应用的SVG技术规范：**

```svg
<svg viewBox="0 0 {self.design_spec.canvas_size['width']} {self.design_spec.canvas_size['height']}" 
     preserveAspectRatio="xMidYMid meet" 
     xmlns="http://www.w3.org/2000/svg">
  
  <defs>
    <!-- 根据页面类型自动生成的渐变定义 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" stop-color="{{用户主色深色变体}}" />
      <stop offset="100%" stop-color="{{用户主色浅色变体}}" />
    </linearGradient>
    
    <linearGradient id="cardBorderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="{{用户主色}}" stop-opacity="0.8" />
      <stop offset="50%" stop-color="{{用户辅助色}}" stop-opacity="0.6" />
      <stop offset="100%" stop-color="{{用户主色}}" stop-opacity="0.8" />
    </linearGradient>
    
    <!-- 如果是数据页面，添加数据区域背景 -->
    <linearGradient id="dataAreaBg" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="{{用户背景色}}" stop-opacity="0.3" />
      <stop offset="100%" stop-color="{{用户主色}}" stop-opacity="0.1" />
    </linearGradient>
  </defs>
  
  <!-- 页面背景 -->
  <rect width="100%" height="100%" fill="url(#backgroundGradient)"/>
  
  <!-- 卡片使用示例 -->
  <rect x="100" y="200" width="30%" height="400" 
        fill="{{卡片背景色}}" fill-opacity="0.9" 
        stroke="url(#cardBorderGradient)" stroke-width="3" 
        rx="16"/>
</svg>
```

**4. 关键词智能高亮要求：**
- 自动识别页面中的重要术语、数据、概念名词
- 重要数字使用大字号+accent色+粗体
- 关键概念使用高亮背景+对比色文字
- 技术术语使用边框+特殊字体样式

**5. 装饰元素内容感知选择：**
根据页面内容自动选择合适的装饰元素：
- 科技内容：几何线条、电路图案（opacity: 0.1）
- 商业内容：箭头指示、增长曲线（opacity: 0.15）
- 学术内容：简洁边框、经典图标（opacity: 0.08）
- 创意内容：流动曲线、艺术图形（opacity: 0.12）

**⚠️ 重要：文字布局要求（必须严格遵守）：**

1. **行间距要求**：
   - 所有文本行高至少为字号的1.6倍
   - 多行文本使用<tspan dy="行间距">，行间距=字号×1.6
   - 例如：字号24px，行间距至少38px

2. **元素间距要求**：
   - 标题与正文间距：至少20px
   - 不同段落间距：至少16px
   - 不同内容块间距：至少40px
   - 卡片内边距：至少25px

3. **文字换行处理**：
   - 长文本必须使用<tspan>标签分行
   - 每行长度不超过容器宽度的85%
   - 换行要保持语义完整

4. **布局检查**：
   - 确认所有文字不重叠
   - 确认内容不溢出容器边界
   - 确认各层级文字有明显间距差异

**6. 响应式设计要求：**
- 主标题字号不得小于画布宽度的2.5%
- 核心内容区域占画布面积不少于60%
- 关键信息在任何缩放下都清晰可读
- 使用百分比宽度确保弹性布局

**关键生成要求：**
1. **内容完整性**：必须严格使用提供的原文内容，不得自行创造或编造内容
2. **空间利用**：页面必须充实丰富，充分利用 {self.design_spec.canvas_size['width']}x{self.design_spec.canvas_size['height']} 的画布空间
3. **文字清晰性**：除了必要的标题区域和合理的边距外，不得有大面积空白，但更重要的是确保文字清晰可读，绝不能出现文字重叠
4. **内容层次性**：如果是目录页以外的内容页，必须包含大量实质性信息，文本内容要分层次展示
5. **视觉丰富性**：使用不同字号和颜色突出重点，适当添加视觉元素（如图标、分割线、装饰性图形）
6. **信息完整性**：确保所有提供的关键要点都在SVG中有所体现
7. **配置一致性**：严格遵循用户前端配置的同时，智能应用内容感知的视觉增强

**绝对避免的问题：**
- ❌ 文字重叠或挤压
- ❌ 行间距过小导致难以阅读
- ❌ 页面内容稀疏，存在大片不必要的空白区域
- ❌ 只有标题和简单描述，缺乏具体内容
- ❌ 忽略提供的原文内容，自行创造内容
- ❌ 布局过于简单，未充分利用画布空间
- ❌ 文字超出容器边界
- ❌ 违背用户前端配置选择

**输出要求：**
请直接输出完整的SVG代码，确保：
- 内容丰富且完全基于提供的原文内容
- 文字布局清晰，严格符合间距要求
- 没有任何文字重叠或视觉冲突
- 充分利用画布空间但保持良好的可读性
- 完美融合用户配置与内容特征

当前时间是：{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
"""
        
        messages = [
            {"role": "system", "content": "你是一位顶尖的AI演示文稿设计师，现在需要根据确认的设计规范生成具体的SVG页面代码。"},
            {"role": "user", "content": stage_two_prompt}
        ]
        
        self._notify_progress(f"生成第{page_number}页", 0.3, "正在调用AI模型生成SVG...")
        
        # 支持流式响应的SVG生成
        svg_content = ""
        
        async def svg_stream_callback(chunk: str) -> bool:
            nonlocal svg_content
            if self._check_cancellation():
                return False  # 返回False终止流式响应
            svg_content += chunk
            # 可以在这里添加实时预览功能
            return True
        
        stream_callback = svg_stream_callback if self.config.enable_streaming else None
        result = await self._call_ai_model(messages, stream_callback)
        #logger.info(f"result: {result}")
        if "error" in result:
            # 如果是重试失败，提供更详细的错误信息
            if "retry_attempts" in result:
                logger.error(f"第{page_number}页SVG生成失败，经过{result['retry_attempts']}次尝试后仍然失败")
                return {
                    "error": f"第{page_number}页SVG生成失败：{result['last_error']}",
                    "retry_info": f"经过{result['retry_attempts']}次尝试",
                    "all_errors": result.get('all_errors', []),
                    "page_number": page_number
                }
            return result
        
        if self._check_cancellation():
            return {"error": "操作已取消", "cancelled": True}
        
        self._notify_progress(f"生成第{page_number}页", 0.8, "正在验证SVG代码...")
        
        # 获取最终的SVG内容
        final_svg = svg_content if self.config.enable_streaming else result["content"]
        
        # 验证和清理SVG代码
        try:
            cleaned_svg = self._validate_and_clean_svg(final_svg)
            #logger.info(f"cleaned_svg: {cleaned_svg}")
            #把生成的svg内容保存到本地output目录下,目录不存在则创建
            #if not os.path.exists("output"):
            #    os.makedirs("output")
            #with open(f"output/svg_page_{page_number}.svg", "w", encoding="utf-8") as f:
            #    f.write(cleaned_svg)
            #logger.info(f"svg_page_{page_number}.svg 已保存到本地output目录下")
            # 创建或更新页面内容
            page_content = PageContent(
                page_number=page_number,
                title=page_info['title'],
                layout_type=page_info.get('layout_type', 'auto'),
                content=page_info,
                svg_code=cleaned_svg,
                generated_at=datetime.now().isoformat(),
                extracted_content=page_content_info  # 保存提取的内容
            )
            
            # 更新或添加到页面列表
            existing_page_index = None
            for i, page in enumerate(self.pages):
                if page.page_number == page_number:
                    existing_page_index = i
                    break
            
            if existing_page_index is not None:
                self.pages[existing_page_index] = page_content
            else:
                self.pages.append(page_content)
            
            self._notify_progress(f"生成第{page_number}页", 1.0, f"第{page_number}页生成完成")
            
            logger.info(f"第 {page_number} 页 SVG 生成完成")
            
            return {
                "success": True,
                "page_number": page_number,
                "title": page_info['title'],
                "svg_code": cleaned_svg,
                "generated_at": page_content.generated_at
            }
            
        except Exception as e:
            logger.error(f"SVG验证失败: {str(e)}")
            return {"error": f"SVG验证失败: {str(e)}"}
    
    def _validate_and_clean_svg(self, svg_content: str) -> str:
        """验证并清理SVG内容
        
        Args:
            svg_content: 原始SVG内容
            
        Returns:
            清理后的SVG内容
        """
        if not svg_content:
            raise ValueError("SVG内容为空")
        
        # 确保SVG内容以<svg开头
        if not svg_content.strip().startswith("<svg"):
            # 尝试提取SVG标签
            svg_match = re.search(r'(<svg[^>]*>.*?</svg>)', svg_content, re.DOTALL)
            if svg_match:
                svg_content = svg_match.group(1)
            else:
                raise ValueError("无法找到有效的SVG标签")
        
        # {{CHENGQI:
        # Action: Added
        # Timestamp: 2025-01-16 10:20:00 +08:00
        # Reason: 增加图片使用验证功能，确保AI生成的SVG真正使用了可用的图片
        # Principle_Applied: 单一职责 - 专门验证图片使用; DRY - 避免重复验证逻辑
        # Optimization: 提高SVG质量控制，确保图片被有效使用
        # Architectural_Note (AR): 验证逻辑与清理逻辑分离，便于维护
        # Documentation_Note (DW): 明确图片验证标准和处理流程
        # }}
        # 验证图片使用情况
        self._validate_image_usage(svg_content)
        
        # 验证文字布局和间距
        self._validate_text_layout(svg_content)

        # 修复SVG语法错误（在处理图片之前）
        svg_content = image_processor.fix_svg_syntax_errors(svg_content)

        # 处理SVG中的图片链接
        svg_content = image_processor.process_svg_images(svg_content)
        
        # 修复常见问题
        # 1. 移除多余的xmlns:xlink声明
        svg_content = re.sub(r'xmlns:xlink="[^"]*"\s+xmlns:xlink="[^"]*"', 'xmlns:xlink="http://www.w3.org/1999/xlink"', svg_content)
        
        # 2. 确保viewBox属性存在
        if 'viewBox' not in svg_content and ('width=' in svg_content and 'height=' in svg_content):
            width_match = re.search(r'width="(\d+)"', svg_content)
            height_match = re.search(r'height="(\d+)"', svg_content)
            if width_match and height_match:
                width = width_match.group(1)
                height = height_match.group(1)
                svg_content = svg_content.replace('<svg', f'<svg viewBox="0 0 {width} {height}"', 1)
        
        # 3. 修复不完整的SVG标签
        if not svg_content.strip().endswith("</svg>"):
            svg_content = svg_content + "</svg>"
            
        # 4. 修复HTML实体错误，将&nbsp;替换为XML实体
        svg_content = svg_content.replace("&nbsp;", "&#160;")
        
        # 5. 检查并修复其他常见HTML实体
        html_entities = {
            "&lt;": "&#60;",
            "&gt;": "&#62;",
            "&amp;": "&#38;",
            "&quot;": "&#34;",
            "&apos;": "&#39;",
            "&cent;": "&#162;",
            "&pound;": "&#163;",
            "&yen;": "&#165;",
            "&euro;": "&#8364;",
            "&copy;": "&#169;",
            "&reg;": "&#174;"
        }
        
        for entity, replacement in html_entities.items():
            svg_content = svg_content.replace(entity, replacement)
            
        # 6. 处理代码示例中的HTML实体引用
        # 找到所有代码示例中的实体引用，如`&lt;svg&gt;`，并替换为XML实体
        def replace_code_entities(match):
            code = match.group(0)
            for entity, replacement in html_entities.items():
                code = code.replace(entity, replacement)
            return code
            
        # 查找代码块（使用`包围的内容）
        svg_content = re.sub(r'`[^`]+`', replace_code_entities, svg_content)
        
        return svg_content
    
    # {{CHENGQI:
    # Action: Added
    # Timestamp: 2025-01-16 10:22:00 +08:00
    # Reason: 新增图片使用验证方法，检查SVG是否包含图片元素
    # Principle_Applied: 单一职责 - 专门处理图片验证; 开闭原则 - 易于扩展验证规则
    # Optimization: 独立的验证方法，提高代码可读性和可维护性
    # Architectural_Note (AR): 验证逻辑模块化，支持不同的验证策略
    # Documentation_Note (DW): 详细记录验证标准和警告信息
    # }}
    def _validate_image_usage(self, svg_content: str) -> None:
        """验证SVG中的图片使用情况
        
        Args:
            svg_content: SVG内容
        """
        # 检查是否有可用图片
        if not self.extracted_images:
            return  # 没有可用图片，无需验证
        
        # 检查SVG中是否包含图片元素
        image_patterns = [
            r'<image[^>]*href=["\'][^"\']+["\'][^>]*>',  # <image href="...">
            r'<image[^>]*href=["\'][^"\']+["\'][^>]*/>',  # <image href="..."/>
            r'url\(["\']?[^"\']+\.(jpg|jpeg|png|gif|svg|webp)["\']?\)',  # CSS背景图
            r'fill=["\']url\(#[^)]*\)["\']',  # pattern填充
        ]
        
        has_images = False
        for pattern in image_patterns:
            if re.search(pattern, svg_content, re.IGNORECASE):
                has_images = True
                break
        
        if not has_images:
            # 生成详细的警告信息
            available_images = [f"{i+1}. {img['url']} ({img.get('alt', '无描述')})" 
                              for i, img in enumerate(self.extracted_images)]
            
            warning_msg = f"""
⚠️ 图片使用验证警告：
- 源文档中有 {len(self.extracted_images)} 张可用图片，但生成的SVG中未发现任何图片元素
- 可用图片列表：
{chr(10).join(available_images)}
- 建议：重新生成时应选择至少一张相关图片来增强视觉效果
- 这可能影响PPT的视觉吸引力和用户体验
"""
            logger.warning(warning_msg)
            
            # 可以选择抛出异常或仅记录警告
            # 这里选择记录警告，让用户看到结果但了解问题
        else:
            # 统计使用的图片数量
            image_count = len(re.findall(r'<image[^>]*href=["\'][^"\']+["\']', svg_content, re.IGNORECASE))
            pattern_count = len(re.findall(r'url\(["\']?[^"\']+\.(jpg|jpeg|png|gif|svg|webp)["\']?\)', svg_content, re.IGNORECASE))
            total_usage = image_count + pattern_count
            
            logger.info(f"✅ 图片使用验证通过：SVG中发现 {total_usage} 个图片引用（<image>元素: {image_count}, 背景图: {pattern_count}）")
    





    
    async def generate_all_pages(self, start_page: int = 1, end_page: Optional[int] = None) -> Dict[str, Any]:
        """生成所有页面的SVG
        
        Args:
            start_page: 起始页面编号
            end_page: 结束页面编号，为None时生成到最后一页
            
        Returns:
            生成结果汇总
        """
        if not self.design_spec:
            return {"error": "尚未生成设计规范，请先调用 generate_design_specification"}
        
        if self.current_stage != GenerationStage.CONFIRMED and self.current_stage != GenerationStage.GENERATING_PAGES:
            return {"error": "设计规范尚未确认，请先调用 confirm_design_specification"}
        
        total_pages = len(self.design_spec.content_outline)
        if end_page is None:
            end_page = total_pages
        
        end_page = min(end_page, total_pages)
        
        logger.info(f"开始批量生成SVG页面: 第{start_page}页 到 第{end_page}页")
        
        self.current_stage = GenerationStage.GENERATING_PAGES
        
        generated_pages = []
        failed_pages = []
        
        for page_num in range(start_page, end_page + 1):
            if self._check_cancellation():
                logger.info("批量生成被用户取消")
                break
            
            try:
                # 计算页面开始生成时的进度 - 基于已完成的页面数
                completed_pages = page_num - start_page
                pages_start_progress = completed_pages / (end_page - start_page + 1)
                self._notify_progress("生成页面", pages_start_progress, f"正在生成第 {page_num}/{end_page} 页...")
                
                result = await self.generate_svg_page(page_num)
                
                if result.get("success"):
                    generated_pages.append({
                        "page_number": page_num,
                        "title": result["title"],
                        "generated_at": result["generated_at"]
                    })
                    logger.info(f"第 {page_num} 页生成成功")
                    
                    # 增量发送SVG数据：获取当前页面的SVG代码并通过进度回调发送
                    current_page = None
                    for page in self.pages:
                        if page.page_number == page_num:
                            current_page = page
                            break
                    
                    if current_page and current_page.svg_code:
                        # 计算页面完成后的进度 - 基于已完成的页面数
                        completed_pages_after = page_num - start_page + 1
                        pages_completed_progress = completed_pages_after / (end_page - start_page + 1)
                        
                        # 通过进度回调发送增量SVG数据，包含精确的进度信息
                        self._notify_progress("页面完成", pages_completed_progress, f"第 {page_num}/{end_page} 页生成完成", {
                            "type": "svg_page_completed",
                            "page_data": {
                                "page_number": page_num,
                                "title": current_page.title,
                                "svg_code": current_page.svg_code,
                                "generated_at": current_page.generated_at,
                                "layout_type": current_page.layout_type
                            },
                            "progress_info": {
                                "completed_pages": completed_pages_after,
                                "total_pages": end_page - start_page + 1,
                                "percentage": pages_completed_progress * 100
                            }
                        })
                else:
                    failed_pages.append({
                        "page_number": page_num,
                        "error": result.get("error", "未知错误")
                    })
                    logger.error(f"第 {page_num} 页生成失败: {result.get('error')}")
                
                # 短暂延迟，避免过快请求
                #await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"生成第 {page_num} 页时发生异常: {str(e)}")
                failed_pages.append({
                    "page_number": page_num,
                    "error": str(e)
                })
        
        if len(generated_pages) == (end_page - start_page + 1):
            self.current_stage = GenerationStage.COMPLETED
            logger.info(f"所有页面生成完成，共 {len(generated_pages)} 页")
        
        return {
            "success": len(failed_pages) == 0,
            "total_requested": end_page - start_page + 1,
            "generated_count": len(generated_pages),
            "failed_count": len(failed_pages),
            "generated_pages": generated_pages,
            "failed_pages": failed_pages,
            "completed": self.current_stage == GenerationStage.COMPLETED
        }
    
    async def generate_complete_presentation(self, content: str, custom_requirements: str = "", style_config: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """一键生成完整演示文稿
        
        Args:
            content: 源文档内容
            custom_requirements: 自定义需求
            style_config: 风格配置
            
        Returns:
            完整的生成结果
        """
        logger.info("开始一键生成完整演示文稿...")
        
        try:
            # 第一阶段：生成设计规范（占总进度的10%）
            self._notify_progress("完整生成", 0.0, "开始生成设计规范...")
            
            spec_result = await self.generate_design_specification(content, custom_requirements, style_config)
            if "error" in spec_result:
                return spec_result
            
            # 自动确认设计规范（占总进度的5%）
            self._notify_progress("完整生成", 0.1, "自动确认设计规范...")
            
            confirm_result = await self.confirm_design_specification()
            if "error" in confirm_result:
                return confirm_result
            
            # 获取总页面数，用于精确计算进度
            if not self.design_spec or not self.design_spec.content_outline:
                return {"error": "设计规范生成失败或页面信息为空"}
            
            total_pages = len(self.design_spec.content_outline)
            logger.info(f"检测到总共需要生成 {total_pages} 页SVG")
            
            # 第二阶段：生成所有页面（占总进度的85%，即15%-100%）
            self._notify_progress("完整生成", 0.15, f"开始生成 {total_pages} 页SVG...")
            
            # 创建带进度映射的回调函数
            def progress_mapper(stage: str, progress: float, message: str = "", extra_data: Optional[Dict] = None):
                # 将页面生成的进度（0-1）映射到总进度的15%-100%
                mapped_progress = 0.15 + (progress * 0.85)
                
                # 直接调用原始回调，避免通过 _notify_progress 导致递归
                if original_callback:
                    try:
                        # 检查是否是异步回调
                        import asyncio
                        import inspect
                        
                        if asyncio.iscoroutinefunction(original_callback):
                            # 异步回调处理
                            try:
                                loop = asyncio.get_event_loop()
                                if loop.is_running():
                                    if extra_data:
                                        asyncio.create_task(original_callback(stage, mapped_progress, message, extra_data))
                                    else:
                                        asyncio.create_task(original_callback(stage, mapped_progress, message))
                                else:
                                    if extra_data:
                                        loop.run_until_complete(original_callback(stage, mapped_progress, message, extra_data))
                                    else:
                                        loop.run_until_complete(original_callback(stage, mapped_progress, message))
                            except Exception as e:
                                logger.warning(f"异步进度回调执行失败: {e}")
                        else:
                            # 同步回调处理
                            sig = inspect.signature(original_callback)
                            param_count = len(sig.parameters)
                            
                            if param_count >= 4 and extra_data:
                                original_callback(stage, mapped_progress, message, extra_data)
                            elif param_count >= 3:
                                original_callback(stage, mapped_progress, message)
                            else:
                                original_callback(stage, mapped_progress)
                    except Exception as e:
                        logger.warning(f"进度回调执行失败: {e}")
            
            # 临时替换进度回调
            original_callback = self.progress_callback
            self.progress_callback = progress_mapper
            
            try:
                pages_result = await self.generate_all_pages()
            finally:
                # 恢复原始回调
                self.progress_callback = original_callback
            
            self._notify_progress("完整生成", 1.0, f"完整演示文稿生成完成！共生成 {pages_result.get('generated_count', 0)}/{total_pages} 页")
            
            # 整合结果
            return {
                "success": pages_result.get("success", False),
                "design_spec": asdict(self.design_spec) if self.design_spec else None,
                "pages_result": pages_result,
                "pages": [asdict(page) for page in self.pages],
                "generation_summary": {
                    "total_pages": total_pages,
                    "successful_pages": pages_result.get("generated_count", 0),
                    "failed_pages": pages_result.get("failed_count", 0),
                    "completed_at": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"完整演示文稿生成失败: {str(e)}", exc_info=True)
            self.current_stage = GenerationStage.ERROR
            return {"error": f"完整演示文稿生成失败: {str(e)}"}
    
    def get_page_svg(self, page_number: int) -> Optional[str]:
        """获取指定页面的SVG代码
        
        Args:
            page_number: 页面编号
            
        Returns:
            SVG代码或None
        """
        for page in self.pages:
            if page.page_number == page_number:
                return page.svg_code
        return None
    
    def get_all_pages_svg(self) -> List[Dict[str, Any]]:
        """获取所有页面的SVG代码
        
        Returns:
            包含所有页面信息和SVG代码的列表
        """
        return [
            {
                "page_number": page.page_number,
                "title": page.title,
                "layout_type": page.layout_type,
                "svg_code": page.svg_code,
                "generated_at": page.generated_at
            }
            for page in sorted(self.pages, key=lambda x: x.page_number)
        ]
    
    def export_svg_files(self, output_dir: str) -> Dict[str, Any]:
        """导出SVG文件到指定目录
        
        Args:
            output_dir: 输出目录路径
            
        Returns:
            导出结果
        """
        import os
        
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            exported_files = []
            
            for page in sorted(self.pages, key=lambda x: x.page_number):
                if page.svg_code:
                    filename = f"slide_{page.page_number:02d}_{page.title.replace(' ', '_')}.svg"
                    # 清理文件名中的非法字符
                    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
                    
                    filepath = os.path.join(output_dir, filename)
                    
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(page.svg_code)
                    
                    exported_files.append({
                        "page_number": page.page_number,
                        "title": page.title,
                        "filename": filename,
                        "filepath": filepath
                    })
            
            logger.info(f"成功导出 {len(exported_files)} 个SVG文件到: {output_dir}")
            
            return {
                "success": True,
                "output_dir": output_dir,
                "exported_count": len(exported_files),
                "files": exported_files
            }
            
        except Exception as e:
            logger.error(f"导出SVG文件失败: {str(e)}")
            return {"error": f"导出SVG文件失败: {str(e)}"}
    
    def get_generation_status(self) -> Dict[str, Any]:
        """获取当前生成状态
        
        Returns:
            状态信息
        """
        return {
            "current_stage": self.current_stage.value,
            "has_design_spec": self.design_spec is not None,
            "total_pages_planned": len(self.design_spec.content_outline) if self.design_spec else 0,
            "generated_pages_count": len(self.pages),
            "generated_pages": [page.page_number for page in self.pages],
            "source_content_length": len(self.source_content),
            "config": asdict(self.config)
        }
    
    def reset(self):
        """重置生成器状态"""
        logger.info("重置SVG生成器状态")
        self.current_stage = GenerationStage.INITIAL
        self.design_spec = None
        self.pages = []
        self.source_content = ""
        self._cache = {}

    def _analyze_page_content_type(self, page_info: Dict[str, Any], page_content_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析页面内容类型和特征，为视觉效果应用提供指导
        
        Args:
            page_info: 页面基本信息
            page_content_info: 页面详细内容信息
            
        Returns:
            包含页面类型和视觉特征的字典
        """
        page_title = page_info.get('title', '').lower()
        page_description = page_info.get('description', '').lower()
        page_number = page_info.get('page_number', 0)
        
        # 获取页面内容文本
        content_text = ""
        if page_content_info:
            content_text += " ".join(page_content_info.get('key_points', []))
            content_text += " " + page_content_info.get('detailed_content', '')
            content_text += " " + page_content_info.get('source_content', '')
        content_text = content_text.lower()
        
        analysis_result = {
            "page_type": "general",
            "visual_style": "standard",
            "gradient_intensity": "medium",
            "decoration_type": "minimal",
            "highlight_level": "normal",
            "special_effects": []
        }
        
        # 封面页面检测
        cover_keywords = ['封面', '标题', 'cover', 'title', '首页']
        if (page_number == 1 and any(keyword in page_title for keyword in ['报告', '分析', '研究', '介绍', '方案'])) or \
           any(keyword in page_title for keyword in cover_keywords):
            analysis_result.update({
                "page_type": "cover",
                "visual_style": "upward_gradient",
                "gradient_intensity": "strong",
                "decoration_type": "flowing_elements",
                "highlight_level": "maximum",
                "special_effects": ["background_gradient", "title_highlight", "flowing_decoration"]
            })
            logger.info(f"页面{page_number}识别为封面页面，应用向上渐变效果")
        
        # 数据展示页面检测
        data_keywords = ['数据', '统计', '指标', '百分比', '%', '图表', 'data', '分析结果', '调研', '调查']
        has_numbers = any(char.isdigit() for char in content_text)
        data_score = sum(1 for keyword in data_keywords if keyword in content_text or keyword in page_title)
        
        if data_score >= 2 or (has_numbers and data_score >= 1):
            analysis_result.update({
                "page_type": "data_presentation",
                "visual_style": "data_focused",
                "gradient_intensity": "medium",
                "decoration_type": "chart_elements",
                "highlight_level": "high",
                "special_effects": ["data_area_background", "number_highlight", "chart_decoration"]
            })
            logger.info(f"页面{page_number}识别为数据展示页面，应用数据区域特殊背景")
        
        # 概念介绍页面检测（多要点并列）
        concept_keywords = ['特点', '优势', '要点', '方面', '特征', '功能', '步骤', '阶段']
        bullet_count = len(page_content_info.get('key_points', [])) if page_content_info else 0
        concept_score = sum(1 for keyword in concept_keywords if keyword in content_text or keyword in page_title)
        
        if bullet_count >= 3 or concept_score >= 2:
            analysis_result.update({
                "page_type": "concept_introduction",
                "visual_style": "card_layout",
                "gradient_intensity": "medium",
                "decoration_type": "concept_icons",
                "highlight_level": "high",
                "special_effects": ["semi_transparent_cards", "gradient_borders", "keyword_highlighting"]
            })
            logger.info(f"页面{page_number}识别为概念介绍页面，应用半透明卡片设计")
        
        # 总结页面检测
        summary_keywords = ['总结', '结论', '展望', '未来', '趋势', '前景', '建议', '归纳', 'summary', 'conclusion']
        if any(keyword in page_title for keyword in summary_keywords) or \
           any(keyword in content_text[:200] for keyword in summary_keywords):  # 检查内容开头
            analysis_result.update({
                "page_type": "summary",
                "visual_style": "forward_looking",
                "gradient_intensity": "strong",
                "decoration_type": "progressive_elements",
                "highlight_level": "maximum",
                "special_effects": ["enhanced_gradient", "symmetric_layout", "forward_arrows"]
            })
            logger.info(f"页面{page_number}识别为总结页面，应用前瞻性设计")
        
        # 技术内容检测
        tech_keywords = ['技术', '系统', '架构', '算法', '平台', '方案', '实现', '开发', 'technology']
        if sum(1 for keyword in tech_keywords if keyword in content_text or keyword in page_title) >= 2:
            analysis_result["decoration_type"] = "tech_elements"
            analysis_result["special_effects"].append("tech_decoration")
            logger.info(f"页面{page_number}检测到技术内容，应用技术装饰元素")
        
        # 商业内容检测
        business_keywords = ['市场', '业务', '商业', '营销', '销售', '客户', '盈利', '成本', '投资', '收益']
        if sum(1 for keyword in business_keywords if keyword in content_text or keyword in page_title) >= 2:
            analysis_result["decoration_type"] = "business_elements"
            analysis_result["special_effects"].append("business_decoration")
            logger.info(f"页面{page_number}检测到商业内容，应用商业装饰元素")
        
        # 学术内容检测
        academic_keywords = ['研究', '分析', '理论', '方法', '实验', '学术', '文献', '研究', '调研']
        if sum(1 for keyword in academic_keywords if keyword in content_text or keyword in page_title) >= 2:
            analysis_result["decoration_type"] = "academic_elements"
            analysis_result["special_effects"].append("academic_decoration")
            logger.info(f"页面{page_number}检测到学术内容，应用学术装饰元素")
        
        return analysis_result

    def _build_content_aware_prompt_section(self, content_analysis: Dict[str, Any], page_number: int) -> str:
        """基于内容分析结果构建内容感知的提示词片段
        
        Args:
            content_analysis: 内容分析结果
            page_number: 页面编号
            
        Returns:
            内容感知提示词片段
        """
        page_type = content_analysis.get('page_type', 'general')
        special_effects = content_analysis.get('special_effects', [])
        decoration_type = content_analysis.get('decoration_type', 'minimal')
        highlight_level = content_analysis.get('highlight_level', 'normal')
        
        prompt_sections = []
        
        # 根据页面类型添加专门的指导
        if page_type == "cover":
            prompt_sections.append("""
**封面页面专项要求：**
- 必须应用向上渐变背景：<linearGradient x1="0%" y1="100%" x2="0%" y2="0%">
- 主标题使用用户配色的最亮变体（亮度+30%），字号不小于64px
- 添加向上流动的装饰元素（箭头、流线等）
- 整体布局要体现专业性和吸引力
- 副标题和说明文字使用适度对比的次级颜色""")
        
        elif page_type == "data_presentation":
            prompt_sections.append("""
**数据展示页面专项要求：**
- 底部30%区域必须使用特殊渐变背景突出数据区域
- 所有数值使用accent色高亮且字号放大1.5-2倍
- 数据卡片采用渐变边框：stroke="url(#cardBorderGradient)" stroke-width="3"
- 关键指标使用特殊背景和边框强调
- 添加图表相关的装饰图标（如柱状图标、趋势箭头）""")
        
        elif page_type == "concept_introduction":
            prompt_sections.append("""
**概念介绍页面专项要求：**
- 主要板块必须使用半透明卡片承载（opacity: 0.85-0.95）
- 卡片边框使用渐变色效果，展现层次感
- 关键词汇使用亮色高亮显示（用户配色+25%亮度）
- 每个概念卡片内部要有充分的内边距（30px以上）
- 卡片之间保持40px以上的间距确保呼吸感""")
        
        elif page_type == "summary":
            prompt_sections.append("""
**总结页面专项要求：**
- 强化向上渐变效果，可使用双层渐变叠加
- 采用对称平衡的布局设计，体现总结性特征
- 添加向上箭头、渐进图标等前瞻性装饰元素
- 关键总结点使用最高级别的视觉强调
- 整体色调偏向明亮和积极，体现未来展望""")
        
        # 根据装饰类型添加特定指导
        decoration_instructions = {
            "tech_elements": "添加几何线条、电路图案装饰元素（opacity: 0.1），体现技术感",
            "business_elements": "添加箭头指示、增长曲线装饰元素（opacity: 0.15），体现商业动态",
            "academic_elements": "添加简洁边框、经典图标装饰元素（opacity: 0.08），体现学术严谨",
            "concept_icons": "添加概念相关的图标和符号，增强理解",
            "chart_elements": "添加图表相关的装饰元素，如坐标轴、数据点",
            "progressive_elements": "添加渐进、向上的视觉元素，体现发展趋势",
            "flowing_elements": "添加流动、动态的装饰元素，增加活力感"
        }
        
        if decoration_type in decoration_instructions:
            prompt_sections.append(f"\n**装饰元素要求：**\n- {decoration_instructions[decoration_type]}")
        
        # 根据高亮级别添加指导
        highlight_instructions = {
            "maximum": "所有关键信息都使用最强对比色高亮，字号放大，添加背景色",
            "high": "重要关键词使用accent色高亮，适当加大字号",
            "normal": "标准的关键词高亮处理，保持良好对比度"
        }
        
        if highlight_level in highlight_instructions:
            prompt_sections.append(f"\n**高亮强度要求：**\n- {highlight_instructions[highlight_level]}")
        
        return "\n".join(prompt_sections)
    
    def _validate_text_layout(self, svg_content: str) -> None:
        """
        验证SVG中的文字布局和间距，确保没有重叠问题
        
        Args:
            svg_content: SVG内容
            
        Raises:
            ValueError: 如果发现文字布局问题
        """
        try:
            import xml.etree.ElementTree as ET
            # 移除可能的XML声明，只保留SVG内容
            svg_clean = re.sub(r'^<\?xml[^>]*\?>', '', svg_content.strip())
            root = ET.fromstring(svg_clean)
        except ET.ParseError as e:
            logger.warning(f"无法解析SVG进行文字布局验证: {e}")
            return
        
        # 检查文本元素
        text_elements = []
        
        # 递归查找所有text元素
        def find_text_elements(element):
            if element.tag.endswith('text'):
                text_elements.append(element)
            for child in element:
                find_text_elements(child)
        
        find_text_elements(root)
        
        warnings = []
        
        for text_elem in text_elements:
            # 检查tspan元素的dy值
            tspan_elements = text_elem.findall('.//*[@dy]')
            
            # 估算字号
            font_size = 24  # 默认字号
            if 'font-size' in text_elem.attrib:
                try:
                    font_size = float(text_elem.attrib['font-size'].replace('px', ''))
                except (ValueError, AttributeError):
                    pass
            
            # 检查style属性中的字号
            style = text_elem.get('style', '')
            font_size_match = re.search(r'font-size\s*:\s*(\d+(?:\.\d+)?)px', style)
            if font_size_match:
                try:
                    font_size = float(font_size_match.group(1))
                except ValueError:
                    pass
            
            # 检查行间距
            min_line_height = font_size * 1.8  # 最小行间距要求
            
            for tspan in tspan_elements:
                dy_value = tspan.get('dy', '0')
                try:
                    dy_num = float(dy_value.replace('px', ''))
                    if dy_num > 0 and dy_num < min_line_height:
                        warnings.append(f"发现行间距过小: dy={dy_num}px < 最小要求{min_line_height:.1f}px (字号{font_size}px × 1.8)")
                except (ValueError, AttributeError):
                    continue
        
        # 检查text元素之间的垂直距离
        text_positions = []
        for text_elem in text_elements:
            try:
                y_pos = float(text_elem.get('y', '0'))
                x_pos = float(text_elem.get('x', '0'))
                text_positions.append((x_pos, y_pos, text_elem))
            except (ValueError, TypeError):
                continue
        
        # 按y坐标排序
        text_positions.sort(key=lambda x: x[1])
        
        # 检查相邻文本元素的距离
        for i in range(len(text_positions) - 1):
            current_x, current_y, current_elem = text_positions[i]
            next_x, next_y, next_elem = text_positions[i + 1]
            
            # 如果x坐标相近（同一列），检查y坐标距离
            if abs(current_x - next_x) < 50:  # 同一列的阈值
                y_distance = next_y - current_y
                if y_distance < 30:  # 最小垂直间距
                    warnings.append(f"发现文本元素间距过小: y距离={y_distance:.1f}px < 最小要求30px")
        
        # 如果有警告，记录但不阻止生成
        if warnings:
            logger.warning(f"SVG文字布局验证发现 {len(warnings)} 个潜在问题:")
            for warning in warnings[:5]:  # 只显示前5个警告
                logger.warning(f"  - {warning}")
            if len(warnings) > 5:
                logger.warning(f"  - ... 还有 {len(warnings) - 5} 个其他问题")
        else:
            logger.info("SVG文字布局验证通过，无重叠问题")

# 测试重试功能的示例函数
async def test_retry_functionality():
    """测试重试功能的示例函数"""
    import asyncio
    
    # 创建一个配置，设置较短的重试延迟用于测试
    test_config = GenerationConfig(
        max_retries=2,
        retry_delay=1.0,  # 1秒延迟用于测试
        model="gemini-2.5-flash"
    )
    
    generator = AIPresenterSVGGenerator(test_config)
    
    logger.info("开始测试重试功能...")
    
    # 模拟一个会失败的请求（使用空消息列表）
    test_messages = [
        {"role": "system", "content": "测试重试功能"},
        {"role": "user", "content": "这是一个测试请求"}
    ]
    
    result = await generator._call_ai_model(test_messages)
    
    if "error" in result:
        logger.info(f"重试功能测试完成，结果: {result}")
        if "retry_attempts" in result:
            logger.info(f"成功执行了重试逻辑，尝试次数: {result['retry_attempts']}")
            return True
    else:
        logger.info("测试请求意外成功")
        return True
    
    return False

if __name__ == "__main__":
    # 可以用于测试重试功能
    pass 