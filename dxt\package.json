{"name": "@anthropic-ai/dxt", "description": "Tools for building Desktop Extensions", "version": "0.2.0", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./browser": {"types": "./dist/browser.d.ts", "import": "./dist/browser.js", "require": "./dist/browser.js"}, "./node": {"types": "./dist/node.d.ts", "import": "./dist/node.js", "require": "./dist/node.js"}, "./cli": {"types": "./dist/cli.d.ts", "import": "./dist/cli.js", "require": "./dist/cli.js"}}, "bin": {"dxt": "dist/cli/cli.js"}, "files": ["dist"], "scripts": {"build": "yarn run build:code && yarn run build:schema", "build:code": "tsc", "build:schema": "node --experimental-strip-types ./scripts/build-dxt-schema.js", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "prepublishOnly": "npm run build", "fix": "eslint --ext .ts . --fix && prettier -w .", "lint": "tsc -p ./tsconfig.json && eslint --ext .ts .", "dev-version": "./scripts/create-dev-version.js"}, "author": "Anthropic <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/node-forge": "1.3.11", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^10.0.0", "jest": "^29.7.0", "prettier": "^3.3.3", "ts-jest": "^29.3.2", "typescript": "^5.6.3"}, "dependencies": {"@inquirer/prompts": "^6.0.1", "commander": "^13.1.0", "fflate": "^0.8.2", "node-forge": "^1.3.1", "zod": "^3.25.67"}, "resolutions": {"@babel/helpers": "7.27.1", "@babel/parser": "7.27.3"}}