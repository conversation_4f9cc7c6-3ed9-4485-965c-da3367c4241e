{"$schema": "../../dxt/dist/dxt-manifest.schema.json", "dxt_version": "0.1", "name": "test-user-config", "display_name": "Test User Config Extension", "version": "1.0.0", "description": "Test extension for user configuration functionality", "author": {"name": "Test Author", "email": "<EMAIL>"}, "scripts": {"start": "node server/index.js"}, "server": {"type": "node", "entry_point": "server/index.js", "mcp_config": {"command": "node", "args": ["${__dirname}/server/index.js"], "env": {"API_KEY": "${user_config.api_key}", "DATABASE_URL": "${user_config.database_url}", "PORT": "${user_config.port}", "DEBUG": "${user_config.debug}", "ALLOWED_PATHS": "${user_config.allowed_paths}"}}}, "user_config": {"api_key": {"type": "string", "title": "API密钥", "description": "用于身份验证的API密钥", "sensitive": true, "required": true}, "database_url": {"type": "string", "title": "数据库连接URL", "description": "数据库连接字符串", "required": true}, "port": {"type": "number", "title": "端口号", "description": "服务器监听端口", "default": 3000, "min": 1000, "max": 65535}, "debug": {"type": "boolean", "title": "调试模式", "description": "是否启用调试模式", "default": false}, "allowed_paths": {"type": "directory", "title": "允许访问的目录", "description": "扩展可以访问的目录列表", "multiple": true, "default": ["${HOME}/Documents"]}, "optional_setting": {"type": "string", "title": "可选设置", "description": "这是一个可选的配置项", "required": false, "default": "default_value"}}, "tools": [{"name": "test_tool", "description": "A test tool"}], "compatibility": {"platforms": ["darwin", "win32", "linux"]}, "keywords": ["test", "config"], "license": "MIT"}