import React from 'react';
import ReactDOM from 'react-dom';
import { Typography, Spin, Table } from 'antd';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import rehypeHighlight from 'rehype-highlight';
import remarkGfm from 'remark-gfm';
import mermaid from 'mermaid';
import './MarkdownView.css'; // 导入外部CSS文件

const { Text } = Typography;

// 辅助函数：处理代码内容，确保对象被正确转换为格式化的JSON
function processCodeContent(children) {
  // 如果 children 已经是字符串，直接处理并返回
  if (typeof children === 'string') {
    return children.replace(/\n$/, '');
  }
  // 如果 children 是数字，转换为字符串
  if (typeof children === 'number') {
    return String(children);
  }
  
  // 对于其他类型（如React元素、数组、对象等），
  // 使用 safeChildrenToString 来安全地提取其文本内容。
  const textualContent = safeChildrenToString(children);
  return textualContent.replace(/\n$/, ''); // 移除末尾的换行符，与原逻辑保持一致
}

// 配置mermaid初始化
mermaid.initialize({
  startOnLoad: false, // 修改为false，手动控制渲染
  theme: 'default',
  securityLevel: 'loose', // 允许在安全环境下渲染流程图
  flowchart: {
    htmlLabels: true,
    curve: 'basis',
    diagramPadding: 20,
    useMaxWidth: true, // 允许使用最大宽度
    nodeSpacing: 50,
    rankSpacing: 80
  },
  fontSize: 14, // 减小字体大小
  fontFamily: 'PingFang SC, Microsoft YaHei, Hiragino Sans GB, WenQuanYi Micro Hei, sans-serif',
  themeVariables: {
    primaryColor: '#f0f5ff',
    primaryTextColor: '#000000', // 更改为黑色以增加对比度
    primaryBorderColor: '#3370ff',
    lineColor: '#3370ff',
    secondaryColor: '#f0f0f0',
    tertiaryColor: '#eee',
    fontFamily: 'PingFang SC, Microsoft YaHei, Hiragino Sans GB, WenQuanYi Micro Hei, sans-serif',
    fontSize: '14px', // 减小字体大小
    nodeBorder: '2px solid #3370ff', // 增加节点边框宽度和对比度
    clusterBkg: '#fafafa',
    clusterBorder: '#3370ff',
    labelBackground: '#f0f5ff',
    textColor: '#000000' // 确保文字颜色为黑色
  }
});

// 替换特殊标记为HTML带样式的内容
const parseSpecialMarkers = (text) => {
  if (typeof text !== 'string') {
    console.warn('parseSpecialMarkers接收到非字符串内容:', text);
    text = text ? String(text) : '';
  }
  return text
    .replace(/\[(√|✓)\]/g, '<span>[<span class="task-special-marker">$1</span>]</span>')
    .replace(/\[(->)\]/g, '<span>[<span class="task-special-marker">-&gt;</span>]</span>')
    .replace(/\[([xX])\]/g, '<span>[<span class="task-special-marker">$1</span>]</span>');
};

// 创建一个安全的HTML元素
function createMarkup(html) {
  return { __html: html };
}

// 辅助函数：安全地将React子元素转换为字符串
function safeChildrenToString(children) {
  if (!children) return '';
  
  try {
    return React.Children.toArray(children)
      .map(child => {
        if (typeof child === 'string') return child;
        if (typeof child === 'number') return String(child);
        if (child && child.props) {
          // 尝试提取子元素的文本内容
          if (child.props.value) return child.props.value;
          if (child.props.children) return safeChildrenToString(child.props.children);
        }
        return '';
      })
      .join('');
  } catch (e) {
    console.error('转换子元素到字符串失败:', e);
    return '';
  }
}

// 检测是否为复杂表格内容
const containsTable = (content) => {
  // 检查是否有包含多个|符号的行
  const hasTableDelimiters = content.split('\n').some(line => 
    (line.match(/\|/g) || []).length >= 2
  );
  
  // 检查是否有表格分隔符行 (---|---|---)
  const hasTableHeaders = content.includes('---') && content.includes('|');
  
  return hasTableDelimiters && hasTableHeaders;
};

// Mermaid组件处理流程图的渲染
export class MermaidComponent extends React.Component {
  constructor(props) {
    super(props);
    this.mermaidRef = React.createRef();
    this.state = {
      svgContent: '',
      error: null,
      detailedError: null, // 新增详细错误信息状态
      isPreviewMode: false, // 是否处于预览模式
      renderAttempt: 0 // 渲染尝试次数
    };
  }

  componentDidMount() {
    // 首次挂载时延迟渲染，确保DOM已完全加载
    setTimeout(() => {
      this.renderMermaid();
    }, 300); // 增加延迟确保完全加载
    
    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown);
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.chart !== this.props.chart) {
      this.renderMermaid();
    }
    
    // 如果渲染失败，尝试重新渲染（最多3次）
    if (!this.state.svgContent && this.state.renderAttempt < 3 && !this.state.error) {
      setTimeout(() => {
        this.setState(
          prevState => ({ renderAttempt: prevState.renderAttempt + 1 }),
          () => this.renderMermaid()
        );
      }, 500 * (this.state.renderAttempt + 1)); // 增加延迟时间
    }
  }

  componentWillUnmount() {
    // 清理键盘事件监听
    document.removeEventListener('keydown', this.handleKeyDown);
  }

  renderMermaid() {
    try {
      // 清除先前的错误
      if (this.state.error) {
        this.setState({ error: null, detailedError: null });
      }
      
      const id = `mermaid-svg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // 处理图表代码，确保如果是中文内容能够正确显示
      let chartCode = this.props.chart;
      
      // 如果没有指定图表类型，默认使用flowchart TD (从上到下)
      if (!/(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|gantt|pie|journey|gitGraph)\s/.test(chartCode.split('\n')[0])) {
        chartCode = `graph TD\n${chartCode}`;
      }
      
      // 处理中文字符，确保正确显示
      // 在节点定义中使用双引号而不是方括号
      chartCode = chartCode.replace(/(\w+)\s*\[([^\]]+)\]/g, '$1["$2"]');
      
      // 使用mermaid的API渲染流程图
      mermaid.render(id, chartCode)
        .then(({ svg }) => {
          // 处理SVG以增强文字清晰度和修复中文显示问题
          const enhancedSvg = this.enhanceSvgText(svg);
          this.setState({ svgContent: enhancedSvg, error: null, detailedError: null });
        })
        .catch(error => {
          console.error('Mermaid渲染错误:', error);
          this.setState({ 
            error: '流程图渲染失败：语法错误', 
            detailedError: error.message || '未知Mermaid错误' // 存储详细错误信息
          });
        });
    } catch (error) {
      console.error('Mermaid错误:', error);
      this.setState({ 
        error: '流程图处理失败', 
        detailedError: error.message || 'Mermaid JS执行错误' // 存储详细错误信息
      });
    }
  }
  
  // 增强SVG文字清晰度
  enhanceSvgText(svg) {
    if (!svg) return svg;
    
    // 设置SVG视图大小，确保响应式
    let enhancedSvg = svg.replace(/width="[^"]*"/, 'width="100%"');
    enhancedSvg = enhancedSvg.replace(/height="[^"]*"/, 'height="auto"');
    
    // 移除所有强制性的样式注入，这些样式会破坏Mermaid的布局计算
    // 保留此函数结构以便未来可能的安全增强，但目前应让Mermaid自身处理样式
    
    return enhancedSvg;
  }

  // 切换预览模式
  togglePreview = () => {
    this.setState(prevState => ({
      isPreviewMode: !prevState.isPreviewMode
    }));
  }

  // 渲染预览模态框到body
  renderPreviewPortal() {
    if (!this.state.isPreviewMode) return null;
    
    return ReactDOM.createPortal(
      <div 
        className="mermaid-preview-overlay"
        onClick={this.togglePreview}
      >
        <div 
          className="mermaid-preview-content"
          onClick={(e) => e.stopPropagation()} // 防止点击内容时关闭预览
        >
          <div 
            className="mermaid-preview-close"
            onClick={this.togglePreview}
            title="关闭预览 (ESC)"
          >
            ×
          </div>
          <div 
            className="mermaid mermaid-preview-svg-container" 
            dangerouslySetInnerHTML={{ __html: this.state.svgContent }}
          />
          <div className="mermaid-preview-hint">
            点击背景或按ESC键关闭预览
          </div>
        </div>
      </div>,
      document.body
    );
  }

  // 处理键盘事件
  handleKeyDown = (event) => {
    if (event.key === 'Escape' && this.state.isPreviewMode) {
      this.togglePreview();
    }
  }

  render() {
    if (this.state.error) {
      return (
        <div className="mermaid-error-container">
          <div className="mermaid-error-title">Mermaid 图表渲染失败</div>
          <p className="mermaid-error-message">
            <strong>错误信息:</strong> {this.state.detailedError || this.state.error}
          </p>
          <details className="mermaid-error-details">
            <summary>点击查看图表代码</summary>
            <pre className="mermaid-error-codeblock">{this.props.chart}</pre>
          </details>
        </div>
      );
    }
    
    if (!this.state.svgContent) {
      return <Spin tip="正在生成流程图..." />;
    }
    
    // 普通模式下的样式
    return (
      <>
        <div className="mermaid-container">
          <div 
            className="mermaid mermaid-clickable" 
            dangerouslySetInnerHTML={{ __html: this.state.svgContent }}
            onClick={this.togglePreview}
            title="点击放大查看"
          />
          <div className="mermaid-preview-hint">
            点击图表可放大查看
          </div>
        </div>
        {this.renderPreviewPortal()}
      </>
    );
  }
}

// 解析Markdown表格为Ant Design Table组件需要的数据结构
const parseMarkdownTable = (content) => {
  // 如果内容不包含表格，返回null
  if (!content || !containsTable(content)) {
    return null;
  }
  
  const lines = content.split('\n');
  let tableStart = -1;
  let tableEnd = -1;
  let headerRow = -1;
  
  // 查找表格的开始和结束
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('|') && line.endsWith('|')) {
      // 找到表格的开始
      if (tableStart === -1) {
        tableStart = i;
        headerRow = i;
      }
      
      // 更新表格结束位置
      tableEnd = i;
    } else if (tableStart !== -1 && !line) {
      // 空行后结束表格
      break;
    }
  }
  
  // 如果没有找到表格，返回null
  if (tableStart === -1 || tableEnd === -1) {
    return null;
  }
  
  // 解析表头
  const headerLine = lines[headerRow].trim();
  const headers = headerLine
    .substring(1, headerLine.length - 1)  // 移除首尾的 |
    .split('|')
    .map(h => h.trim());
  
  // 跳过分隔符行
  const dataStartRow = headerRow + 2;
  
  // 解析数据行
  const dataRows = [];
  for (let i = dataStartRow; i <= tableEnd; i++) {
    const line = lines[i].trim();
    if (!line || !line.startsWith('|') || !line.endsWith('|')) {
      continue;
    }
    
    const rowData = line
      .substring(1, line.length - 1)  // 移除首尾的 |
      .split('|')
      .map(cell => cell.trim());
    
    // 创建行数据对象
    const row = {};
    headers.forEach((header, index) => {
      row[header] = index < rowData.length ? rowData[index] : '';
    });
    
    // 添加一个key属性
    row.key = i - dataStartRow;
    dataRows.push(row);
  }
  
  // 创建Table的columns配置
  const columns = headers.map(header => ({
    title: header,
    dataIndex: header,
    key: header,
    ellipsis: false,
    width: 'auto',
    render: text => {
      // 为单元格内容创建可换行的渲染
      return <div className="table-cell-content">{text}</div>;
    }
  }));
  
  return {
    columns,
    dataSource: dataRows,
    tableContent: lines.slice(tableStart, tableEnd + 1).join('\n')
  };
};

// 检查并提取Mermaid代码块
export const extractMermaidBlocks = (content) => {
  if (!content) return { blocks: [], content };

  // 同时支持```mermaid和带有缩进的mermaid代码块
  const mermaidPattern = /```mermaid\n([\s\S]*?)\n```/g;
  const blocks = [];
  let modifiedContent = content;
  let match;

  while ((match = mermaidPattern.exec(content)) !== null) {
    const fullMatch = match[0];
    // 处理可能的缩进问题
    let mermaidCode = match[1].trim();
    
    // 规范化mermaid代码，确保第一行是图表类型定义
    if (!/(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|gantt|pie|journey|gitGraph)\s/.test(mermaidCode.split('\n')[0])) {
      // 如果没有定义图表类型，默认添加flowchart定义
      mermaidCode = `graph TD\n${mermaidCode}`;
    }
    
    blocks.push({
      position: match.index,
      code: mermaidCode,
      fullMatch
    });
  }

  return { blocks, content: modifiedContent };
};

// 检查文本是否包含URL格式的字符串
const containsUrl = (text) => {
  if (!text || typeof text !== 'string') return false;
  return /https?:\/\/\S+/.test(text.trim());
};

// 创建共享的ReactMarkdown组件配置
const createMarkdownComponents = (isTaskTool) => {
  return {
    // 自定义加粗文本渲染
    strong: ({node, children, ...props}) => {
      const style = {
        fontWeight: 'bold',
        wordBreak: 'break-word',
        whiteSpace: 'normal',
        display: 'inline'
      };
      return <strong style={style} {...props}>{children}</strong>;
    },
    
    // 代码块自定义渲染
    code({node, inline, className, children, ...props}) {
      const match = /language-(\w+)/.exec(className || '');
      
      // 获取文本内容进行检查
      const textContent = safeChildrenToString(children);
      
      // 检查是否是URL格式，如果是则不渲染为代码块
      const isUrl = containsUrl(textContent);
      
      if (isUrl) {
        return (
          <a 
            href={textContent.trim()} 
            target="_blank" 
            rel="noopener noreferrer" 
            className="url-link"
          >
            {textContent}
          </a>
        );
      }
      
      return !inline && match ? (
        <SyntaxHighlighter
          style={vscDarkPlus}
          language={match[1]}
          PreTag="div"
          {...props}
        >
          {processCodeContent(children)}
        </SyntaxHighlighter>
      ) : (
        <code className={className} {...props}>
          {processCodeContent(children)}
        </code>
      );
    },
    
    // 链接自定义渲染
    a: ({node, children, href, ...props}) => {
      // 获取链接文本内容
      const linkText = safeChildrenToString(children);
      
      // 检查是否在目录列表中的链接
      let isTocLink = false;
      let parentListItem = node.parent;
      
      while (parentListItem && parentListItem.tagName !== 'li') {
        parentListItem = parentListItem.parent;
      }
      
      if (parentListItem && parentListItem.properties && parentListItem.properties.className) {
        isTocLink = parentListItem.properties.className.includes('toc-item');
      }
      
      // 检查链接是否是URL格式(http或https开头)
      const isUrl = href && (href.startsWith('http://') || href.startsWith('https://'));
      
      // URL格式的链接应该不同于普通链接
      if (isUrl) {
        return (
          <a 
            href={href} 
            target="_blank" 
            rel="noopener noreferrer" 
            className="url-link"
            {...props}
          >
            {children}
          </a>
        );
      }
      
      if (isTocLink) {
        return <a className="toc-link" {...props}>{children}</a>;
      }
      
      return <a target="_blank" rel="noopener noreferrer" {...props}>{children}</a>;
    },
    
    // 自定义段落渲染
    p: ({node, children, ...props}) => {
      const text = safeChildrenToString(children);
      
      // 设置段落样式，确保内容不会溢出
      const style = {
        wordBreak: 'break-word',
        whiteSpace: 'normal',
        width: '100%',
        display: 'block'
      };
      
      if (text.includes('[√]') || text.includes('[->]') || text.includes('[X]') || text.includes('[x]')) {
        const html = parseSpecialMarkers(text);
        return <p style={style} {...props} dangerouslySetInnerHTML={createMarkup(html)} />;
      }
      
      return <p style={style} {...props}>{children}</p>;
    },
    
    // 自定义列表项渲染，确保URL链接正确显示
    li: ({node, children, ...props}) => {
      // 移除style属性，使用className来控制样式
      const { style, ...otherProps } = props;
      
      // 使用className而不是内联样式
      const className = 'markdown-list-item';
      
      // 检查是否在目录列表中
      const parentElement = node.parent;
      let isTocItem = false;
      let tocLevel = 1;
      
      if (parentElement && parentElement.properties && parentElement.properties.className) {
        isTocItem = parentElement.properties.className.includes('toc-container');
      }
      
      if (isTocItem) {
        const textContent = safeChildrenToString(node.children);
        
        // 基于文本内容判断级别
        if (textContent.match(/^\d+\.\d+\.\d+/)) {
          tocLevel = 3;
        } else if (textContent.match(/^\d+\.\d+/)) {
          tocLevel = 2;
        }
        
        // 添加目录相关类名
        const tocClassName = `toc-item toc-level-${tocLevel}`;
        otherProps.className = otherProps.className ? `${otherProps.className} ${tocClassName} ${className}` : `${tocClassName} ${className}`;
      } else {
        // 非目录项，添加基本类名
        otherProps.className = otherProps.className ? `${otherProps.className} ${className}` : className;
      }

      if (isTaskTool) {
        // 如果是任务工具，添加特殊样式类
        otherProps.className = otherProps.className ? `${otherProps.className} task-tool-item` : 'task-tool-item';
      }

      const text = safeChildrenToString(children);

      // 检查是否包含URL标记格式
      const urlMatch = typeof text === 'string' && text.match(/(https?:\/\/[^\s]+)(\s*==\s*\d+|\s*==\s*[0-9a-zA-Z]+)?/);
      
      if (urlMatch) {
        // 特殊处理含有URL格式的列表项
        const url = urlMatch[1];
        const annotation = urlMatch[2] || ''; 
        
        // 替换列表项中的URL为可点击链接
        const htmlContent = text.replace(
          /(https?:\/\/[^\s]+)(\s*==\s*\d+|\s*==\s*[0-9a-zA-Z]+)?/, 
          `<a href="$1" target="_blank" rel="noopener noreferrer" class="url-link">$1</a><span class="url-annotation">$2</span>`
        );
        
        return (
          <li {...otherProps}>
            <span dangerouslySetInnerHTML={createMarkup(htmlContent)} />
          </li>
        );
      }
      
      if (text.includes('[√]') || text.includes('[->]') || text.includes('[X]') || text.includes('[x]')) {
        const html = parseSpecialMarkers(text);
        return (
          <li {...otherProps}>
            <span dangerouslySetInnerHTML={createMarkup(html)} />
          </li>
        );
      }
      
      return <li {...otherProps}>{children}</li>;
    },
    
    // 标题渲染
    h2: ({node, children, ...props}) => {
      const text = safeChildrenToString(children);
      // 检测是否为目录标题
      const isToc = text.includes('目录') || text.includes('Table of Contents');
      
      return (
        <h2 className={isToc ? 'toc-header' : ''} {...props}>
          {children}
        </h2>
      );
    },
    
    // 增强列表渲染，支持目录列表
    ul: ({node, children, ordered, depth, ...props}) => {
      // 检查前一个兄弟元素是否为目录标题
      const parentElement = node.parent;
      let isTocList = false;
      
      if (parentElement && parentElement.children) {
        const currentIndex = parentElement.children.indexOf(node);
        if (currentIndex > 0) {
          const prevSibling = parentElement.children[currentIndex - 1];
          if (prevSibling.tagName === 'h2') {
            const h2Text = prevSibling.children[0]?.value || '';
            isTocList = h2Text.includes('目录') || h2Text.includes('Table of Contents');
          }
        }
      }
      
      return (
        <ul className={isTocList ? 'toc-container' : ''} {...props}>
          {children}
        </ul>
      );
    },
    
    ol: ({node, children, ordered, depth, ...props}) => {
      // 同样检查是否为目录列表
      const parentElement = node.parent;
      let isTocList = false;
      
      if (parentElement && parentElement.children) {
        const currentIndex = parentElement.children.indexOf(node);
        if (currentIndex > 0) {
          const prevSibling = parentElement.children[currentIndex - 1];
          if (prevSibling.tagName === 'h2') {
            const h2Text = prevSibling.children[0]?.value || '';
            isTocList = h2Text.includes('目录') || h2Text.includes('Table of Contents');
          }
        }
      }
      
      return (
        <ol className={isTocList ? 'toc-container' : ''} {...props}>
          {children}
        </ol>
      );
    },
    
    // 表格相关组件
    table: ({node, ...props}) => (
      <div className="table-container">
        <table {...props} />
      </div>
    ),
    thead: ({node, ...props}) => <thead {...props} />,
    tbody: ({node, ...props}) => <tbody {...props} />,
    tr: ({node, ...props}) => <tr {...props} />,
    th: ({node, ...props}) => <th {...props} />,
    td: ({node, ...props}) => <td {...props} />
  };
};

// Markdown视图组件，用于展示工具调用返回的Markdown格式内容
export const MarkdownView = ({ result, name }) => {
  // 打印原始result对象到控制台
  console.log('原始 result 对象:', result);
  console.log('result 类型:', typeof result);
  console.log('result 详细内容:', JSON.stringify(result, null, 2));

  // React hooks必须在组件顶部调用
  React.useEffect(() => {
    // 确保mermaid在组件挂载时正确初始化
    try {
      mermaid.contentLoaded();
    } catch (e) {
      console.error('Mermaid初始化失败:', e);
    }
  }, []);

  // 如果结果为空，显示加载中状态
  if (!result) {
    return (
      <div className="loading-container">
        <Spin />
      </div>
    );
  }

  // 确保result是字符串
  const content = typeof result === 'string' 
    ? result 
    : typeof result === 'object' 
      ? JSON.stringify(result, null, 2) 
      : String(result);
  
  // 检查是否为需要原始显示的工具
  const isRawDisplayTool = name === 'task-decomposition' || name === 'task-update';

  // 如果是指定工具，直接显示原始Markdown
  if (isRawDisplayTool) {
    return (
      <div className="markdown-content raw-markdown-display">
        <pre><code>{content}</code></pre>
      </div>
    );
  }

  // --- 其他工具的渲染逻辑从这里开始 ---

  // 检查是否为task相关工具 (用于其他task类工具，非原始显示的)
  const isTaskTool = name && (name.startsWith('task') || name.includes('task')) && !isRawDisplayTool;

  // 提取Mermaid代码块
  const { blocks: mermaidBlocks } = extractMermaidBlocks(content);
  
  // 解析表格内容
  const tableData = parseMarkdownTable(content);
  
  // 创建统一的Markdown组件配置
  const markdownComponents = createMarkdownComponents(isTaskTool);
  
  // 如果有Mermaid代码块，创建自定义处理的内容
  if (mermaidBlocks.length > 0) {
    // 分割内容并插入Mermaid组件
    let parts = [content];
    
    // 替换所有Mermaid代码块为特殊标记
    mermaidBlocks.forEach((block, index) => {
      const placeholderKey = `__MERMAID_BLOCK_${index}__`;
      parts = parts.map(part => 
        part.replace(block.fullMatch, placeholderKey)
      );
    });
    
    // 使用ReactMarkdown渲染Markdown内容，但特殊处理Mermaid部分
    return (
      <div className="markdown-content">
        {parts.map((part, partIndex) => {
          // 分割部分进行处理
          const segments = part.split(/(__MERMAID_BLOCK_\d+__)/);
          
          return segments.map((segment, segmentIndex) => {
            // 检查是否是Mermaid占位符
            const mermaidMatch = segment.match(/__MERMAID_BLOCK_(\d+)__/);
            
            if (mermaidMatch) {
              const blockIndex = parseInt(mermaidMatch[1], 10);
              // 渲染Mermaid组件
              return (
                <MermaidComponent 
                  key={`mermaid-${blockIndex}`}
                  chart={mermaidBlocks[blockIndex].code}
                />
              );
            }
            
            // 渲染普通Markdown
            return segment ? (
              <ReactMarkdown
                key={`md-${partIndex}-${segmentIndex}`}
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeHighlight]}
                components={markdownComponents}
              >
                {segment}
              </ReactMarkdown>
            ) : null;
          });
        })}
      </div>
    );
  }
  
  // 如果内容包含表格，使用Ant Design Table组件
  if (tableData) {
    // 分割内容，将表格部分提取出来
    const parts = content.split(tableData.tableContent);

    return (
      <div className="markdown-content">
        {/* 渲染表格前的内容 */}
        {parts[0] && (
          <ReactMarkdown 
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeHighlight]}
            components={markdownComponents}
          >
            {parts[0]}
          </ReactMarkdown>
        )}
        
        {/* 使用Ant Design的Table组件渲染表格 */}
        <div className="table-container">
          <Table 
            className="custom-table"
            columns={tableData.columns} 
            dataSource={tableData.dataSource} 
            pagination={false}
            size="middle"
            bordered
            style={{ marginBottom: '16px' }}
            scroll={{ x: 'max-content' }}
            rowKey="key"
            tableLayout="auto"
          />
        </div>
        
        {/* 渲染表格后的内容 */}
        {parts[1] && (
          <ReactMarkdown 
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeHighlight]}
            components={markdownComponents}
          >
            {parts[1]}
          </ReactMarkdown>
        )}
      </div>
    );
  }

  // 没有表格，使用标准Markdown渲染
  return (
    <div className="markdown-content">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={markdownComponents}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownView; 