<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- CSS Styles for Colors, Fonts, and Layout -->
    <style type="text/css">
      /* Color Palette */
      .background-color { fill: #F8FAFC; }
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .container-background { fill: #E0F2FE; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }

      /* Font Families */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; } /* bold */
      .content-title { font-size: 28px; font-weight: 500; line-height: 1.4; } /* medium */
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; } /* normal */

      /* Icon Styles */
      .icon-stroke { stroke: #4A86E8; stroke-width: 2; fill: none; stroke-linecap: round; stroke-linejoin: round; }

      /* Shadows */
      .shadow-subtle { filter: url(#drop-shadow-subtle); }
    </style>

    <!-- Drop Shadow Filter -->
    <filter id="drop-shadow-subtle" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="4"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.08 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#4A86E8"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#0EA5E9"/>
      <stop offset="100%" stop-color="#4A86E8"/>
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC"/>
      <stop offset="100%" stop-color="#E0F2FE"/>
    </linearGradient>
    
    <!-- Accent with transparency gradient for tech feel -->
    <linearGradient id="accentTransparentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#0EA5E9" stop-opacity="0.2"/>
      <stop offset="100%" stop-color="#0EA5E9" stop-opacity="0"/>
    </linearGradient>

  </defs>

  <!-- Overall Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative Elements - Illustrative and Bento Grid inspired -->
  <g class="decorative-elements">
    <!-- Large, soft-edged background shapes -->
    <rect x="1250" y="100" width="500" height="280" rx="30" ry="30" fill="url(#primaryGradient)" opacity="0.1" class="shadow-subtle"/>
    <circle cx="200" cy="900" r="100" fill="#0EA5E9" opacity="0.08" class="shadow-subtle"/>
    <path d="M1920 0L1920 600C1700 650 1400 600 1200 700C1000 800 800 900 600 950C400 1000 200 1080 0 1080L0 0L1920 0Z" fill="#3B82F6" opacity="0.05"/>

    <!-- Outline illustration elements: Book, Lightbulb, Graduation Cap -->
    <!-- Book Icon -->
    <g class="icon-stroke" transform="translate(1400, 450) scale(2)">
      <path d="M4 19V3C4 2.44772 4.44772 2 5 2H19C19.5523 2 20 2.44772 20 3V19C20 19.5523 19.5523 20 19 20H5C4.44772 20 4 19.5523 4 19Z"/>
      <path d="M12 2L12 20"/>
      <path d="M4 6H12"/>
      <path d="M4 10H12"/>
      <path d="M4 14H12"/>
    </g>

    <!-- Lightbulb Icon -->
    <g class="icon-stroke" transform="translate(1650, 600) scale(2)">
      <path d="M9 18H15"/>
      <path d="M12 18V20"/>
      <path d="M12 2C16.4183 2 20 5.58172 20 10C20 14.4183 16.4183 18 12 18C7.58172 18 4 14.4183 4 10C4 5.58172 7.58172 2 12 2Z"/>
    </g>

    <!-- Graduation Cap Icon -->
    <g class="icon-stroke" transform="translate(1300, 750) scale(2)">
      <path d="M22 10L12 5L2 10L12 15L22 10Z"/>
      <path d="M6 12L6 16C6 17.1046 6.89543 18 8 18H16C17.1046 18 18 17.1046 18 16V12"/>
      <path d="M12 15V22"/>
    </g>

    <!-- Abstract connecting lines and dots -->
    <path d="M100 200 C300 100, 500 300, 700 200" stroke="#BAE6FD" stroke-width="2" fill="none" opacity="0.6"/>
    <circle cx="100" cy="200" r="5" fill="#BAE6FD" opacity="0.8"/>
    <circle cx="700" cy="200" r="5" fill="#BAE6FD" opacity="0.8"/>

    <path d="M1800 700 C1600 800, 1400 600, 1200 700" stroke="#BAE6FD" stroke-width="2" fill="none" opacity="0.6"/>
    <circle cx="1800" cy="700" r="5" fill="#BAE6FD" opacity="0.8"/>
    <circle cx="1200" cy="700" r="5" fill="#BAE6FD" opacity="0.8"/>

    <!-- Accent transparent gradient for tech feel (positioned off-center for dynamic effect) -->
    <rect x="-50" y="300" width="300" height="100" fill="url(#accentTransparentGradient)" transform="rotate(-15 0 300)"/>
    <rect x="1750" y="650" width="200" height="80" fill="url(#accentTransparentGradient)" transform="rotate(15 1700 700)"/>
  </g>

  <!-- Brand Logo Area -->
  <g class="logo-container">
    <!-- JIMU品牌Logo -->
    <g transform="translate(80, 60)">
      <!-- Logo背景 -->
      <rect x="0" y="0" width="180" height="60" rx="8" ry="8" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="1"/>
      <!-- Logo图形：两个重叠圆形代表"积木"概念 -->
      <g transform="translate(10, 15)">
        <circle cx="15" cy="15" r="12" fill="#1E40AF" opacity="0.8"/>
        <circle cx="25" cy="15" r="12" fill="#3B82F6" opacity="0.9"/>
      </g>
      <!-- Logo文字 -->
      <text x="55" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="700" fill="#1E40AF">JIMU</text>
      <text x="55" y="40" font-family="Arial, sans-serif" font-size="10" font-weight="400" fill="#64748B">智能演示</text>
    </g>
  </g>

  <!-- Main Content Area: Title and Subtitle -->
  <g class="content-area">
    <!-- Main Title -->
    <text x="80" y="450" class="font-primary hero-title text-primary">
      <tspan x="80" dy="0">{title}</tspan>
    </text>

    <!-- Subtitle -->
    <text x="80" y="550" class="font-secondary content-title text-secondary">
      <tspan x="80" dy="0">{subtitle}</tspan>
    </text>
  </g>

  <!-- Small text elements for page context -->
  <g class="page-info">
    <text x="80" y="980" class="font-secondary small-text text-light">
      <tspan x="80" dy="0">Powered by Innovation</tspan>
    </text>
    <text x="1840" y="980" text-anchor="end" class="font-secondary small-text text-light">
      <tspan x="1840" dy="0">Page 1/10</tspan>
    </text>
  </g>

</svg>