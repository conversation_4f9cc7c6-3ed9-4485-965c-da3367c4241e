<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Colors -->
    <style type="text/css"><![CDATA[
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #3B82F6; }
      .secondary-color { fill: #7DD3FC; }
      .accent-color { fill: #BAE6FD; }
      .text-primary-color { fill: #1E293B; }
      .text-secondary-color { fill: #64748B; }
      .text-light-color { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; }
      .container-background { fill: #E0F2FE; }

      /* Fonts */
      .font-primary { font-family: 'Inter', 'Helvetica', 'Arial', sans-serif; }
      .font-secondary { font-family: 'SF Pro Display', system-ui, sans-serif; }
      .font-accent { font-family: 'Poppins', sans-serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; }
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 600; }
      .content-title { font-size: 28px; font-weight: 600; }
      .body-text { font-size: 22px; font-weight: 400; }
      .small-text { font-size: 16px; font-weight: 400; }
      .caption-text { font-size: 14px; font-weight: 400; }

      /* Line Heights (for multi-tspan, use explicit dy) */
      /* Text Alignment */
      .text-align-left { text-anchor: start; }
      .text-align-center { text-anchor: middle; }
      .text-align-right { text-anchor: end; }

      /* Stroke styles for decorative elements */
      .stroke-primary { stroke: #3B82F6; stroke-width: 2; }
      .stroke-secondary { stroke: #7DD3FC; stroke-width: 2; }
      .stroke-accent { stroke: #BAE6FD; stroke-width: 1; }

      /* Progress bar fill */
      .progress-fill { fill: #3B82F6; }
      .progress-bg { fill: #BAE6FD; }

      /* Current chapter highlight */
      .current-chapter-text-color { fill: #3B82F6; }
    ]]></style>

    <!-- Gradients (as requested in design norms, but subtle for minimalist) -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#7DD3FC" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#BAE6FD" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="bg-color" />

  <!-- Header Section -->
  <g id="header">
    <!-- Logo Placeholder (top-left) -->
    <rect x="80" y="60" width="120" height="40" class="primary-color" rx="8" ry="8" />
    <text x="140" y="88" class="font-primary small-text text-align-center text-primary-color" text-anchor="middle">
      <tspan>{logo_url}</tspan>
    </text>

    <!-- Page Number (top-right) -->
    <text x="1840" y="88" class="font-primary small-text text-align-right text-secondary-color">
      <tspan>2 / 10</tspan>
    </text>
  </g>

  <!-- Main Title and Subtitle -->
  <g id="main-title-section">
    <text x="960" y="200" class="font-primary main-title text-align-center text-primary-color">
      <tspan>{title}</tspan>
    </text>
    <text x="960" y="265" class="font-primary section-title text-align-center text-secondary-color">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- Content Area: Directory Structure -->
  <g id="directory-content">
    <!-- Horizontal Divider Line -->
    <line x1="80" y1="360" x2="1840" y2="360" class="stroke-accent" />

    <!-- Directory Items -->
    <!-- Each item group height approx 80-100px for number, title, description, and gap -->
    <g id="chapter-01">
      <!-- Circle indicator -->
      <circle cx="140" cy="450" r="6" class="secondary-color" />
      <text x="160" y="450" class="font-primary content-title primary-color">
        <tspan>01</tspan>
      </text>
      <text x="240" y="450" class="font-primary content-title text-primary-color">
        <tspan>项目背景和目标</tspan>
      </text>
      <text x="240" y="485" class="font-primary body-text text-secondary-color">
        <tspan>回顾项目启动时的设定、愿景和关键目标</tspan>
      </text>
      <!-- Decorative line below chapter number -->
      <line x1="160" y1="495" x2="210" y2="495" class="stroke-secondary" />
    </g>

    <g id="chapter-02-current">
      <!-- Highlight background for current chapter -->
      <rect x="120" y="520" width="1680" height="100" class="container-background" rx="10" ry="10" />
      <!-- Current chapter indicator line -->
      <rect x="120" y="520" width="10" height="100" class="primary-color" rx="5" ry="5" />
      <!-- Circle indicator -->
      <circle cx="140" cy="580" r="6" class="primary-color" />
      <text x="160" y="580" class="font-primary content-title current-chapter-text-color">
        <tspan>02</tspan>
      </text>
      <text x="240" y="580" class="font-primary content-title current-chapter-text-color">
        <tspan>项目执行情况和进展</tspan>
      </text>
      <text x="240" y="615" class="font-primary body-text text-secondary-color">
        <tspan>详细说明各项任务的执行、资源投入和阶段性成果</tspan>
      </text>
    </g>

    <g id="chapter-03">
      <!-- Circle indicator -->
      <circle cx="140" cy="710" r="6" class="secondary-color" />
      <text x="160" y="710" class="font-primary content-title primary-color">
        <tspan>03</tspan>
      </text>
      <text x="240" y="710" class="font-primary content-title text-primary-color">
        <tspan>问题分析和解决方案</tspan>
      </text>
      <text x="240" y="745" class="font-primary body-text text-secondary-color">
        <tspan>探讨项目遇到的挑战、风险以及采取的应对措施</tspan>
      </text>
      <line x1="160" y1="755" x2="210" y2="755" class="stroke-secondary" />
    </g>

    <g id="chapter-04">
      <!-- Circle indicator -->
      <circle cx="140" cy="840" r="6" class="secondary-color" />
      <text x="160" y="840" class="font-primary content-title primary-color">
        <tspan>04</tspan>
      </text>
      <text x="240" y="840" class="font-primary content-title text-primary-color">
        <tspan>经验总结和改进建议</tspan>
      </text>
      <text x="240" y="875" class="font-primary body-text text-secondary-color">
        <tspan>总结成功的经验、不足之处和未来改进方向</tspan>
      </text>
      <line x1="160" y1="885" x2="210" y2="885" class="stroke-secondary" />
    </g>
  </g>

  <!-- Progress Indicator (Bottom Center) -->
  <g id="progress-indicator">
    <rect x="710" y="960" width="500" height="8" class="progress-bg" rx="4" ry="4" />
    <rect x="710" y="960" width="100" height="8" class="progress-fill" rx="4" ry="4" /> <!-- 20% of 500 is 100 -->
    <text x="960" y="990" class="font-primary small-text text-align-center text-secondary-color">
      <tspan>进度: 20%</tspan>
    </text>
  </g>

  <!-- Footer Section -->
  <g id="footer">
    <text x="80" y="1020" class="font-primary small-text text-align-left text-light-color">
      <tspan>{date}</tspan>
    </text>
    <text x="1840" y="1020" class="font-primary small-text text-align-right text-light-color">
      <tspan>{author}</tspan>
    </text>
  </g>

  <!-- Decorative Elements (Simple lines and shapes, Apple-like minimalism) -->
  <g id="decorative-elements">
    <!-- Top-right corner accent -->
    <line x1="1700" y1="60" x2="1840" y2="60" class="stroke-primary" />
    <line x1="1840" y1="60" x2="1840" y2="120" class="stroke-primary" />

    <!-- Bottom-left corner accent -->
    <line x1="80" y1="960" x2="220" y2="960" class="stroke-primary" />
    <line x1="80" y1="960" x2="80" y2="900" class="stroke-primary" />
  </g>
</svg>