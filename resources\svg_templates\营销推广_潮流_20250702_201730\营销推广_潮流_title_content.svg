<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients Definition -->
    <!-- Background Gradient: Pure black to subtle dark grey for depth -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#000000"/>
      <stop offset="1" stop-color="#1A1A1A"/>
    </linearGradient>

    <!-- Tesla Red Gradient: For high-light elements and text, with transparency for tech feel -->
    <linearGradient id="teslaRedGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#E31937"/>
      <stop offset="1" stop-color="#E31937" stop-opacity="0.7"/>
    </linearGradient>

    <!-- Blue Accent Gradient: For icons and secondary decorative elements -->
    <linearGradient id="blueAccentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#4A86E8"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- CSS Styles Definition -->
    <style type="text/css">
      <![CDATA[
      /* Hero Title Style: Large Chinese text, bold, Tesla Red gradient */
      .title-hero {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-size: 72px; /* hero_title */
        font-weight: 700; /* bold */
        fill: url(#teslaRedGradient);
        letter-spacing: 0em;
      }
      /* Main Title Style: Large Chinese text, bold, white fill */
      .title-main {
        font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        font-size: 56px; /* main_title */
        font-weight: 700; /* bold */
        fill: #FFFFFF; /* White text on dark background */
        letter-spacing: 0em;
      }
      /* Subtitle Style: For secondary titles and explanations */
      .subtitle-main {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 28px; /* content_title */
        font-weight: 400; /* normal */
        fill: #D1D5DB; /* Light grey for secondary text */
        line-height: 1.4; /* normal */
      }
      /* Subtitle Accent Style: For emphasized subtitles, Tesla Red */
      .subtitle-accent {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 28px; /* content_title */
        font-weight: 600; /* semibold */
        fill: #E31937; /* Tesla Red for emphasis */
        line-height: 1.4;
      }
      /* Body Text Style: For main content paragraphs */
      .body-text {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #FFFFFF; /* White for main content */
        line-height: 1.6; /* relaxed */
      }
      /* List Item Style: For bullet points */
      .list-item {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 22px; /* body_text */
        font-weight: 400; /* normal */
        fill: #FFFFFF; /* White for list items */
        line-height: 1.6; /* relaxed */
      }
      /* List Item Bold Style: For emphasis within list items */
      .list-item-bold {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: 22px; /* body_text */
        font-weight: 700; /* bold */
        fill: #FFFFFF; /* White for list items */
        line-height: 1.6;
      }
      /* English Accent Style: Small English text as embellishment */
      .english-accent {
        font-family: 'Segoe UI', sans-serif;
        font-size: 16px; /* small_text */
        font-weight: 300; /* light */
        fill: #D1D5DB; /* Light grey */
        letter-spacing: 0.05em; /* wider */
      }
      /* Card Background Style: Darker panel with Tesla Red border and subtle shadow */
      .card-bg {
        fill: #1A1A1A; /* Slightly lighter black for card background */
        stroke: url(#teslaRedGradient); /* Tesla Red border */
        stroke-width: 2px;
        filter: drop-shadow(0px 8px 16px rgba(0, 0, 0, 0.4)); /* Subtle shadow for depth */
      }
      /* Large Number Style: For prominent statistics or highlights */
      .large-number {
        font-family: 'Segoe UI', sans-serif;
        font-size: 180px; /* Custom very large size */
        font-weight: 900; /* black */
        fill: url(#teslaRedGradient);
        text-anchor: middle; /* Center align for numbers */
      }
      ]]>
    </style>
  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Decorative Elements: Subtle Tesla Red glows and geometric patterns -->
  <g id="decorative_elements">
    <circle cx="1700" cy="100" r="80" fill="#E31937" fill-opacity="0.1"/>
    <circle cx="200" cy="980" r="120" fill="#E31937" fill-opacity="0.05"/>
    <rect x="1600" y="800" width="200" height="10" rx="5" fill="url(#teslaRedGradient)"/>
    <rect x="1590" y="820" width="200" height="10" rx="5" fill="url(#teslaRedGradient)" fill-opacity="0.5"/>
    <rect x="1580" y="840" width="200" height="10" rx="5" fill="url(#teslaRedGradient)" fill-opacity="0.2"/>

    <!-- Subtle geometric shapes with blue accents -->
    <path d="M0 0 L 300 0 L 0 300 Z" fill="#4A86E8" fill-opacity="0.03"/>
    <path d="M1920 1080 L 1620 1080 L 1920 780 Z" fill="#0EA5E9" fill-opacity="0.03"/>
  </g>

  <!-- Main Content Area - Designed with Bento Grid inspiration -->
  <g id="main_content_area">
    <!-- Top-Left Card: Main Title and Subtitle -->
    <rect x="80" y="60" width="1000" height="350" rx="20" class="card-bg"/>

    <!-- Main Title: Chinese (large, bold, Tesla Red gradient) and English (small, light) -->
    <text x="120" y="150" class="title-hero">
      <tspan x="120" y="150">未来已来</tspan>
      <tspan x="120" y="230" class="title-main">智能产品革新</tspan>
    </text>
    <text x="120" y="270" class="english-accent">INNOVATION BEYOND IMAGINATION</text>

    <!-- Subtitle text -->
    <text x="120" y="340" class="subtitle-main">
      <tspan x="120" y="340">我们致力于提供前沿科技和卓越用户体验</tspan>
      <tspan x="120" y="380">通过创新设计和强大功能，塑造行业新标杆。</tspan>
    </text>

    <!-- Bottom-Left Card: Main Content Paragraph -->
    <rect x="80" y="440" width="800" height="280" rx="20" class="card-bg"/>
    <text x="120" y="480" class="body-text">
      <tspan x="120" y="480">深入解析我们的核心产品优势，</tspan>
      <tspan x="120" y="515">体验科技如何赋能您的生活和业务。</tspan>
      <tspan x="120" y="550">从设计美学到功能实用性，</tspan>
      <tspan x="120" y="585">每一个细节都经过精心打磨，</tspan>
      <tspan x="120" y="620">确保为您带来无与伦比的价值。</tspan>
      <tspan x="120" y="655">我们相信，卓越源于对极致的追求。</tspan>
    </text>

    <!-- Right Card: Key Features List -->
    <rect x="900" y="440" width="940" height="580" rx="20" class="card-bg"/>
    <text x="940" y="490" class="subtitle-accent">核心产品优势</text>

    <!-- List Item 1 -->
    <text x="940" y="540" class="list-item">
      <tspan x="940" y="540" font-size="24px" fill="url(#blueAccentGradient)">和#9679;</tspan> <!-- Unicode for circle icon -->
      <tspan x="980" y="540" class="list-item-bold">突破性技术:</tspan>
      <tspan x="980" y="575">采用最新AI算法和硬件集成，实现行业领先性能。</tspan>
    </text>
    <!-- List Item 2 -->
    <text x="940" y="630" class="list-item">
      <tspan x="940" y="630" font-size="24px" fill="url(#blueAccentGradient)">和#9679;</tspan>
      <tspan x="980" y="630" class="list-item-bold">用户友好设计:</tspan>
      <tspan x="980" y="665">直观的操作界面和人体工学设计，提升使用舒适度。</tspan>
    </text>
    <!-- List Item 3 -->
    <text x="940" y="720" class="list-item">
      <tspan x="940" y="720" font-size="24px" fill="url(#blueAccentGradient)">和#9679;</tspan>
      <tspan x="980" y="720" class="list-item-bold">极致安全保障:</tspan>
      <tspan x="980" y="755">多重加密和隐私保护机制，确保您的数据安全。</tspan>
    </text>
    <!-- List Item 4 -->
    <text x="940" y="810" class="list-item">
      <tspan x="940" y="810" font-size="24px" fill="url(#blueAccentGradient)">和#9679;</tspan>
      <tspan x="980" y="810" class="list-item-bold">生态系统兼容:</tspan>
      <tspan x="980" y="845">无缝集成现有智能设备，构建互联互通的智能生活。</tspan>
    </text>
    <!-- List Item 5 -->
    <text x="940" y="900" class="list-item">
      <tspan x="940" y="900" font-size="24px" fill="url(#blueAccentGradient)">和#9679;</tspan>
      <tspan x="980" y="900" class="list-item-bold">持续创新支持:</tspan>
      <tspan x="980" y="935">定期软件更新和技术服务，保障产品始终领先。</tspan>
    </text>

    <!-- Bottom-Center Card: Large Number Emphasis -->
    <rect x="80" y="740" width="800" height="280" rx="20" class="card-bg"/>
    <text x="480" y="880" class="large-number">99%</text>
    <text x="480" y="940" class="subtitle-accent" text-anchor="middle">用户满意度</text>
    <text x="480" y="970" class="english-accent" text-anchor="middle">CUSTOMER SATISFACTION</text>

  </g>

  <!-- Logo Placeholder (Top-Left) -->
  <image href="{logo_url}" x="80" y="20" width="150" height="auto" preserveAspectRatio="xMidYMid meet"/>

  <!-- Content Placeholders (for reference, hidden in actual display) -->
  <!--
  <text x="100" y="100" fill="transparent" font-size="1px">标题: {title}</text>
  <text x="100" y="105" fill="transparent" font-size="1px">副标题: {subtitle}</text>
  <text x="100" y="110" fill="transparent" font-size="1px">正文内容: {content}</text>
  <text x="100" y="115" fill="transparent" font-size="1px">图片: {image_url}</text>
  <text x="100" y="120" fill="transparent" font-size="1px">Logo: {logo_url}</text>
  <text x="100" y="125" fill="transparent" font-size="1px">日期: {date}</text>
  <text x="100" y="130" fill="transparent" font-size="1px">作者: {author}</text>
  -->
</svg>