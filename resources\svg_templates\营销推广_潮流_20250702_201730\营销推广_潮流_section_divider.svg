<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Styles for text and shapes -->
    <style type="text/css">
      <![CDATA[
      .background-fill {
        fill: #000000; /* Pure black background for Bento Grid style */
      }
      .hero-title {
        font-family: Microsoft YaHei, Segoe UI, sans-serif;
        font-weight: 700; /* bold */
        font-size: 72px; /* main_title from font_sizes */
        fill: #F8FAFC; /* Light text color for contrast on black */
      }
      .chapter-number {
        font-family: Microsoft YaHei, Segoe UI, sans-serif;
        font-weight: 700; /* bold */
        font-size: 120px; /* Super large font for emphasis */
      }
      .subtitle-text {
        font-family: Microsoft YaHei, Segoe UI, sans-serif;
        font-weight: 400; /* normal */
        font-size: 36px; /* section_title from font_sizes */
        fill: #94A3B8; /* text_light for secondary text */
      }
      .caption-text {
        font-family: Microsoft YaHei, Segoe UI, sans-serif;
        font-weight: 400;
        font-size: 28px; /* Adjusted from small_text for better readability */
        fill: #94A3B8;
      }
      .accent-gradient-path {
        fill: url(#teslaRedGradient);
      }
      .blue-outline {
        stroke: #4A86E8; /* primary_color */
        stroke-width: 3px;
        fill: none;
      }
      .red-glow {
        filter: url(#glowFilter);
      }
      .blue-fade-fill {
        fill: #4A86E8; /* primary_color */
        opacity: 0.1; /* Subtle blue tint */
      }
      .blue-accent-circle {
        fill: #0EA5E9; /* accent_color */
      }
      .chart-line {
        stroke: #0EA5E9; /* accent_color */
        stroke-width: 4px;
      }
      .chart-point {
        fill: #0EA5E9; /* accent_color */
      }
      ]]>
    </style>

    <!-- Gradients -->
    <linearGradient id="teslaRedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#E31937" />
      <stop offset="100%" stop-color="#FF4B4B" />
    </linearGradient>

    <linearGradient id="blueTechGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A86E8" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.2" />
    </linearGradient>

    <!-- Filters for glow effect -->
    <filter id="glowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="15" result="blur" />
      <feMerge>
        <feMergeNode in="blur" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="background-fill" />

  <!-- Decorative Elements - Trendy, Geometric, Bento Grid Style -->
  <!-- Large accent shape (Tesla Red) - creating a strong visual divide -->
  <path d="M0 0 H960 L1440 540 L960 1080 H0 V0 Z" class="accent-gradient-path red-glow" transform="translate(1000 0) scale(0.8)" />
  <path d="M0 0 H960 L1440 540 L960 1080 H0 V0 Z" class="accent-gradient-path" transform="translate(980 0) scale(0.8)" />

  <!-- Blue outline elements for tech feel and transition -->
  <rect x="100" y="100" width="400" height="250" rx="20" ry="20" class="blue-outline" />
  <circle cx="1700" cy="800" r="150" class="blue-outline" />
  <path d="M150 700 C250 600, 350 800, 450 700 L450 900 C350 1000, 250 800, 150 900 Z" class="blue-outline" />

  <!-- Subtle blue fade fills for depth -->
  <rect x="120" y="120" width="360" height="210" rx="15" ry="15" class="blue-fade-fill" />
  <circle cx="1700" cy="800" r="120" class="blue-fade-fill" />

  <!-- Chapter Title and Subtitle Section -->
  <g transform="translate(120 400)">
    <!-- Chapter Number (Super large, prominent) -->
    <text x="0" y="0" class="chapter-number" style="fill: url(#blueTechGradient);">03</text>
    <!-- Chapter Title -->
    <text x="0" y="120" class="hero-title">{title}</text>
    <!-- Subtitle / Description - Mixed Chinese and English -->
    <text x="0" y="220" class="subtitle-text">
      <tspan x="0" dy="0">{subtitle}</tspan>
      <tspan x="0" dy="50">产品特色 和 市场优势</tspan> <!-- "和" used instead of "和" -->
      <tspan x="0" dy="50">Unlock User Value. 深入洞察用户价值</tspan>
    </text>
  </g>

  <!-- Simple Outline Graphic (Data Visualization Element) -->
  <g transform="translate(1300 150)">
    <rect x="0" y="0" width="400" height="250" rx="15" ry="15" class="blue-outline" />
    <line x1="50" y1="200" x2="150" y2="100" class="chart-line" />
    <line x1="150" y1="100" x2="250" y2="180" class="chart-line" />
    <line x1="250" y1="180" x2="350" y2="80" class="chart-line" />
    <circle cx="50" cy="200" r="8" class="chart-point" />
    <circle cx="150" cy="100" r="8" class="chart-point" />
    <circle cx="250" cy="180" r="8" class="chart-point" />
    <circle cx="350" cy="80" r="8" class="chart-point" />
    <text x="20" y="240" class="caption-text" style="fill: #94A3B8;">数据趋势分析</text>
  </g>

  <!-- Page Number / Progress Indicator -->
  <text x="1800" y="1020" class="caption-text" style="text-anchor: end;">3 / 10</text>

</svg>