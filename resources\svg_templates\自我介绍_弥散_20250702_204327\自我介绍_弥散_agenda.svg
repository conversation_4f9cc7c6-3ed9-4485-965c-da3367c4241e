<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 背景渐变: 从 background_color 到 container_background -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主色渐变: linear-gradient(135deg, #4A86E8, #3B82F6) -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1" gradientUnits="objectBoundingBox">
      <stop stop-color="#4A86E8"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- 强调色渐变: linear-gradient(45deg, #0EA5E9, #06B6D4) -->
    <linearGradient id="accentGradient" x1="0" y1="1" x2="1" y2="0" gradientUnits="objectBoundingBox">
      <stop stop-color="#0EA5E9"/>
      <stop offset="1" stop-color="#06B6D4"/>
    </linearGradient>

    <!-- 柔和发光滤镜，用于弥散风格 -->
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="40" result="blur"/>
      <feMerge>
        <feMergeNode in="blur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 卡片阴影滤镜 -->
    <filter id="cardShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feOffset dx="0" dy="4"/>
      <feGaussianBlur stdDeviation="6" result="offsetBlur"/>
      <feComposite in="SourceGraphic" in2="offsetBlur" operator="over"/>
    </filter>

    <!-- 图标系统定义 -->
    <g id="iconArrowRight">
      <path d="M12 5L19 12L12 19" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M5 12H19" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <g id="iconBook">
      <path d="M4 19.5C4 18.5714 4.89844 17.8571 6 17.8571C7.10156 17.8571 8 18.5714 8 19.5C8 20.4286 7.10156 21.1429 6 21.1429C4.89844 21.1429 4 20.4286 4 19.5ZM4 19.5V4.5C4 3.57143 4.89844 2.85714 6 2.85714C7.10156 2.85714 8 3.57143 8 4.5V19.5ZM12 4.5C12 3.57143 12.8984 2.85714 14 2.85714C15.1016 2.85714 16 3.57143 16 4.5V19.5C16 20.4286 15.1016 21.1429 14 21.1429C12.8984 21.1429 12 20.4286 12 19.5V4.5Z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <g id="iconStar">
      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <g id="iconUser">
      <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <g id="iconTools">
      <path d="M14.71 16.29C14.32 16.68 13.68 16.68 13.29 16.29L11.71 14.71C11.32 14.32 11.32 13.68 11.71 13.29C12.1 12.9 12.74 12.9 13.13 13.29L14.71 14.71C15.1 15.1 15.1 15.74 14.71 16.29Z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M18.36 5.64L16.95 7.05M7.05 16.95L5.64 18.36M18.36 18.36L16.95 16.95M7.05 7.05L5.64 5.64M2 12H4M20 12H22M12 2V4M12 20V22" stroke="#4A86E8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
  </defs>

  <style>
    /* 全局样式 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* 颜色 */
    .color-primary { fill: #3B82F6; }
    .color-secondary { fill: #1E40AF; }
    .color-accent { fill: #06B6D4; }
    .color-background { fill: #F8FAFC; }
    .color-text-primary { fill: #1E293B; }
    .color-text-secondary { fill: #64748B; }
    .color-card-background { fill: #FFFFFF; }
    .color-card-border { stroke: #BAE6FD; }

    /* 字体大小 */
    .text-hero-title { font-size: 72px; }
    .text-main-title { font-size: 56px; }
    .text-section-title { font-size: 36px; }
    .text-content-title { font-size: 28px; }
    .text-body-text { font-size: 22px; }
    .text-small-text { font-size: 16px; }
    .text-caption { font-size: 14px; }

    /* 字体粗细 */
    .font-normal { font-weight: 400; }
    .font-bold { font-weight: 700; }

    /* 卡片样式 */
    .card {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      rx: 12; /* 圆角 */
      ry: 12;
      filter: url(#cardShadow);
    }
    
    /* 装饰元素 */
    .gradient-fill-primary { fill: url(#primaryGradient); }
    .gradient-fill-accent { fill: url(#accentGradient); }
    .soft-glow { filter: url(#softGlow); }

    /* 文本对齐 */
    .text-align-left { text-anchor: start; }
    .text-align-center { text-anchor: middle; }
    .text-align-right { text-anchor: end; }

    /* 图标样式 */
    .icon-base {
      width: 24px;
      height: 24px;
      stroke: #4A86E8;
      stroke-width: 2;
      fill: none; /* 图标为勾线风格 */
    }

  </style>

  <!-- 背景层 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 装饰性弥散形状 (柔和发光) -->
  <circle cx="1700" cy="150" r="120" class="gradient-fill-accent soft-glow" opacity="0.3"/>
  <circle cx="200" cy="900" r="100" class="gradient-fill-primary soft-glow" opacity="0.3"/>
  <rect x="1500" y="800" width="300" height="150" rx="75" ry="75" class="gradient-fill-primary soft-glow" opacity="0.2"/>

  <!-- 主要内容区域 - Bento Grid 风格布局 -->
  <!-- 头部 / Logo -->
  <g id="header">
    <image x="80" y="60" width="120" height="auto" xlink:href="{logo_url}"/>
  </g>

  <!-- 中心标题区域 -->
  <g id="mainTitleSection">
    <text x="960" y="180" class="font-primary text-main-title font-bold color-text-primary text-align-center">
      <tspan x="960" y="180">自我介绍</tspan>
      <tspan x="960" y="240" class="text-content-title color-text-secondary">Self-Introduction</tspan>
    </text>
  </g>

  <!-- 内容概览 / 目录结构 -->
  <g id="contentOverview" transform="translate(240, 320)">
    <!-- 章节 1 -->
    <g class="card-item">
      <rect x="0" y="0" width="700" height="180" class="card"/>
      <use xlink:href="#iconUser" x="40" y="40" width="32" height="32" class="icon-base"/>
      <text x="90" y="65" class="font-primary text-section-title font-bold color-text-primary">
        <tspan>01. 个人简介</tspan>
      </text>
      <text x="90" y="105" class="font-secondary text-body-text color-text-secondary">
        <tspan>关于我：背景、兴趣和目标</tspan>
      </text>
      <text x="90" y="135" class="font-secondary text-small-text color-text-secondary">
        <tspan>About Me: Background, Interests and Goals</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="630" y="74" width="24" height="24" class="icon-base"/>
    </g>

    <!-- 章节 2 -->
    <g class="card-item" transform="translate(0, 210)">
      <rect x="0" y="0" width="700" height="180" class="card"/>
      <use xlink:href="#iconTools" x="40" y="40" width="32" height="32" class="icon-base"/>
      <text x="90" y="65" class="font-primary text-section-title font-bold color-text-primary">
        <tspan>02. 专业技能</tspan>
      </text>
      <text x="90" y="105" class="font-secondary text-body-text color-text-secondary">
        <tspan>核心能力和技术栈</tspan>
      </text>
      <text x="90" y="135" class="font-secondary text-small-text color-text-secondary">
        <tspan>Core Competencies and Tech Stack</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="630" y="74" width="24" height="24" class="icon-base"/>
    </g>

    <!-- 章节 3 -->
    <g class="card-item" transform="translate(0, 420)">
      <rect x="0" y="0" width="700" height="180" class="card"/>
      <use xlink:href="#iconStar" x="40" y="40" width="32" height="32" class="icon-base"/>
      <text x="90" y="65" class="font-primary text-section-title font-bold color-text-primary">
        <tspan>03. 项目经验</tspan>
      </text>
      <text x="90" y="105" class="font-secondary text-body-text color-text-secondary">
        <tspan>精选项目和成果</tspan>
      </text>
      <text x="90" y="135" class="font-secondary text-small-text color-text-secondary">
        <tspan>Featured Projects and Achievements</tspan>
      </text>
      <use xlink:href="#iconArrowRight" x="630" y="74" width="24" height="24" class="icon-base"/>
    </g>
  </g>

  <!-- 章节导航 (右侧) -->
  <g id="chapterNavigation" transform="translate(1000, 320)">
    <rect x="0" y="0" width="680" height="630" class="card"/>

    <text x="40" y="65" class="font-primary text-section-title font-bold color-text-primary">
      <tspan>目录导航</tspan>
    </text>
    <text x="40" y="105" class="font-secondary text-small-text color-text-secondary">
      <tspan>Table of Contents</tspan>
    </text>

    <!-- 导航项 -->
    <g transform="translate(40, 150)">
      <use xlink:href="#iconBook" x="0" y="0" width="24" height="24" class="icon-base"/>
      <text x="40" y="20" class="font-primary text-body-text font-bold color-text-primary">
        <tspan>第一章：个人简介</tspan>
      </text>
      <text x="40" y="50" class="font-secondary text-small-text color-text-secondary">
        <tspan>背景、教育和兴趣爱好</tspan>
      </text>
    </g>

    <g transform="translate(40, 240)">
      <use xlink:href="#iconBook" x="0" y="0" width="24" height="24" class="icon-base"/>
      <text x="40" y="20" class="font-primary text-body-text font-bold color-text-primary">
        <tspan>第二章：专业技能</tspan>
      </text>
      <text x="40" y="50" class="font-secondary text-small-text color-text-secondary">
        <tspan>技术栈、工具和软技能</tspan>
      </text>
    </g>

    <g transform="translate(40, 330)">
      <use xlink:href="#iconBook" x="0" y="0" width="24" height="24" class="icon-base"/>
      <text x="40" y="20" class="font-primary text-body-text font-bold color-text-primary">
        <tspan>第三章：项目经验</tspan>
      </text>
      <text x="40" y="50" class="font-secondary text-small-text color-text-secondary">
        <tspan>核心项目、职责和成果</tspan>
      </text>
    </g>

    <g transform="translate(40, 420)">
      <use xlink:href="#iconBook" x="0" y="0" width="24" height="24" class="icon-base"/>
      <text x="40" y="20" class="font-primary text-body-text font-bold color-text-primary">
        <tspan>第四章：工作经历</tspan>
      </text>
      <text x="40" y="50" class="font-secondary text-small-text color-text-secondary">
        <tspan>职业发展和关键贡献</tspan>
      </text>
    </g>

    <g transform="translate(40, 510)">
      <use xlink:href="#iconBook" x="0" y="0" width="24" height="24" class="icon-base"/>
      <text x="40" y="20" class="font-primary text-body-text font-bold color-text-primary">
        <tspan>第五章：未来展望</tspan>
      </text>
      <text x="40" y="50" class="font-secondary text-small-text color-text-secondary">
        <tspan>职业规划和个人成长</tspan>
      </text>
    </g>
  </g>

  <!-- 进度指示器和页码 -->
  <g id="progressIndicator">
    <rect x="80" y="1000" width="1760" height="12" rx="6" ry="6" fill="#BAE6FD"/> <!-- 背景条 -->
    <rect x="80" y="1000" width="352" height="12" rx="6" ry="6" fill="#3B82F6"/> <!-- 进度条 (20% for 2/10) -->

    <text x="80" y="980" class="font-secondary text-small-text color-text-secondary text-align-left">
      <tspan>进度: 20%</tspan>
    </text>
    <text x="1840" y="980" class="font-secondary text-small-text color-text-secondary text-align-right">
      <tspan>页码: 2/10</tspan>
    </text>
  </g>

  <!-- 占位符：数据概览或图片 -->
  <g id="imageOrChartPlaceholder">
    <rect x="1040" y="400" width="600" height="200" fill="url(#primaryGradient)" opacity="0.1" rx="20" ry="20" class="soft-glow"/>
    <text x="1340" y="510" class="font-primary text-content-title font-normal color-text-secondary text-align-center" opacity="0.6">
      <tspan>数据概览</tspan>
      <tspan x="1340" dy="35" class="text-small-text">Data Overview Placeholder</tspan>
    </text>
  </g>
</svg>