/* {{CHENGQI:
// Action: Added
// Timestamp: 2025-01-17 09:45:00 +08:00
// Reason: 为DXT上传组件创建样式文件，提供美观的拖拽上传界面
// Principle_Applied: KISS - 保持样式简洁明了，用户体验优先
// Optimization: 响应式设计，支持不同屏幕尺寸
// Architectural_Note (AR): 遵循项目样式规范，保持一致性
// Documentation_Note (DW): 提供清晰的视觉层次和交互反馈
// }} */

.dxt-upload-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: #fff;
}

.dxt-upload-dragger {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;
    padding: 32px 24px;
}

.dxt-upload-dragger:hover {
    border-color: #1890ff;
    background: #f0f8ff;
}

.dxt-upload-dragger.ant-upload-drag-hover {
    border-color: #40a9ff;
    background: #e6f7ff;
}

.dxt-upload-dragger .ant-upload-drag-icon {
    margin-bottom: 16px;
}

.dxt-upload-dragger .ant-upload-drag-icon .anticon {
    font-size: 48px;
    color: #1890ff;
}

.dxt-upload-dragger .ant-upload-text {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
}

.dxt-upload-dragger .ant-upload-hint {
    font-size: 14px;
    color: #999;
    line-height: 1.5;
}

.dxt-upload-dragger .ant-upload-hint .ant-tag {
    margin: 2px 4px;
    border-radius: 4px;
    font-size: 12px;
}

/* 上传进度样式 */
.upload-progress {
    padding: 16px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
}

.upload-progress .ant-progress {
    margin: 8px 0;
}

.upload-progress .ant-typography {
    margin-bottom: 4px;
}

/* 验证结果样式 */
.ant-alert {
    border-radius: 6px;
}

.ant-alert .ant-space {
    width: 100%;
}

.ant-alert .ant-typography {
    font-size: 13px;
    line-height: 1.4;
}

/* 按钮样式 */
.ant-btn {
    border-radius: 6px;
    height: 40px;
    padding: 0 24px;
    font-weight: 500;
}

/* 禁用状态样式 */
.dxt-upload-dragger.ant-upload-disabled {
    border-color: #d9d9d9;
    background: #f5f5f5;
    cursor: not-allowed;
}

.dxt-upload-dragger.ant-upload-disabled .ant-upload-drag-icon .anticon {
    color: #ccc;
}

.dxt-upload-dragger.ant-upload-disabled .ant-upload-text,
.dxt-upload-dragger.ant-upload-disabled .ant-upload-hint {
    color: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dxt-upload-card {
        margin: 0 16px 24px;
    }
    
    .dxt-upload-dragger {
        padding: 24px 16px;
    }
    
    .dxt-upload-dragger .ant-upload-drag-icon .anticon {
        font-size: 40px;
    }
    
    .dxt-upload-dragger .ant-upload-text {
        font-size: 14px;
    }
    
    .dxt-upload-dragger .ant-upload-hint {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .dxt-upload-dragger {
        padding: 16px 12px;
    }
    
    .dxt-upload-dragger .ant-upload-drag-icon .anticon {
        font-size: 36px;
    }
    
    .ant-btn {
        width: 100%;
    }
}

/* 动画效果 */
.upload-progress {
    animation: fadeIn 0.3s ease-in;
}

.ant-alert {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 拖拽激活状态 */
.dxt-upload-dragger.ant-upload-drag-hover {
    border-color: #40a9ff;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.dxt-upload-dragger.ant-upload-drag-hover .ant-upload-drag-icon .anticon {
    color: #40a9ff;
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* 成功状态样式 */
.ant-alert-success {
    border-color: #b7eb8f;
    background-color: #f6ffed;
}

/* 错误状态样式 */
.ant-alert-error {
    border-color: #ffccc7;
    background-color: #fff2f0;
}

/* 信息提示样式 */
.ant-alert-info {
    border-color: #91d5ff;
    background-color: #e6f7ff;
}

.ant-alert-info ul {
    margin-top: 8px;
}

.ant-alert-info li {
    margin-bottom: 4px;
    color: #666;
    font-size: 13px;
}

/* 卡片标题样式 */
.dxt-upload-card .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

/* 进度条自定义样式 */
.ant-progress-line {
    margin: 8px 0;
}

.ant-progress-text {
    font-weight: 500;
}

/* Tag样式优化 */
.ant-tag {
    border-radius: 12px;
    font-size: 11px;
    padding: 2px 8px;
    margin: 2px;
} 