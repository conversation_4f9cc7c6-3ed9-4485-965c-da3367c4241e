# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_data_files, collect_submodules
from PyInstaller.building.build_main import Analysis, PYZ, EXE, COLLECT
from PyInstaller.building.datastruct import TOC
from pathlib import Path
import sys
import os
from pathlib import Path
from PyInstaller.building.datastruct import TOC  # 导入TOC类

def collect_backend_modules(subdirs):
    """收集backend目录下指定子目录的所有模块"""
    modules = []
    backend_path = Path("backend")
    
    for subdir in subdirs:
        subdir_path = backend_path / subdir
        if subdir_path.exists():
            for py_file in subdir_path.rglob("*.py"):
                if py_file.name == "__init__.py":
                    # 对于__init__.py文件，使用目录名
                    rel_path = py_file.parent.relative_to(Path("."))
                else:
                    # 对于普通py文件，去掉.py扩展名
                    rel_path = py_file.with_suffix("").relative_to(Path("."))
                
                module_name = str(rel_path).replace(os.sep, ".")
                modules.append(module_name)
    
    return modules

block_cipher = None

# 获取操作系统信息
is_windows = sys.platform.startswith('win')
is_mac = sys.platform.startswith('darwin')
is_linux = sys.platform.startswith('linux')

# 设置图标路径 - 统一使用 app_icon.png
icon_file = 'resources/app_icon.png'

def complete_source_protection_filter(datas_list):
    """
    完全源码保护过滤器
    排除所有插件源码和deer-flow源码，只保留必要的配置文件
    """
    filtered_datas = []
    
    for item in datas_list:
        # PyInstaller的datas可能是2元组(src, dest)或3元组(src, dest, type)
        if len(item) == 2:
            src, dest = item
            data_type = None
        elif len(item) == 3:
            src, dest, data_type = item
        else:
            # 如果格式不符合预期，直接保留
            filtered_datas.append(item)
            continue
            
        should_include = True
        
        # 标准化路径，使用正斜杠
        normalized_src = src.replace('\\', '/')
        
        # 排除所有插件Python源码（保留manifest.json和__init__.py）
        if 'backend/plugins/' in normalized_src or normalized_src.startswith('backend/plugins/'):
            if normalized_src.endswith('.py') and not normalized_src.endswith('__init__.py'):
                print(f"[PROTECT] 保护插件源码: {src}")
                should_include = False
            elif normalized_src.endswith('manifest.json'):
                print(f"[KEEP] 保留插件配置: {src}")
                should_include = True
            elif normalized_src.endswith('__init__.py'):
                print(f"[KEEP] 保留模块初始化: {src}")
                should_include = True
            elif normalized_src.endswith(('.backup', '.pyc', '__pycache__')):
                print(f"[EXCLUDE] 排除插件缓存/备份文件: {src}")
                should_include = False
            else:
                # 其他文件（如README.md等）也排除
                print(f"[EXCLUDE] 排除插件文件: {src}")
                should_include = False
        
        # 排除backend/tools目录的Python源码（保护工具源码）
        elif 'backend/tools/' in normalized_src or normalized_src.startswith('backend/tools/'):
            if normalized_src.endswith('.py') and not normalized_src.endswith('__init__.py'):
                print(f"[PROTECT] 保护工具源码: {src}")
                should_include = False
            elif normalized_src.endswith('__init__.py'):
                print(f"[KEEP] 保留工具模块初始化: {src}")
                should_include = True
            elif normalized_src.endswith(('.json', '.yaml', '.yml', '.txt')):
                print(f"[KEEP] 保留工具配置文件: {src}")
                should_include = True
            else:
                # 其他文件（如README.md等）也排除
                print(f"[EXCLUDE] 排除工具文件: {src}")
                should_include = False
        
        # 排除deer-flow源码目录（为独立打包做准备）
        elif 'deer-flow/src' in normalized_src or normalized_src.startswith('deer-flow/src'):
            print(f"[PROTECT] 保护deer-flow源码: {src}")
            should_include = False
        
        # 排除deer-flow配置文件（独立打包后不需要）
        elif normalized_src.startswith('deer-flow/') and normalized_src.endswith(('.py', '.yaml', '.json')):
            print(f"[EXCLUDE] 排除deer-flow配置: {src}")
            should_include = False
        
        if should_include:
            # 保持原始格式
            if data_type is not None:
                filtered_datas.append((src, dest, data_type))
            else:
                filtered_datas.append((src, dest))
    
    return filtered_datas

# 收集所有必要的数据文件
datas = [
    ('view/build', 'view/build'),  # 前端构建文件
    # 注意：backend/tools 已移至 hiddenimports，代码将被隐藏
    # 注意：插件Python代码通过hiddenimports隐式导入，只保留必要的配置文件
    # 手动添加每个插件的manifest.json（如果插件系统需要读取的话）
    ('backend/plugins/deer_flow/manifest.json', 'backend/plugins/deer_flow/'),
    ('backend/plugins/ai_programming_agent/manifest.json', 'backend/plugins/ai_programming_agent/'),
    ('backend/plugins/data_analysis_agent/manifest.json', 'backend/plugins/data_analysis_agent/'),
    ('backend/plugins/openmanus_agent/manifest.json', 'backend/plugins/openmanus_agent/'),
    ('backend/plugins/research_agent/manifest.json', 'backend/plugins/research_agent/'),
    # 添加SVG模板和LangGraph配置目录
    ('resources/svg_templates', 'resources/svg_templates'),  # SVG模板文件
    # LangGraph配置文件（只包含YAML配置文件，Python文件通过hiddenimports隐藏）
    ('backend/langgraph_enhancement/config/teams.yaml', 'backend/langgraph_enhancement/config/'),
    ('backend/langgraph_enhancement/config/agents.yaml', 'backend/langgraph_enhancement/config/'),
    ('backend/langgraph_enhancement/config/context_config.yaml', 'backend/langgraph_enhancement/config/'),
    # 注意：DeerFlow插件的模板文件已通过嵌入式模板系统隐式包含
]

# 添加deer-flow源码支持集成模式
"""deer_flow_src = 'deer-flow/src'
if os.path.exists(deer_flow_src):
    print(f"找到deer-flow源码目录: {deer_flow_src}")
    datas.append((deer_flow_src, 'deer-flow/src'))
    
    # 添加deer-flow相关配置文件
    deer_flow_configs = [
        'deer-flow/conf.yaml',
        'deer-flow/langgraph.json',
        'deer-flow/server.py',
        'deer-flow/main.py'
    ]
    
    for config_file in deer_flow_configs:
        if os.path.exists(config_file):
            print(f"添加deer-flow配置文件: {config_file}")
            # 将文件添加到对应的deer-flow目录结构中
            datas.append((config_file, os.path.dirname(config_file) if os.path.dirname(config_file) else '.'))
        else:
            print(f"警告：deer-flow配置文件不存在: {config_file}")
else:
    print(f"警告：找不到deer-flow源码目录: {deer_flow_src}")
    print("DeerFlow插件将只能在子进程模式下运行（需要单独的deer-flow安装）")
"""
# 获取项目根目录的绝对路径（使用当前工作目录）
ROOT_DIR = os.getcwd()

# 使用系统级的Python包路径
import site
site_packages_paths = site.getsitepackages()

# 查找 qwen.tiktoken 文件
tiktoken_file = None
for site_path in site_packages_paths:
    potential_tiktoken = os.path.join(site_path, 'qwen_agent', 'utils', 'qwen.tiktoken')
    if os.path.exists(potential_tiktoken):
        tiktoken_file = potential_tiktoken
        break

# 添加tiktoken文件（关键文件，必须存在）
if tiktoken_file:
    print(f"找到tiktoken文件: {tiktoken_file}")
    # 直接添加到qwen_agent/utils目录
    datas.append((tiktoken_file, 'qwen_agent/utils'))
else:
    print(f"警告：找不到tiktoken文件，尝试使用绝对路径")
    # 使用我们之前找到的确切路径
    tiktoken_file = '/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/qwen_agent/utils/qwen.tiktoken'
    if os.path.exists(tiktoken_file):
        print(f"使用绝对路径找到tiktoken文件: {tiktoken_file}")
        datas.append((tiktoken_file, 'qwen_agent/utils'))
    else:
        print(f"错误：无法找到tiktoken文件: {tiktoken_file}")

# 把resources下的marketplace_plugins.json加入打包列表
marketplace_plugins_file = 'resources/marketplace_plugins.json'
if os.path.exists(marketplace_plugins_file):
    print(f"找到marketplace插件配置文件: {marketplace_plugins_file}")
    datas.append((marketplace_plugins_file, 'resources'))
else:
    print(f"警告：找不到marketplace插件配置文件: {marketplace_plugins_file}")
role_config = 'resources/roles_config.json'
if os.path.exists(role_config):
    print(f"找到marketplace插件配置文件: {role_config}")
    datas.append((role_config, 'resources'))
else:
    print(f"警告：找不到role_config插件配置文件: {role_config}")

# 收集必要的隐式导入模块
hiddenimports = [
    'uvicorn.logging',
    'uvicorn.lifespan',
    'uvicorn.lifespan.on',
    'uvicorn.lifespan.off',
    'uvicorn.protocols',
    'uvicorn.protocols.http',
    'uvicorn.protocols.http.auto',
    'uvicorn.protocols.websockets',
    'uvicorn.protocols.websockets.auto',
    'fastapi',
    'email.mime.image',
    'email.mime.audio',
    'email.mime.message',
    # DeerFlow集成模式必需依赖
    'readabilipy',
    'socksio',
    'markdownify',
    'sse_starlette',
    'pandas',
    'numpy',
    'yfinance',
    'litellm',
    'json_repair',
    'jinja2',
    'duckduckgo_search',
    'inquirerpy',
    'arxiv',
    'langsmith',
    'tenacity',
    'jsonpatch',
    'jsonpointer',
    'PyYAML',
    'packaging',
    'typing_extensions',
    'pydantic',
    'pydantic_core',
    'pydantic_settings',
    'httpx',
    'httpx_sse',
    'requests',
    'beautifulsoup4',
    'html5lib',
    'lxml',
    'six',
    'starlette',
    'openai',
    'tiktoken',
    'distro',
    'jiter',
    'tqdm',
    'regex',
    'aiohttp',
    'multidict',
    'yarl',
    'frozenlist',
    'aiosignal',
    'attrs',
    'charset_normalizer',
    'idna',
    'certifi',
    'urllib3',
    'SQLAlchemy',
    'greenlet',
    'orjson',
    'xxhash',
    # SVG和PPT生成相关依赖
    'python-pptx',
    'lxml',
    'Pillow',
]

# 自动收集langchain相关的所有子模块
print("\n正在收集langchain相关模块...")
langchain_modules = []

# 定义要收集的langchain包
langchain_packages = [
    'langchain',
    'langchain_openai', 
    'langchain_core',
    'langchain_community',
    'langgraph',
    'langchain_text_splitters'
]

for package in langchain_packages:
    try:
        modules = collect_submodules(package)
        langchain_modules.extend(modules)
        print(f"收集到 {len(modules)} 个 {package} 子模块")
    except Exception as e:
        print(f"收集 {package} 子模块时出错: {e}")

print(f"总共收集到 {len(langchain_modules)} 个langchain相关模块")

# 收集langchain相关的数据文件
langchain_datas = []
for package in langchain_packages:
    try:
        data_files = collect_data_files(package)
        langchain_datas.extend(data_files)
        print(f"收集到 {len(data_files)} 个 {package} 数据文件")
    except Exception as e:
        print(f"收集 {package} 数据文件时出错: {e}")

# 添加langchain数据文件到datas
datas.extend(langchain_datas)
print(f"总共收集到 {len(langchain_datas)} 个langchain数据文件")

# 添加langchain模块到hiddenimports
hiddenimports.extend(langchain_modules)

# 额外添加一些可能被遗漏的核心模块
additional_langchain_modules = [
    'readabilipy',
    'socksio', 
    'markdownify',
    'sse_starlette',
    'pandas',
    'numpy',
    'yfinance',
    'litellm',
    'json_repair',
    'jinja2',
    'duckduckgo_search',
    'inquirerpy',
    'arxiv',
    'langsmith',
    'tenacity',
    'jsonpatch',
    'jsonpointer',
    'PyYAML',
    'packaging',
    'typing_extensions',
    'pydantic',
    'pydantic_core',
    'pydantic_settings',
    'httpx',
    'httpx_sse',
    'requests',
    'beautifulsoup4',
    'html5lib',
    'lxml',
    'six',
    'starlette',
    'openai',
    'tiktoken',
    'distro',
    'jiter',
    'tqdm',
    'regex',
    'aiohttp',
    'multidict',
    'yarl',
    'frozenlist',
    'aiosignal',
    'attrs',
    'charset_normalizer',
    'idna',
    'certifi',
    'urllib3',
    'SQLAlchemy',
    'greenlet',
    'orjson',
    'xxhash',
    # SVG和PPT生成相关依赖
    'python-pptx',
    'lxml',
    'Pillow',
]

hiddenimports.extend(additional_langchain_modules)

# 手动添加指定的核心模块（用户要求）
core_backend_modules = [
    'backend.tools.pptx_generator',
    'backend.tools.svg_to_ppt_service', 
    'backend.utils.prompts',
    'backend.utils.svg_generator',
]

print("添加指定的核心backend模块:")
for module in core_backend_modules:
    print(f"  + {module}")
hiddenimports.extend(core_backend_modules)

# 自动收集backend/tools和backend/utils下的所有模块
print("\n开始自动收集backend模块...")
auto_collected_modules = collect_backend_modules(['tools', 'utils'])
if auto_collected_modules:
    # 去重：移除已经手动添加的模块
    new_modules = [m for m in auto_collected_modules if m not in core_backend_modules]
    if new_modules:
        print(f"自动收集到额外的 {len(new_modules)} 个模块:")
        for module in new_modules[:10]:  # 只显示前10个
            print(f"  + {module}")
        if len(new_modules) > 10:
            print(f"  ... 以及其他 {len(new_modules) - 10} 个模块")
        hiddenimports.extend(new_modules)
    else:
        print("所有模块都已手动添加，无需额外收集")
else:
    print("自动收集未找到任何模块")
    # 作为备份，手动添加已知的核心模块
    backup_modules = [
        'backend.tools',
        'backend.utils'
    ]
    print("添加备份模块:")
    for module in backup_modules:
        print(f"  + {module}")
    hiddenimports.extend(backup_modules)

# 添加额外的隐式导入
hiddenimports.extend(collect_submodules('backend'))

# 手动添加所有插件模块到隐式导入（确保插件代码被隐藏）
plugin_modules = [
    #tools
    'backend.tools',
    # DeerFlow插件
    'backend.plugins.deer_flow',
    'backend.plugins.deer_flow.plugin',
    'backend.plugins.deer_flow.src_import_helper',
    'backend.plugins.deer_flow.embedded_templates',  # 嵌入式模板系统
    'backend.plugins.deer_flow.generators',
    'backend.plugins.deer_flow.generators.podcast_generator',
    'backend.plugins.deer_flow.generators.ppt_generator', 
    'backend.plugins.deer_flow.generators.prose_generator',
    'backend.plugins.deer_flow.fixed_app',
    'backend.plugins.deer_flow.fixed_loader',
    'backend.plugins.deer_flow.patched_server',
    'backend.plugins.deer_flow.workflow',
    
    # AI编程助手插件
    'backend.plugins.ai_programming_agent',
    'backend.plugins.ai_programming_agent.plugin',
    
    # 数据分析助手插件
    'backend.plugins.data_analysis_agent',
    'backend.plugins.data_analysis_agent.plugin',
    
    # OpenManus代理插件
    'backend.plugins.openmanus_agent',
    'backend.plugins.openmanus_agent.plugin',
    'backend.plugins.openmanus_agent.adapters',
    'backend.plugins.openmanus_agent.adapters.base_adapter',
    'backend.plugins.openmanus_agent.adapters.manus_adapter',
    
    # 研究助手插件
    'backend.plugins.research_agent',
    'backend.plugins.research_agent.plugin',
    
    # LangGraph增强模块（Python文件隐藏打包）
    'backend.langgraph_enhancement',
    'backend.langgraph_enhancement.config',
    'backend.langgraph_enhancement.config.config_manager',
]

# 添加DeerFlow插件内src目录的所有模块
deer_flow_src_modules = []

# 手动列出所有重要的DeerFlow src模块（确保完整性）
deer_flow_src_manual_modules = [
    # 核心模块
    'backend.plugins.deer_flow.src',
    'backend.plugins.deer_flow.src.workflow',
    
    # graph 模块
    'backend.plugins.deer_flow.src.graph',
    'backend.plugins.deer_flow.src.graph.builder',
    'backend.plugins.deer_flow.src.graph.nodes',
    'backend.plugins.deer_flow.src.graph.types',
    
    # prompts 模块
    'backend.plugins.deer_flow.src.prompts',
    'backend.plugins.deer_flow.src.prompts.planner_model',
    'backend.plugins.deer_flow.src.prompts.template',
    
    # config 模块
    'backend.plugins.deer_flow.src.config',
    'backend.plugins.deer_flow.src.config.config',
    'backend.plugins.deer_flow.src.config.agents',
    'backend.plugins.deer_flow.src.config.configuration',
    'backend.plugins.deer_flow.src.config.report_style',
    'backend.plugins.deer_flow.src.config.tools',
    
    # llms 模块
    'backend.plugins.deer_flow.src.llms',
    'backend.plugins.deer_flow.src.llms.llm',
    
    # agents 模块
    'backend.plugins.deer_flow.src.agents',
    
    # tools 模块
    'backend.plugins.deer_flow.src.tools',
    'backend.plugins.deer_flow.src.tools.search',
    'backend.plugins.deer_flow.src.tools.crawl',
    'backend.plugins.deer_flow.src.tools.retriever',
    'backend.plugins.deer_flow.src.tools.decorators',
    'backend.plugins.deer_flow.src.tools.tts',
    'backend.plugins.deer_flow.src.tools.tavily_search',
    'backend.plugins.deer_flow.src.tools.tavily_search.tavily_search_api_wrapper',
    'backend.plugins.deer_flow.src.tools.tavily_search.tavily_search_results_with_images',
    
    # utils 模块
    'backend.plugins.deer_flow.src.utils',
    'backend.plugins.deer_flow.src.utils.json_utils',
    
    # 专门的内容生成模块
    'backend.plugins.deer_flow.src.podcast',
    'backend.plugins.deer_flow.src.podcast.graph',
    'backend.plugins.deer_flow.src.podcast.graph.builder',
    'backend.plugins.deer_flow.src.podcast.graph.state',
    'backend.plugins.deer_flow.src.podcast.graph.script_writer_node',
    'backend.plugins.deer_flow.src.podcast.graph.tts_node',
    'backend.plugins.deer_flow.src.podcast.graph.audio_mixer_node',
    
    'backend.plugins.deer_flow.src.ppt',
    'backend.plugins.deer_flow.src.ppt.graph',
    'backend.plugins.deer_flow.src.ppt.graph.builder',
    'backend.plugins.deer_flow.src.ppt.graph.state',
    'backend.plugins.deer_flow.src.ppt.graph.ppt_composer_node',
    'backend.plugins.deer_flow.src.ppt.graph.ppt_generator_node',
    
    'backend.plugins.deer_flow.src.prose',
    'backend.plugins.deer_flow.src.prose.graph',
    'backend.plugins.deer_flow.src.prose.graph.builder',
    'backend.plugins.deer_flow.src.prose.graph.state',
    'backend.plugins.deer_flow.src.prose.graph.prose_continue_node',
    'backend.plugins.deer_flow.src.prose.graph.prose_fix_node',
    'backend.plugins.deer_flow.src.prose.graph.prose_improve_node',
    'backend.plugins.deer_flow.src.prose.graph.prose_longer_node',
    'backend.plugins.deer_flow.src.prose.graph.prose_shorter_node',
    'backend.plugins.deer_flow.src.prose.graph.prose_zap_node',
    
    'backend.plugins.deer_flow.src.prompt_enhancer',
    'backend.plugins.deer_flow.src.prompt_enhancer.graph',
    'backend.plugins.deer_flow.src.prompt_enhancer.graph.builder',
    'backend.plugins.deer_flow.src.prompt_enhancer.graph.state',
    'backend.plugins.deer_flow.src.prompt_enhancer.graph.enhancer_node',
    
    # rag 模块
    'backend.plugins.deer_flow.src.rag',
    'backend.plugins.deer_flow.src.rag.builder',
    'backend.plugins.deer_flow.src.rag.retriever',
    'backend.plugins.deer_flow.src.rag.ragflow',
    
    # crawler 模块
    'backend.plugins.deer_flow.src.crawler',
    
    # server 模块
    'backend.plugins.deer_flow.src.server',
    'backend.plugins.deer_flow.src.server.app',
    'backend.plugins.deer_flow.src.server.chat_request',
    'backend.plugins.deer_flow.src.server.rag_request',
    'backend.plugins.deer_flow.src.server.mcp_request',
    'backend.plugins.deer_flow.src.server.mcp_utils',
]

deer_flow_src_modules.extend(deer_flow_src_manual_modules)

# 尝试自动收集作为补充
try:
    auto_collected = collect_submodules('backend.plugins.deer_flow.src')
    deer_flow_src_modules.extend(auto_collected)
    print(f"自动收集到DeerFlow插件src模块: {len(auto_collected)} 个")
except Exception as e:
    print(f"警告：自动收集DeerFlow插件src模块失败: {e}")

plugin_modules.extend(deer_flow_src_modules)
print(f"总共添加DeerFlow插件src模块: {len(deer_flow_src_modules)} 个")

hiddenimports.extend(plugin_modules)

# 使用collect_submodules作为补充，确保插件模块没有遗漏
try:
    collected_plugin_modules = collect_submodules('backend.plugins')
    # 过滤掉已经通过自定义函数收集的模块，避免重复
    plugin_only_modules = [m for m in collected_plugin_modules if not (m.startswith('backend.tools') or m.startswith('backend.utils'))]
    hiddenimports.extend(plugin_only_modules)
    print(f"自动收集到 {len(plugin_only_modules)} 个插件模块（已过滤tools/utils）")
except Exception as e:
    print(f"警告：自动收集插件模块失败: {e}")

# 注意：backend/tools和backend/utils模块已通过上面的collect_backend_modules函数收集
# 这里不再重复收集，避免冗余

# 添加macOS特定的模块以修复WebKit崩溃问题
if is_mac:
    macos_specific_modules = [
        'objc',
        'Foundation',
        'WebKit',
        'Cocoa',
        'AppKit',
        'CoreFoundation',
        'objc._objc',
        'objc._convenience',
        'PyObjCTools',
        'PyObjCTools.AppHelper',
        'PyObjCTools.Debugging',
    ]
    hiddenimports.extend(macos_specific_modules)
    print(f"添加了 {len(macos_specific_modules)} 个macOS特定模块")

# 移除重复项（保持原有功能）
print(f"\n去重前总模块数: {len(hiddenimports)}")
hiddenimports = list(set(hiddenimports))
print(f"去重后总模块数: {len(hiddenimports)}")
print("隐式导入模块收集完成\n")

a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],  # 这里可以添加要排除的Python模块
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 应用完全源码保护过滤器
print("=" * 60)
print("应用完全源码保护过滤器...")
print("=" * 60)
a.datas = complete_source_protection_filter(a.datas)
print("=" * 60)
print("源码保护过滤完成")
print("=" * 60)

# 定义要排除的文件
excluded_binaries = [
    "config.json",
    "mcp.json"
]

excluded_datas = [
    # 可以在这里添加要排除的特定数据文件
]

# 排除特定二进制文件
if excluded_binaries:
    a.binaries = [x for x in a.binaries if x[0] not in excluded_binaries]

# 排除特定数据文件
if excluded_datas:
    a.datas = [x for x in a.datas if x[0] not in excluded_datas]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Jimu Chat',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 设置为False可以在Windows上隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=True,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_file if os.path.exists(icon_file) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Jimu Chat',
)

# macOS特定打包配置
if is_mac:
    app = BUNDLE(
        coll,
        name='Jimu Chat.app',
        icon=icon_file if os.path.exists(icon_file) else None,
        bundle_identifier='com.aichat.desktop',
        info_plist={
            'NSPrincipalClass': 'NSApplication',
            'NSHighResolutionCapable': 'True',
            'LSApplicationCategoryType': 'public.app-category.developer-tools',
            'CFBundleShortVersionString': '1.0.0',
            # 添加WebKit和网络权限
            'NSAppTransportSecurity': {
                'NSAllowsArbitraryLoads': True,
                'NSAllowsLocalNetworking': True
            },
            # 禁用WebKit认证挑战处理
            'NSURLSessionConfiguration': {
                'HTTPShouldUsePipelining': False
            },
            # 添加必要的使用权限描述
            'NSCameraUsageDescription': 'This app uses camera for enhanced user experience.',
            'NSMicrophoneUsageDescription': 'This app uses microphone for voice input.',
        },
    )