<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变：从浅背景色到容器背景色，提供柔和的视觉深度 -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- 主色渐变：用于党政宣传的权威感和深度，符合统一蓝色系配色 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(135)">
      <stop offset="0%" stop-color="#1E3A8A"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- 强调色渐变：用于视觉引导和活泼感，同时保持整体庄重氛围 -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(45)">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E3A8A"/>
    </linearGradient>

    <!-- 科技感高亮透明渐变 - 强调色：为几何图形提供现代感和透明效果 -->
    <linearGradient id="techGradientBlue" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(90)">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.1"/>
      <stop offset="50%" stop-color="#3B82F6" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.1"/>
    </linearGradient>

    <!-- 科技感高亮透明渐变 - 主色：为背景装饰提供深邃感和透明效果 -->
    <linearGradient id="techGradientDarkBlue" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(90)">
      <stop offset="0%" stop-color="#1E3A8A" stop-opacity="0.1"/>
      <stop offset="50%" stop-color="#1E3A8A" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#1E3A8A" stop-opacity="0.1"/>
    </linearGradient>

    <!-- 装饰性引用开头符号，采用几何路径和渐变填充，与政务风格协调 -->
    <symbol id="quoteOpen" viewBox="0 0 100 100">
      <path d="M 10 70 L 40 70 C 40 40 20 20 10 0 L 10 20 C 20 30 30 40 30 50 L 10 50 Z M 60 70 L 90 70 C 90 40 70 20 60 0 L 60 20 C 70 30 80 40 80 50 L 60 50 Z" fill="#1E3A8A" fill-opacity="0.1"/>
      <path d="M 10 70 L 40 70 C 40 40 20 20 10 0 L 10 20 C 20 30 30 40 30 50 L 10 50 Z M 60 70 L 90 70 C 90 40 70 20 60 0 L 60 20 C 70 30 80 40 80 50 L 60 50 Z" fill="url(#primaryGradient)"/>
    </symbol>
    <!-- 装饰性引用结束符号，与开头符号相呼应 -->
    <symbol id="quoteClose" viewBox="0 0 100 100">
      <path d="M 10 0 L 40 0 C 40 30 20 50 10 70 L 10 50 C 20 40 30 30 30 20 L 10 20 Z M 60 0 L 90 0 C 90 30 70 50 60 70 L 60 50 C 70 40 80 30 80 20 L 60 20 Z" fill="#1E3A8A" fill-opacity="0.1"/>
      <path d="M 10 0 L 40 0 C 40 30 20 50 10 70 L 10 50 C 20 40 30 30 30 20 L 10 20 Z M 60 0 L 90 0 C 90 30 70 50 60 70 L 60 50 C 70 40 80 30 80 20 L 60 20 Z" fill="url(#primaryGradient)"/>
    </symbol>
  </defs>

  <style>
    /* 基础字体和颜色定义，确保全局一致性 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* 字体大小系统 */
    .text-hero-title { font-size: 72px; }
    .text-main-title { font-size: 56px; }
    .text-section-title { font-size: 36px; }
    .text-content-title { font-size: 28px; }
    .text-body { font-size: 22px; }
    .text-small { font-size: 16px; }
    .text-caption { font-size: 14px; }

    /* 字体粗细系统 */
    .font-normal { font-weight: 400; }
    .font-bold { font-weight: 700; }
    .font-semibold { font-weight: 600; }
    .font-light { font-weight: 300; }

    /* 填充颜色类 */
    .fill-primary-color { fill: #1E3A8A; }
    .fill-secondary-color { fill: #1E40AF; }
    .fill-accent-color { fill: #3B82F6; }
    .fill-text-primary { fill: #1E293B; }
    .fill-text-secondary { fill: #64748B; }
    .fill-text-light { fill: #94A3B8; }

    /* 描边颜色类 */
    .stroke-primary-color { stroke: #1E3A8A; }
    .stroke-accent-color { stroke: #3B82F6; }

    /* 引用内容样式：突出显示，采用大字体和粗体中文 */
    .quote-content {
      font-size: 48px;
      font-weight: 700;
      fill: #1E293B;
      font-family: 'Microsoft YaHei', sans-serif;
      text-anchor: middle;
      line-height: 1.4;
    }

    /* 引用来源样式：辅助色，区分于主体引用，保持正式感 */
    .quote-source {
      font-size: 28px;
      font-weight: 400;
      fill: #1E40AF;
      font-family: 'Source Han Sans CN', sans-serif;
      text-anchor: middle;
    }

    /* 引用来源中的英文点缀：小号细体英文，作为辅助信息 */
    .quote-source-en {
      font-size: 20px;
      font-weight: 300;
      fill: #64748B;
      font-family: 'Times New Roman', serif;
      text-anchor: middle;
    }

    /* 标题样式：主色大字体，庄重醒目 */
    .title-text {
      fill: #1E3A8A;
      font-family: 'Microsoft YaHei', sans-serif;
      font-weight: 700;
      font-size: 56px;
      text-anchor: middle;
    }

    /* 副标题样式：辅助文本色，提供补充说明 */
    .subtitle-text {
      fill: #64748B;
      font-family: 'Source Han Sans CN', sans-serif;
      font-weight: 400;
      font-size: 28px;
      text-anchor: middle;
    }

    /* 页码样式：浅色小字体，置于页面角落 */
    .page-number-text {
      fill: #94A3B8;
      font-family: 'Segoe UI', sans-serif;
      font-weight: 400;
      font-size: 20px;
      text-anchor: end;
    }

    /* 装饰线样式：使用科技感渐变，提供现代感和引导性 */
    .decorative-line {
      stroke: url(#techGradientBlue);
      stroke-width: 4;
      stroke-linecap: round;
    }

    /* 装饰形状样式：使用科技感渐变，作为背景视觉元素 */
    .decorative-shape-fill {
      fill: url(#techGradientDarkBlue);
      opacity: 0.7;
    }
  </style>

  <!-- 背景层：填充整个画布 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- 装饰元素层：包含几何图形、线条和页码强调，模拟Bento Grid的模块化布局 -->
  <g class="decorative-elements">
    <!-- 左上角抽象形状，增加视觉层次感 -->
    <path d="M0 0 H400 V200 L200 0 Z" fill="#1E3A8A" fill-opacity="0.05"/>
    <path d="M0 0 L400 0 L200 200 L0 200 Z" class="decorative-shape-fill"/>

    <!-- 右下角抽象形状，与左上角呼应 -->
    <path d="M1920 1080 H1520 V880 L1720 1080 Z" fill="#3B82F6" fill-opacity="0.05"/>
    <path d="M1920 1080 L1520 1080 L1720 880 L1920 880 Z" class="decorative-shape-fill"/>

    <!-- 页面中央分割线，提供结构感 -->
    <line x1="160" y1="540" x2="1760" y2="540" class="decorative-line" stroke-width="2" stroke-dasharray="10 10"/>

    <!-- 侧边垂直装饰线，增强页面边界感 -->
    <line x1="80" y1="100" x2="80" y2="980" class="decorative-line" stroke-width="2" stroke-opacity="0.3"/>
    <line x1="1840" y1="100" x2="1840" y2="980" class="decorative-line" stroke-width="2" stroke-opacity="0.3"/>

    <!-- 四角点缀方块，细节化处理 -->
    <rect x="80" y="60" width="40" height="40" fill="#1E40AF" fill-opacity="0.1"/>
    <rect x="1800" y="60" width="40" height="40" fill="#1E40AF" fill-opacity="0.1"/>
    <rect x="80" y="980" width="40" height="40" fill="#1E40AF" fill-opacity="0.1"/>
    <rect x="1800" y="980" width="40" height="40" fill="#1E40AF" fill-opacity="0.1"/>

    <!-- 页面序号的超大视觉强调，半透明处理，既突出又不过于抢眼 -->
    <text x="1750" y="250" class="font-primary font-bold" font-size="200" fill="#1E3A8A" fill-opacity="0.05" text-anchor="middle">9</text>
  </g>

  <!-- 头部区域：包含Logo、主标题和副标题 -->
  <g class="header">
    <!-- Logo 占位符，请替换为实际Logo图片链接 -->
    <image href="{logo_url}" x="80" y="60" width="160" height="80" preserveAspectRatio="xMidYMid meet"/>
    <!-- 主标题占位符，请替换为实际标题内容 -->
    <text x="960" y="100" class="title-text">{title}</text>
    <!-- 副标题占位符，请替换为实际副标题内容 -->
    <text x="960" y="145" class="subtitle-text">{subtitle}</text>
  </g>

  <!-- 核心引用内容区域：突出显示重要引用，情感共鸣 -->
  <g class="quote-section">
    <!-- 引用开头装饰符号，位于引用内容左上角 -->
    <use href="#quoteOpen" x="200" y="300" width="100" height="100"/>

    <!-- 引用正文内容，采用多行tspan确保行间距和布局，请将长文本拆分为以下三行 -->
    <text x="960" y="450" class="quote-content">
      <tspan x="960" dy="0">{content_line1}</tspan>
      <tspan x="960" dy="60">{content_line2}</tspan>
      <tspan x="960" dy="60">{content_line3}</tspan>
    </text>

    <!-- 引用结束装饰符号，位于引用内容右下角 -->
    <use href="#quoteClose" x="1620" y="600" width="100" height="100"/>

    <!-- 引用来源信息，包含作者和日期，中英文混用 -->
    <text x="960" y="800" class="quote-source">
      <tspan x="960" dy="0">{author}</tspan>
      <tspan x="960" dy="40" class="quote-source-en">{date} - Policy Interpretation Centre</tspan>
    </text>
  </g>

  <!-- 底部区域：包含页码信息 -->
  <g class="footer">
    <text x="1840" y="1020" class="page-number-text">页面 9/10</text>
  </g>
</svg>