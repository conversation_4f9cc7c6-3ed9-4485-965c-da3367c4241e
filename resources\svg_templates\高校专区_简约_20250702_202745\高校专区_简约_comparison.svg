<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 定义渐变色，用于背景、强调元素等 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#BAE6FD"/>
      <stop offset="100%" stop-color="#3B82F6"/>
    </linearGradient>
    <!-- 用于强调数字的高亮渐变，自身透明度渐变制造科技感 -->
    <linearGradient id="highlightNumberGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.6"/>
      <stop offset="50%" stop-color="#7DD3FC" stop-opacity="1"/>
      <stop offset="100%" stop-color="#3B82F6" stop-opacity="0.6"/>
    </linearGradient>
  </defs>

  <style>
    /* 基础样式定义 */
    .background { fill: #F8FAFC; }

    /* 字体族定义 */
    .fontPrimary { font-family: "Inter", "Helvetica", "Arial", sans-serif; }
    .fontSecondary { font-family: "SF Pro Display", system-ui, sans-serif; }
    .fontAccent { font-family: "Poppins", sans-serif; }

    /* 字体粗细定义 */
    .fontWeightNormal { font-weight: 400; }
    .fontWeightMedium { font-weight: 500; }
    .fontWeightSemibold { font-weight: 600; }
    .fontWeightBold { font-weight: 700; }

    /* 字体大小定义 */
    .fontSizeHeroTitle { font-size: 72px; }
    .fontSizeMainTitle { font-size: 56px; }
    .fontSizeSectionTitle { font-size: 36px; }
    .fontSizeContentTitle { font-size: 28px; }
    .fontSizeBodyText { font-size: 22px; }
    .fontSizeSmallText { font-size: 16px; }
    .fontSizeCaption { font-size: 14px; }

    /* 文本颜色定义 */
    .textColorPrimary { fill: #1E293B; }
    .textColorSecondary { fill: #64748B; }
    .textColorLight { fill: #94A3B8; }
    .textColorWhite { fill: #FFFFFF; }

    /* 填充颜色定义 */
    .fillPrimary { fill: #3B82F6; }
    .fillSecondary { fill: #7DD3FC; }
    .fillAccent { fill: #BAE6FD; }
    .fillBackground { fill: #F8FAFC; }
    .fillCardBackground { fill: #FFFFFF; }
    .fillContainerBackground { fill: #E0F2FE; } /* E0F2FE 是 container_background */

    /* 描边颜色定义 */
    .strokePrimary { stroke: #3B82F6; }
    .strokeAccent { stroke: #BAE6FD; }

    /* 卡片样式 */
    .card {
      fill: #FFFFFF; /* card_background */
      stroke: #BAE6FD; /* card_border */
      stroke-width: 1px;
    }

    /* 装饰元素样式 */
    .lineDivider {
      stroke: #BAE6FD;
      stroke-width: 1px;
    }
    .decorativeShapeFill {
      fill: url(#primaryGradient);
      opacity: 0.1; /* 保持简约，不抢眼 */
    }
    .decorativeLineStroke {
      stroke: #7DD3FC;
      stroke-width: 2px;
      stroke-linecap: round;
    }

    /* 文本对齐工具类 */
    .textAlignCenter { text-anchor: middle; }
    .textAlignLeft { text-anchor: start; }
    .textAlignRight { text-anchor: end; }

    /* 关键数字强调样式 */
    .keyHighlightNumber {
      font-size: 100px; /* 超大字体 */
      font-weight: 700;
      fill: url(#highlightNumberGradient); /* 使用高亮渐变 */
    }
    .keyHighlightText {
      font-size: 28px;
      font-weight: 600;
      fill: #1E293B;
    }
  </style>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="background"/>

  <!-- 顶部头部区域 -->
  <g id="header">
    <!-- Logo 占位符 -->
    <rect x="80" y="60" width="160" height="60" fill="#E0F2FE"/>
    <text x="160" y="98" class="fontPrimary fontSizeSmallText textColorSecondary textAlignCenter fontWeightSemibold">
      <tspan>{logo_url}</tspan>
    </text>

    <!-- 主标题 -->
    <text x="960" y="150" class="fontPrimary fontSizeMainTitle textColorPrimary textAlignCenter fontWeightBold">
      <tspan>{title}</tspan>
    </text>

    <!-- 副标题 -->
    <text x="960" y="200" class="fontPrimary fontSizeContentTitle textColorSecondary textAlignCenter fontWeightNormal">
      <tspan>{subtitle}</tspan>
    </text>
  </g>

  <!-- 对比分析区域 -->
  <g id="comparisonSection">
    <!-- 分隔线 -->
    <line x1="80" y1="260" x2="1840" y2="260" class="lineDivider"/>

    <!-- 左侧对比卡片 -->
    <rect x="80" y="290" width="800" height="400" rx="0" class="card"/>
    <text x="480" y="340" class="fontPrimary fontSizeContentTitle textColorPrimary textAlignCenter fontWeightSemibold">
      <tspan>传统方法：深入分析</tspan>
    </text>
    <text x="480" y="380" class="fontPrimary fontSizeBodyText textColorSecondary textAlignCenter fontWeightNormal">
      <tspan>Content Focus: {content}</tspan>
      <tspan x="480" dy="30">Description: 传统研究在学术界的</tspan>
      <tspan x="480" dy="30">应用和其经典理论基础。强调其</tspan>
      <tspan x="480" dy="30">在特定历史时期所做的贡献，和</tspan>
      <tspan x="480" dy="30">其在数据处理上的局限性。</tspan>
    </text>
    <!-- 图片/图标占位符 -->
    <rect x="120" y="550" width="100" height="100" fill="#BAE6FD" opacity="0.3"/>
    <text x="170" y="605" class="fontPrimary fontSizeSmallText textColorSecondary textAlignCenter">
      <tspan>{image_url}</tspan>
    </text>

    <!-- 右侧对比卡片 -->
    <rect x="1040" y="290" width="800" height="400" rx="0" class="card"/>
    <text x="1440" y="340" class="fontPrimary fontSizeContentTitle textColorPrimary textAlignCenter fontWeightSemibold">
      <tspan>创新范式：未来展望</tspan>
    </text>
    <text x="1440" y="380" class="fontPrimary fontSizeBodyText textColorSecondary textAlignCenter fontWeightNormal">
      <tspan>Content Focus: {content}</tspan>
      <tspan x="1440" dy="30">Description: 介绍创新研究范式的</tspan>
      <tspan x="1440" dy="30">核心理念和新兴技术。阐述其如何</tspan>
      <tspan x="1440" dy="30">解决传统难题，提供更高效和精准</tspan>
      <tspan x="1440" dy="30">的分析结果。展望未来发展趋势。</tspan>
    </text>
    <!-- 图片/图标占位符 -->
    <rect x="1080" y="550" width="100" height="100" fill="#BAE6FD" opacity="0.3"/>
    <text x="1130" y="605" class="fontPrimary fontSizeSmallText textColorSecondary textAlignCenter">
      <tspan>{image_url}</tspan>
    </text>
  </g>

  <!-- 核心差异点展示区域 -->
  <g id="differencesSection">
    <rect x="80" y="720" width="1760" height="180" class="fillContainerBackground" rx="0"/>
    <text x="960" y="760" class="fontPrimary fontSizeSectionTitle textColorPrimary textAlignCenter fontWeightBold">
      <tspan>核心差异点</tspan>
    </text>

    <!-- 差异点列表 -->
    <text x="360" y="820" class="fontPrimary fontSizeBodyText textColorPrimary textAlignLeft fontWeightSemibold">
      <tspan>1. 数据处理效率: </tspan>
      <tspan dx="10" class="textColorSecondary fontWeightNormal">创新范式提升30%</tspan>
    </text>
    <text x="360" y="860" class="fontPrimary fontSizeBodyText textColorPrimary textAlignLeft fontWeightSemibold">
      <tspan>2. 理论模型创新: </tspan>
      <tspan dx="10" class="textColorSecondary fontWeightNormal">引入前沿范式</tspan>
    </text>
    <text x="760" y="820" class="fontPrimary fontSizeBodyText textColorPrimary textAlignLeft fontWeightSemibold">
      <tspan>3. 实验周期缩短: </tspan>
      <tspan dx="10" class="textColorSecondary fontWeightNormal">平均减少40%</tspan>
    </text>
    <text x="760" y="860" class="fontPrimary fontSizeBodyText textColorPrimary textAlignLeft fontWeightSemibold">
      <tspan>4. 跨学科融合: </tspan>
      <tspan dx="10" class="textColorSecondary fontWeightNormal">显著增强</tspan>
    </text>

    <!-- 强调超大数字 -->
    <text x="1550" y="860" class="keyHighlightNumber textAlignCenter">
      <tspan>+30%</tspan>
    </text>
    <text x="1550" y="890" class="fontPrimary fontSizeSmallText textColorSecondary textAlignCenter">
      <tspan>效率提升</tspan>
    </text>
  </g>

  <!-- 结论总结区域 -->
  <g id="conclusionSection">
    <text x="960" y="960" class="fontPrimary fontSizeSectionTitle textColorPrimary textAlignCenter fontWeightBold">
      <tspan>结论总结</tspan>
    </text>
    <text x="960" y="1000" class="fontPrimary fontSizeBodyText textColorSecondary textAlignCenter fontWeightNormal">
      <tspan>综合来看，创新研究范式在多个维度展现出显著优势，</tspan>
      <tspan x="960" dy="30">为学术研究提供了新的方向和可能性。这有助于推动</tspan>
      <tspan x="960" dy="30">学科发展和解决复杂问题。</tspan>
    </text>
  </g>

  <!-- 装饰性元素 (简约风格) -->
  <g id="decorativeElements">
    <!-- 右上角抽象形状 -->
    <rect x="1700" y="0" width="220" height="100" class="decorativeShapeFill"/>
    <circle cx="1800" cy="50" r="40" class="fillAccent" opacity="0.2"/>

    <!-- 左下角线条点缀 -->
    <line x1="0" y1="980" x2="100" y2="1080" class="decorativeLineStroke"/>
    <line x1="80" y1="1080" x2="180" y2="980" class="decorativeLineStroke"/>

    <!-- 标题下方简洁线条 -->
    <line x1="800" y1="140" x2="1120" y2="140" class="lineDivider"/>
  </g>

</svg>