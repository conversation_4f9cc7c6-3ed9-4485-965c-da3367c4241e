<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Background Gradient for Diffuse Style -->
    <linearGradient id="backgroundGradient" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#F8FAFC"/>
      <stop offset="1" stop-color="#E0F2FE"/>
    </linearGradient>

    <!-- Primary Gradient for accents and shapes -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(135)">
      <stop stop-color="#3B82F6"/>
      <stop offset="1" stop-color="#1E40AF"/>
    </linearGradient>

    <!-- Accent Gradient for highlights -->
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0" gradientUnits="objectBoundingBox" gradientTransform="rotate(45)">
      <stop stop-color="#06B6D4"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>

    <!-- Filter for soft glow/diffuse effect on elements -->
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="30" result="blur"/>
      <feMerge>
        <feMergeNode in="blur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Card Shadow Filter -->
    <!-- Mimics '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' -->
    <filter id="cardShadow" x="-5%" y="-5%" width="110%" height="110%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4" result="blur1"/>
      <feOffset dx="0" dy="4" in="blur1" result="offset1"/>
      <feComponentTransfer in="offset1" result="shadow1">
        <feFuncA type="linear" slope="0.1"/>
      </feComponentTransfer>

      <feGaussianBlur in="SourceAlpha" stdDeviation="2" result="blur2"/>
      <feOffset dx="0" dy="2" in="blur2" result="offset2"/>
      <feComponentTransfer in="offset2" result="shadow2">
        <feFuncA type="linear" slope="0.06"/>
      </feComponentTransfer>

      <feMerge>
        <feMergeNode in="shadow1"/>
        <feMergeNode in="shadow2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Icon for info -->
    <symbol id="iconInfo" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="10" stroke="#3B82F6" stroke-width="2" fill="none"/>
      <line x1="12" y1="8" x2="12" y2="12" stroke="#3B82F6" stroke-width="2" stroke-linecap="round"/>
      <line x1="12" y1="16" x2="12" y2="16" stroke="#3B82F6" stroke-width="2" stroke-linecap="round"/>
    </symbol>

  </defs>

  <style>
    /* Font Definitions */
    /* Using local() for common system fonts to ensure compatibility */
    @font-face {
      font-family: 'Microsoft YaHei';
      src: local('Microsoft YaHei'), local('MSYH');
      font-weight: 400;
    }
    @font-face {
      font-family: 'Microsoft YaHei';
      src: local('Microsoft YaHei Bold'), local('MSYHB');
      font-weight: 700;
    }
    @font-face {
      font-family: 'Segoe UI';
      src: local('Segoe UI'), local('SegoeUI');
      font-weight: 400;
    }
    @font-face {
      font-family: 'Segoe UI';
      src: local('Segoe UI Bold'), local('SegoeUI-Bold');
      font-weight: 700;
    }
    @font-face {
      font-family: 'Source Han Sans CN';
      src: local('Source Han Sans CN'), local('SourceHanSansCN-Regular');
      font-weight: 400;
    }
    @font-face {
      font-family: 'Source Han Sans CN';
      src: local('Source Han Sans CN Bold'), local('SourceHanSansCN-Bold');
      font-weight: 700;
    }

    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

    /* Colors */
    .color-primary { fill: #3B82F6; }
    .color-accent { fill: #06B6D4; }
    .color-text-primary { fill: #1E293B; }
    .color-text-secondary { fill: #64748B; }
    .color-card-background { fill: #FFFFFF; }
    .color-card-border { stroke: #BAE6FD; }
    .color-info { fill: #06B6D4; } /* Matches accent color for info icon */

    /* Font Sizes and Weights */
    .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
    .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
    .content-title { font-size: 28px; font-weight: 700; line-height: 1.4; }
    .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
    .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
    .font-bold { font-weight: 700; }

    /* Layout Helpers */
    .text-align-center { text-anchor: middle; }
    .text-align-left { text-anchor: start; }

    /* Card Styles */
    .card {
      filter: url(#cardShadow);
    }

    /* Diffuse background elements */
    .diffuse-shape-1 {
      fill: url(#primaryGradient);
      opacity: 0.1;
      filter: url(#softGlow);
    }
    .diffuse-shape-2 {
      fill: url(#accentGradient);
      opacity: 0.08;
      filter: url(#softGlow);
    }

    /* Emphasized numbers/text */
    .super-large-number {
        font-size: 180px; /* Super large for emphasis */
        font-weight: 900;
        fill: #1E293B; /* text_primary */
        opacity: 0.08; /* Subtle watermark */
    }
    .highlight-text {
        fill: #06B6D4; /* Accent color */
        font-weight: 700;
    }
    .conclusion-text {
        font-size: 28px;
        font-weight: 700;
        fill: #1E293B;
    }

  </style>

  <!-- Background Layer (Diffuse Style) -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)"/>

  <!-- Diffuse Decorative Shapes (Soft Glow) -->
  <circle cx="100" cy="100" r="150" class="diffuse-shape-1"/>
  <circle cx="1800" cy="980" r="180" class="diffuse-shape-2"/>
  <rect x="1500" y="50" width="300" height="200" rx="100" ry="100" transform="rotate(20 1500 50)" class="diffuse-shape-2"/>
  <rect x="120" y="800" width="400" height="250" rx="120" ry="120" transform="rotate(-15 120 800)" class="diffuse-shape-1"/>

  <!-- Main Content Area (offset by page margins) -->
  <g transform="translate(80 60)">

    <!-- Header Section -->
    <text x="880" y="50" class="font-primary main-title color-text-primary text-align-center">{title}</text>
    <text x="880" y="120" class="font-secondary body-text color-text-secondary text-align-center">{subtitle}</text>

    <!-- Comparison Section (Left and Right Cards) -->
    <g transform="translate(0 200)"> <!-- Start content below header, with spacing -->

      <!-- Left Card -->
      <rect x="0" y="0" width="850" height="550" rx="12" ry="12" class="card color-card-background" stroke="#BAE6FD" stroke-width="1"/>
      <text x="425" y="60" class="font-primary section-title color-primary text-align-center">方案一: {content}</text>

      <!-- Left Card Content -->
      <text x="80" y="140" class="font-secondary body-text color-text-secondary text-align-left">
        <tspan x="80" y="140" class="font-bold color-text-primary">特点:</tspan>
        <tspan x="80" dy="40">· {content} 传统稳定架构，部署简单</tspan>
        <tspan x="80" dy="40">· {content} 易于维护和调试，成本较低</tspan>
        <tspan x="80" dy="40">· {content} 适用于小型项目，快速迭代</tspan>
        <tspan x="80" dy="40">· {content} 扩展性有限，性能瓶颈明显</tspan>
        <tspan x="80" dy="40">· {content} 功能迭代速度相对较慢</tspan>
      </text>
      <text x="425" y="480" class="font-primary super-large-number text-align-center">60%</text>


      <!-- Right Card -->
      <rect x="910" y="0" width="850" height="550" rx="12" ry="12" class="card color-card-background" stroke="#BAE6FD" stroke-width="1"/>
      <text x="1335" y="60" class="font-primary section-title color-accent text-align-center">方案二: {content}</text>

      <!-- Right Card Content -->
      <text x="990" y="140" class="font-secondary body-text color-text-secondary text-align-left">
        <tspan x="990" y="140" class="font-bold color-text-primary">特点:</tspan>
        <tspan x="990" dy="40">· {content} 微服务架构，高并发支持</tspan>
        <tspan x="990" dy="40">· {content} 技术栈更新，开发效率高</tspan>
        <tspan x="990" dy="40">· {content} 弹性伸缩，应对业务增长</tspan>
        <tspan x="990" dy="40">· {content} 复杂性增加，初期投入大</tspan>
        <tspan x="990" dy="40">· {content} 对团队技术要求更高</tspan>
      </text>
      <text x="1335" y="480" class="font-primary super-large-number text-align-center">90%</text>

      <!-- Difference Emphasis -->
      <g transform="translate(880 200)">
        <use xlink:href="#iconInfo" x="-12" y="-12" width="24" height="24" class="color-info"/>
        <text x="0" y="0" class="font-primary content-title color-info text-align-center">差异点</text>
      </g>

      <text x="880" y="270" class="font-secondary body-text color-text-secondary text-align-center">
        <tspan x="880" y="270" class="font-bold highlight-text">性能和扩展性:</tspan>
        <tspan x="880" dy="40">方案二在处理高并发场景下表现更优，</tspan>
        <tspan x="880" dy="30">具备更强的弹性伸缩能力。</tspan>
        <tspan x="880" dy="50" class="font-bold highlight-text">开发和维护成本:</tspan>
        <tspan x="880" dy="40">方案一初期投入低，但长期维护可能受限；</tspan>
        <tspan x="880" dy="30">方案二初期复杂，但长期效率更高。</tspan>
      </text>

    </g>

    <!-- Conclusion Section -->
    <g transform="translate(0 780)"> <!-- Below comparison cards, with spacing -->
      <rect x="0" y="0" width="1760" height="200" rx="12" ry="12" class="card color-card-background" stroke="#BAE6FD" stroke-width="1"/>
      <text x="880" y="60" class="font-primary section-title color-text-primary text-align-center">结论总结</text>
      <text x="880" y="130" class="font-secondary conclusion-text color-text-secondary text-align-center">
        <tspan x="880" y="130" class="font-bold highlight-text">最终选择:</tspan>
        <tspan x="880" dy="40">考虑到未来业务发展和技术前瞻性，我们倾向于采用{content}方案二，</tspan>
        <tspan x="880" dy="30">尽管初期挑战较大，但其长期收益更符合我们的战略目标。</tspan>
      </text>
    </g>

  </g>

  <!-- Logo (Top Left Corner, within page margins) -->
  <g transform="translate(80 60)">
    <!-- Placeholder for Logo -->
    <rect x="0" y="0" width="120" height="40" rx="8" ry="8" fill="#3B82F6"/>
    <text x="60" y="28" class="font-primary small-text color-card-background text-align-center">LOGO</text>
    <!-- Uncomment and replace href for actual image logo -->
    <!-- <image x="0" y="0" width="120" height="40" href="{logo_url}"/> -->
  </g>

</svg>