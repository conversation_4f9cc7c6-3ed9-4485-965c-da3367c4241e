<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style>
      /* Base Colors */
      .bg-color { fill: #F8FAFC; }
      .primary-color { fill: #1E40AF; }
      .secondary-color { fill: #475569; }
      .accent-color { fill: #3B82F6; }
      .text-primary { fill: #1E293B; }
      .text-secondary { fill: #64748B; }
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .card-border { stroke: #BAE6FD; stroke-width: 1; }
      .container-background { fill: #E0F2FE; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; }
      .main-title { font-size: 56px; font-weight: 700; }
      .section-title { font-size: 36px; font-weight: 600; }
      .content-title { font-size: 28px; font-weight: 500; }
      .body-text { font-size: 22px; font-weight: 400; }
      .small-text { font-size: 16px; font-weight: 400; }
      .caption { font-size: 14px; font-weight: 300; }

      /* Card Styles */
      .card {
        fill: #FFFFFF;
        stroke: #BAE6FD;
        stroke-width: 1px;
        filter: url(#shadow); /* Apply shadow filter */
      }

      /* Gradients for decorative elements */
      .gradient-primary-fill { fill: url(#primaryGradient); }
      .gradient-accent-fill { fill: url(#accentGradient); }
      .gradient-bg-fill { fill: url(#backgroundGradient); }

      /* Icon Styles */
      .icon-style {
        stroke: #3B82F6; /* Accent color for icons */
        stroke-width: 2;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      /* Filters for shadows */
      <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
        <feDropShadow dx="0" dy="4" stdDeviation="3" flood-color="rgba(0, 0, 0, 0.1)" />
        <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0, 0, 0, 0.06)" />
      </filter>
      <filter id="subtle-shadow" x="-50%" y="-50%" width="200%" height="200%">
        <feDropShadow dx="0" dy="2" stdDeviation="1" flood-color="rgba(0, 0, 0, 0.05)" />
      </filter>

      /* Call to Action Button Style */
      .cta-button-bg { fill: #3B82F6; } /* Accent color */
      .cta-button-text { fill: #FFFFFF; font-size: 24px; font-weight: 600; }

      /* Data Chart Styles */
      .chart-bar-fill { fill: #1E40AF; } /* Primary color */
      .chart-bar-accent-fill { fill: #3B82F6; } /* Accent color */
      .chart-axis-line { stroke: #64748B; stroke-width: 1; }
      .chart-label { fill: #64748B; font-size: 16px; font-weight: 400; }

    </style>

    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E40AF" />
      <stop offset="100%" stop-color="#475569" />
    </linearGradient>

    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" />
      <stop offset="100%" stop-color="#1E40AF" />
    </linearGradient>

    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>

    <!-- Simple icon definitions (reusable) -->
    <g id="icon-check" class="icon-style">
      <polyline points="20 6 9 17 4 12"></polyline>
    </g>
    <g id="icon-arrow-right" class="icon-style">
      <polyline points="12 5 19 12 12 19"></polyline>
      <line x1="19" y1="12" x2="5" y2="12"></line>
    </g>
    <g id="icon-mail" class="icon-style">
      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
      <polyline points="22,6 12,13 2,6"></polyline>
    </g>
    <g id="icon-phone" class="icon-style">
      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-3.67-3.67A19.79 19.79 0 0 1 2 6.18 2 2 0 0 1 4.08 4h3a2 2 0 0 1 2 1.74 17 17 0 0 0 .63 4.41 2 2 0 0 1-.72 2.18l-1.4 1.4a1 1 0 0 0 .11 1.15 19.93 19.93 0 0 0 8.58 8.58 1 1 0 0 0 1.15.11l1.4-1.4a2 2 0 0 1 2.18-.72A17 17 0 0 0 19.26 20a2 2 0 0 1 1.74 2z"></path>
    </g>
  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" class="gradient-bg-fill" />

  <!-- Header -->
  <g id="header">
    <!-- Logo Placeholder -->
    <rect x="80" y="60" width="160" height="40" fill="#E0F2FE" rx="8" ry="8" filter="url(#subtle-shadow)"/>
    <text x="95" y="87" class="small-text text-secondary font-primary">企业Logo</text>

    <!-- Main Title -->
    <text x="960" y="160" text-anchor="middle" class="main-title text-primary font-primary">
      {title}
    </text>
    <!-- Subtitle -->
    <text x="960" y="220" text-anchor="middle" class="section-title text-secondary font-secondary">
      {subtitle}
    </text>
  </g>

  <!-- Main Content Area - Bento Grid Style Layout -->
  <g id="main-content">
    <!-- Left Card: Key Conclusions -->
    <rect x="80" y="280" width="860" height="400" rx="12" ry="12" class="card" />
    <text x="120" y="330" class="content-title text-primary font-primary">核心结论</text>

    <g id="conclusions-list">
      <!-- Using tspan with explicit y for line spacing, dy is relative to current position. -->
      <text x="120" y="390" class="body-text text-secondary font-primary">
        <tspan x="120" y="390">✅ 成果显著：年度目标超额达成，市场份额稳步提升。</tspan>
        <tspan x="120" y="430">✅ 创新突破：多项技术创新推动产品迭代和升级。</tspan>
        <tspan x="120" y="470">✅ 团队协作：高效沟通和紧密合作保障项目顺利进行。</tspan>
        <tspan x="120" y="510">✅ 用户增长：用户活跃度及满意度均有显著提升。</tspan>
        <tspan x="120" y="550">✅ 风险控制：有效识别和管理潜在风险，确保业务稳定。</tspan>
      </text>
    </g>

    <!-- Right Top Card: Action Points -->
    <rect x="980" y="280" width="860" height="300" rx="12" ry="12" class="card" />
    <text x="1020" y="330" class="content-title text-primary font-primary">行动要点</text>
    <g id="action-points-list">
      <text x="1020" y="390" class="body-text text-secondary font-primary">
        <tspan x="1020" y="390">➡️ 市场拓展：深化区域合作，探索新兴市场。</tspan>
        <tspan x="1020" y="430">➡️ 产品优化：基于用户反馈持续改进产品体验。</tspan>
        <tspan x="1020" y="470">➡️ 人才培养：加强内部培训，提升团队核心竞争力。</tspan>
        <tspan x="1020" y="510">➡️ 效率提升：引入自动化工具，优化工作流程。</tspan>
      </text>
    </g>

    <!-- Bottom Left Card: Data Visualization (Placeholder Bar Chart) -->
    <rect x="80" y="700" width="860" height="320" rx="12" ry="12" class="card" />
    <text x="120" y="750" class="content-title text-primary font-primary">数据概览</text>
    <g id="data-chart">
      <!-- X-axis -->
      <line x1="120" y1="920" x2="900" y2="920" class="chart-axis-line" />
      <!-- Y-axis -->
      <line x1="120" y1="780" x2="120" y2="920" class="chart-axis-line" />

      <!-- Bars -->
      <rect x="150" y="860" width="60" height="60" class="chart-bar-fill" rx="4" ry="4"/>
      <text x="180" y="940" text-anchor="middle" class="chart-label">Q1</text>
      <text x="180" y="850" text-anchor="middle" class="chart-label">60%</text>

      <rect x="250" y="820" width="60" height="100" class="chart-bar-accent-fill" rx="4" ry="4"/>
      <text x="280" y="940" text-anchor="middle" class="chart-label">Q2</text>
      <text x="280" y="810" text-anchor="middle" class="chart-label">80%</text>

      <rect x="350" y="790" width="60" height="130" class="chart-bar-fill" rx="4" ry="4"/>
      <text x="380" y="940" text-anchor="middle" class="chart-label">Q3</text>
      <text x="380" y="780" text-anchor="middle" class="chart-label">95%</text>

      <rect x="450" y="770" width="60" height="150" class="chart-bar-accent-fill" rx="4" ry="4"/>
      <text x="480" y="940" text-anchor="middle" class="chart-label">Q4</text>
      <text x="480" y="760" text-anchor="middle" class="chart-label">100%</text>

      <!-- Y-axis labels -->
      <text x="100" y="790" text-anchor="end" class="chart-label">100%</text>
      <text x="100" y="820" text-anchor="end" class="chart-label">80%</text>
      <text x="100" y="850" text-anchor="end" class="chart-label">60%</text>
      <text x="100" y="880" text-anchor="end" class="chart-label">40%</text>
      <text x="100" y="910" text-anchor="end" class="chart-label">20%</text>
    </g>


    <!-- Right Bottom Card: Contact Info & Call to Action -->
    <rect x="980" y="600" width="860" height="420" rx="12" ry="12" class="card" />
    <text x="1020" y="650" class="content-title text-primary font-primary">联系我们</text>

    <g id="contact-info">
      <use xlink:href="#icon-mail" x="1020" y="700" width="32" height="32" />
      <text x="1065" y="725" class="body-text text-secondary font-primary">邮箱：<EMAIL></text>

      <use xlink:href="#icon-phone" x="1020" y="750" width="32" height="32" />
      <text x="1065" y="775" class="body-text text-secondary font-primary">电话：+86 123 4567 8901</text>

      <text x="1020" y="825" class="body-text text-primary font-primary">感谢您的聆听和支持！</text>
    </g>

    <g id="call-to-action">
      <rect x="1020" y="880" width="300" height="70" rx="10" ry="10" class="cta-button-bg" />
      <text x="1170" y="925" text-anchor="middle" class="cta-button-text font-primary">立即行动</text>
      <use xlink:href="#icon-arrow-right" x="1260" y="898" width="32" height="32" stroke="#FFFFFF" />
    </g>

    <!-- Decorative elements for tech feel (subtle gradients) -->
    <rect x="1700" y="100" width="100" height="100" rx="20" ry="20" fill="url(#accentGradient)" opacity="0.1" />
    <circle cx="100" cy="980" r="50" fill="url(#primaryGradient)" opacity="0.08" />
    <rect x="1400" y="50" width="60" height="180" rx="10" ry="10" fill="url(#primaryGradient)" opacity="0.05" transform="rotate(15 1430 140)" />

  </g>

  <!-- Footer -->
  <g id="footer">
    <text x="80" y="1020" class="small-text text-light font-secondary">
      {date}
    </text>
    <text x="1840" y="1020" text-anchor="end" class="small-text text-light font-secondary">
      {author}
    </text>
  </g>

</svg>