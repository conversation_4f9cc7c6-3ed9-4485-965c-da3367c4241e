// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-01-18T10:15:00+08:00]
// Reason: 重构播客组件，添加AI脚本生成和编辑功能，移除长文本策略选项，新增双语音选择支持
// Principle_Applied: 用户体验 - 提供脚本编辑控制和双语音选择; 模块化 - 分离脚本生成和音频生成流程
// Optimization: 新的工作流程提升用户对播客内容的控制，支持对话式和访谈式的双语音配置
// Architectural_Note (AR): 保持组件结构清晰，添加脚本管理状态和双语音管理
// Documentation_Note (DW): 实现全新的播客生成工作流程，支持双语音对话
// }} 

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Radio, Space, Button, Input, Spin, message, Tabs, Upload, Card, Select, Divider } from 'antd';
import { PlayCircleOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import AudioPlayer from '../../AudioPlayer';

const { TextArea } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

const PodcastContent = ({ 
  config, 
  updateConfig, 
  result, 
  handleDownload, 
  handlePlayAudio,
  availableVoices
}) => {
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-17T23:15:00+08:00]
  // Reason: 添加调试信息，查看组件重渲染的原因和频率
  // Principle_Applied: 调试优化 - 通过日志分析性能问题; 问题诊断 - 定位重渲染根源
  // Optimization: 添加渲染计数和props变化检测
  // Architectural_Note (AR): 临时调试代码，用于问题诊断
  // Documentation_Note (DW): 根据用户反馈添加的调试功能
  // }}
  const renderCount = useRef(0);
  renderCount.current += 1;
  
  //console.log(`🔄 PodcastContent 渲染次数: ${renderCount.current}`);
  console.log('📊 Props状态:', {
    // sourceContentLength: sourceContent?.length || 0,  // 已移至主组件
    configPodcast: config?.podcast,
    resultType: result?.contentType,
    availableVoicesCount: availableVoices?.length || 0
  });
  
  const currentConfig = config.podcast;
  const [actualDuration, setActualDuration] = useState(null);
  
  // {{CHENGQI:
  // Action: Simplified
  // Timestamp: [2025-01-17T23:50:00+08:00]
  // Reason: 简化props监控逻辑，移除已不存在的props引用
  // Principle_Applied: KISS - 保持简单; 单一职责 - 专注于配置和结果展示
  // Optimization: 移除不必要的调试代码，简化组件逻辑
  // Architectural_Note (AR): 适应新的组件职责分工
  // Documentation_Note (DW): 清理调试代码，专注核心功能
  // }}
  const prevProps = useRef();
  
  useEffect(() => {
    if (prevProps.current) {
      const changes = [];
      //console.log(`🔄 PodcastContent 渲染次数: ${renderCount.current}`);
      if (prevProps.current.config !== config) {
        changes.push('config changed');
      }
      if (prevProps.current.result !== result) {
        changes.push('result changed');
      }
      if (prevProps.current.availableVoices !== availableVoices) {
        changes.push('availableVoices changed');
      }
      if (prevProps.current.updateConfig !== updateConfig) {
        changes.push('updateConfig function changed');
      }
      if (prevProps.current.handleDownload !== handleDownload) {
        changes.push('handleDownload function changed');
      }
      
      if (changes.length > 0) {
        console.log('🔍 Props变化导致重渲染:', changes);
      }
    }
    
    prevProps.current = {
      config,
      result,
      availableVoices,
      updateConfig,
      handleDownload
    };
  });

  // 时间格式化函数
  const formatDuration = (seconds) => {
    if (!seconds || !isFinite(seconds)) return null;
    
    if (seconds < 60) {
      return `${Math.round(seconds)}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.round(seconds % 60);
      return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分钟`;
    }
  };

  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-18T10:20:00+08:00]
  // Reason: 添加双语音选择支持，判断是否需要显示双语音选择器
  // Principle_Applied: 单一职责 - 专门处理双语音逻辑; 条件渲染 - 根据风格动态显示UI
  // Optimization: 只在对话式和访谈式时显示双语音选择，提升用户体验
  // Architectural_Note (AR): 扩展配置支持，向后兼容单语音模式
  // Documentation_Note (DW): 新增双语音选择逻辑
  // }}
  
  // 判断是否需要双语音选择
  const needsDualVoices = currentConfig.style === 'conversational' || currentConfig.style === 'interview';
  
  // 获取语音选项，用于下拉选择
  const getVoiceOptions = () => {
    return availableVoices.map(voice => ({
      value: voice.value,
      label: voice.label,
      description: voice.description
    }));
  };

  // 获取语音信息
  const getVoiceInfo = (voiceId) => {
    const voice = availableVoices.find(v => v.value === voiceId);
    return voice || { value: voiceId, label: voiceId, description: '' };
  };



  const ConfigPanel = () => (
    <Space direction="vertical" style={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>
      <div>
        <label>播客风格：</label>
        <Radio.Group 
          value={currentConfig.style} 
          onChange={(e) => updateConfig('podcast', 'style', e.target.value)}
          style={{ 
            display: 'flex',
            flexWrap: 'wrap',
            gap: '12px',
            marginTop: '8px'
          }}
        >
          <Radio value="conversational">对话式</Radio>
          <Radio value="educational">教育性</Radio>
          <Radio value="interview">访谈式</Radio>
        </Radio.Group>
      </div>
      <div>
        <label>时长控制：</label>
        <Radio.Group 
          value={currentConfig.duration} 
          onChange={(e) => updateConfig('podcast', 'duration', e.target.value)}
          style={{ 
            display: 'flex',
            flexWrap: 'wrap',
            gap: '12px',
            marginTop: '8px'
          }}
        >
          <Radio value="short">短（5-10分钟）</Radio>
          <Radio value="medium">中（10-20分钟）</Radio>
          <Radio value="long">长（20-30分钟）</Radio>
        </Radio.Group>
      </div>
      
      {/* {{CHENGQI:
      // Action: Added
      // Timestamp: [2025-01-18T10:35:00+08:00]
      // Reason: 根据播客风格显示不同的语音选择界面，对话式和访谈式显示双语音选择
      // Principle_Applied: 条件渲染 - 根据业务逻辑动态显示UI; 用户体验 - 提供符合场景的配置选项
      // Optimization: 双语音选择使用下拉框，更清晰直观
      // Architectural_Note (AR): 根据功能需求动态调整UI结构
      // Documentation_Note (DW): 实现双语音选择界面
      // }} */}
      {needsDualVoices ? (
        // 双语音选择模式（对话式和访谈式）
        <div>
          <label>主播语音配置：</label>
          <div style={{ marginTop: '8px' }}>
            <div style={{ marginBottom: '12px' }}>
              <label style={{ fontSize: '12px', color: '#666', marginBottom: '4px', display: 'block' }}>
                主播一语音：
              </label>
              <Select
                style={{ width: '100%' }}
                value={currentConfig.voice1}
                onChange={(value) => updateConfig('podcast', 'voice1', value)}
                placeholder="选择主播一语音"
                optionLabelProp="label"
              >
                {getVoiceOptions().map(voice => (
                  <Option key={voice.value} value={voice.value} label={voice.label}>
                    <div>
                      <span style={{ fontWeight: 'bold' }}>{voice.label}</span>
                      {voice.description && (
                        <div style={{ fontSize: '11px', color: '#666' }}>
                          {voice.description}
                        </div>
                      )}
                    </div>
                  </Option>
                ))}
              </Select>
            </div>
            
            <div>
              <label style={{ fontSize: '12px', color: '#666', marginBottom: '4px', display: 'block' }}>
                主播二语音：
              </label>
              <Select
                style={{ width: '100%' }}
                value={currentConfig.voice2}
                onChange={(value) => updateConfig('podcast', 'voice2', value)}
                placeholder="选择主播二语音"
                optionLabelProp="label"
              >
                {getVoiceOptions().map(voice => (
                  <Option key={voice.value} value={voice.value} label={voice.label}>
                    <div>
                      <span style={{ fontWeight: 'bold' }}>{voice.label}</span>
                      {voice.description && (
                        <div style={{ fontSize: '11px', color: '#666' }}>
                          {voice.description}
                        </div>
                      )}
                    </div>
                  </Option>
                ))}
              </Select>
            </div>
            
            {currentConfig.voice1 && currentConfig.voice2 && (
              <div style={{ 
                marginTop: '8px', 
                padding: '8px', 
                backgroundColor: '#f0f8ff', 
                borderRadius: '4px',
                fontSize: '12px',
                color: '#1890ff'
              }}>
                <strong>对话配置：</strong> {getVoiceInfo(currentConfig.voice1).label} ↔ {getVoiceInfo(currentConfig.voice2).label}
              </div>
            )}
          </div>
        </div>
      ) : (
        // 单语音选择模式（教育性）
        <div>
          <label>语音类型：</label>
          <div style={{
            maxHeight: '200px',
            overflowY: 'auto',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            padding: '12px',
            marginTop: '8px',
            backgroundColor: '#fafafa'
          }}>
            <Radio.Group 
              value={currentConfig.voice} 
              onChange={(e) => updateConfig('podcast', 'voice', e.target.value)}
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(140px, 1fr))',
                gap: '8px',
                width: '100%'
              }}
            >
              {availableVoices.map(voice => (
                <Radio 
                  key={voice.value} 
                  value={voice.value}
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    textAlign: 'center',
                    padding: '8px 4px',
                    borderRadius: '4px',
                    backgroundColor: currentConfig.voice === voice.value ? '#e6f7ff' : 'transparent',
                    border: currentConfig.voice === voice.value ? '1px solid #1890ff' : '1px solid transparent',
                    minHeight: '60px',
                    fontSize: '12px'
                  }}
                >
                  <div style={{ 
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%'
                  }}>
                    <span style={{ 
                      fontWeight: 'bold', 
                      fontSize: '12px',
                      lineHeight: '1.2',
                      marginBottom: '2px',
                      wordBreak: 'break-word'
                    }}>
                      {voice.label}
                    </span>
                    {voice.description && (
                      <div style={{ 
                        fontSize: '10px', 
                        color: '#666',
                        lineHeight: '1.2',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        wordBreak: 'break-word'
                      }}>
                        {voice.description}
                      </div>
                    )}
                  </div>
                </Radio>
              ))}
              {availableVoices.length === 0 && (
                <div style={{ 
                  color: '#999', 
                  fontSize: '12px', 
                  padding: '20px',
                  textAlign: 'center',
                  gridColumn: '1 / -1'
                }}>
                  正在加载可用语音...
                </div>
              )}
            </Radio.Group>
          </div>
        </div>
      )}
    </Space>
  );

  // {{CHENGQI:
  // Action: 保持原有
  // Timestamp: [2025-01-17T16:15:00+08:00]
  // Reason: ResultDisplay保持不变，继续使用AudioPlayer组件
  // Principle_Applied: 向后兼容 - 保持音频播放功能不变
  // Optimization: 音频播放和下载功能保持现有的优化
  // Architectural_Note (AR): 结果展示部分无需修改
  // Documentation_Note (DW): 音频播放界面保持原有设计
  // }}
  
  const ResultDisplay = () => {
    if (!result || result.contentType !== 'podcast') return null;

    return (
      <div style={{ padding: '40px' }}>
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <PlayCircleOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <h3>播客音频已生成</h3>
          <p style={{ color: '#666', marginBottom: '8px' }}>
            {actualDuration ? (
              <>实际时长：{formatDuration(actualDuration)}</>
            ) : (
              <>预计时长：约 {currentConfig.duration === 'short' ? '8' : currentConfig.duration === 'medium' ? '15' : '25'} 分钟</>
            )}
          </p>
        </div>
        
        {/* 音频播放器 */}
        <div style={{ marginBottom: '24px' }}>
          <AudioPlayer
            audioData={result.data}
            contentType={result.content_type || 'audio/mpeg'}
            size="medium"
            onPlay={() => {}}
            onPause={() => {}}
            onEnded={() => {}}
            onError={(error) => {}}
            onDurationChange={(duration) => setActualDuration(duration)}
          />
        </div>
        
        {/* {{CHENGQI:
        // Action: Added
        // Timestamp: [2025-01-19T17:45:00+08:00]
        // Reason: 重新添加音频下载功能，支持客户端tk下载和网页下载两种模式
        // Principle_Applied: 用户体验 - 提供音频文件下载功能; 平台兼容 - 区分客户端和网页环境
        // Optimization: 根据运行环境选择合适的下载方式
        // Architectural_Note (AR): 恢复音频下载功能，完善用户交互体验
        // Documentation_Note (DW): 重新实现音频下载按钮和逻辑
        // }} */}
        {/* 操作按钮 */}
        <div style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(result.data, 'podcast.mp3', result.content_type || 'audio/mpeg')}
            size="large"
            style={{ marginRight: '16px' }}
          >
            下载音频
          </Button>
        </div>
      
      </div>
    );
  };



  return {
    ConfigPanel,
    ResultDisplay
  };
};

export default PodcastContent; 