<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <style>
      /* Colors */
      .primary-color { fill: #4A86E8; }
      .secondary-color { fill: #3B82F6; }
      .accent-color { fill: #0EA5E9; }
      .background-color { fill: #F8FAFC; }
      .text-primary-color { fill: #1E293B; } /* Renamed to avoid confusion with primary-color fill */
      .text-secondary-color { fill: #64748B; } /* Renamed */
      .text-light { fill: #94A3B8; }
      .card-background { fill: #FFFFFF; }
      .container-background { fill: #E0F2FE; }

      /* Fonts */
      .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
      .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
      .font-accent { font-family: 'Times New Roman', serif; }

      /* Font Sizes and Weights */
      .hero-title { font-size: 72px; font-weight: 700; line-height: 1.1; }
      .main-title { font-size: 56px; font-weight: 700; line-height: 1.1; }
      .section-title { font-size: 36px; font-weight: 700; line-height: 1.4; }
      .content-title { font-size: 28px; font-weight: 600; line-height: 1.4; }
      .body-text { font-size: 22px; font-weight: 400; line-height: 1.6; }
      .small-text { font-size: 16px; font-weight: 400; line-height: 1.6; }
      .caption-text { font-size: 14px; font-weight: 400; line-height: 1.6; }

      /* Card Styles */
      .card-border { stroke: #BAE6FD; stroke-width: 1px; }

      /* Specific styles for this template */
      .chart-label { font-size: 18px; font-weight: 500; fill: #1E293B; }
      .chart-value { font-size: 24px; font-weight: 700; fill: #4A86E8; }
      .data-stat-number { font-size: 64px; font-weight: 700; fill: #4A86E8; }
      .data-stat-label { font-size: 24px; font-weight: 500; fill: #1E293B; }
      .progress-circle-text { font-size: 48px; font-weight: 700; fill: #0EA5E9; }

    </style>

    <!-- Linear Gradients for SVG elements -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#4A86E8" />
      <stop offset="100%" stop-color="#3B82F6" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#F8FAFC" />
      <stop offset="100%" stop-color="#E0F2FE" />
    </linearGradient>
    <linearGradient id="chartBarGradient" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>
    <linearGradient id="progressCircleGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#0EA5E9" />
      <stop offset="100%" stop-color="#4A86E8" />
    </linearGradient>

    <!-- Filter for subtle shadow -->
    <filter id="dropshadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="6" />
      <feOffset dx="0" dy="8" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.12" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>

  <!-- Background Layer -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#backgroundGradient)" />

  <!-- Decorative elements (abstract, illustrative) -->
  <circle cx="1700" cy="150" r="80" fill="#0EA5E9" opacity="0.1" />
  <circle cx="1800" cy="80" r="120" fill="#4A86E8" opacity="0.05" />
  <rect x="50" y="900" width="200" height="100" rx="20" fill="#3B82F6" opacity="0.08" />
  <path d="M0 850 C 300 950, 600 800, 900 900 L 900 1080 L 0 1080 Z" fill="#4A86E8" opacity="0.05" />
  <path d="M1920 200 C 1700 100, 1500 300, 1300 250 L 1300 0 L 1920 0 Z" fill="#0EA5E9" opacity="0.03" />

  <!-- Logo Placeholder -->
  <g transform="translate(80, 60)">
    <rect x="0" y="0" width="160" height="40" rx="8" fill="#4A86E8" />
    <text x="80" y="25" text-anchor="middle" class="font-primary small-text" fill="#FFFFFF">
      <tspan>LOGO</tspan>
    </text>
    <!-- Actual Logo: <image href="{logo_url}" x="0" y="0" width="160" height="40" preserveAspectRatio="xMidYMid meet"/> -->
  </g>

  <!-- Main Title Area -->
  <g transform="translate(192, 100)">
    <text x="0" y="0" class="font-primary main-title text-primary-color">
      <tspan>{title}</tspan>
    </text>
    <text x="0" y="70" class="font-secondary body-text text-secondary-color">
      <tspan>深入分析数据洞察，助力知识高效传递</tspan>
      <tspan x="0" dy="30">Deep Dive into Data Insights, Empowering Effective Knowledge Transfer</tspan>
      <tspan x="0" dy="30">{subtitle}</tspan>
    </text>
  </g>

  <!-- Main Content Area: Data Visualization and Statistics -->
  <g transform="translate(80, 280)">
    <!-- Left Section: Main Chart Area (Simulated Bar Chart) -->
    <rect x="0" y="0" width="1000" height="700" rx="12" fill="#FFFFFF" class="card-border" filter="url(#dropshadow)" />
    <text x="50" y="60" class="font-primary content-title text-primary-color">
      <tspan>学习进度分析和趋势</tspan>
      <tspan class="font-secondary small-text text-secondary-color" x="50" dy="30">Learning Progress Analysis and Trends</tspan>
    </text>

    <!-- Simulated Bar Chart -->
    <g transform="translate(120, 180)">
      <!-- Y-axis labels -->
      <text x="-50" y="0" class="font-secondary caption-text text-secondary-color" text-anchor="end">100%</text>
      <text x="-50" y="100" class="font-secondary caption-text text-secondary-color" text-anchor="end">75%</text>
      <text x="-50" y="200" class="font-secondary caption-text text-secondary-color" text-anchor="end">50%</text>
      <text x="-50" y="300" class="font-secondary caption-text text-secondary-color" text-anchor="end">25%</text>
      <text x="-50" y="400" class="font-secondary caption-text text-secondary-color" text-anchor="end">0%</text>

      <!-- X-axis line -->
      <line x1="0" y1="400" x2="750" y2="400" stroke="#E0E0E0" stroke-width="1" />

      <!-- Bars and Labels -->
      <!-- Bar 1 -->
      <rect x="0" y="120" width="80" height="280" rx="8" fill="url(#chartBarGradient)" />
      <text x="40" y="440" class="font-secondary chart-label" text-anchor="middle">模块一</text>
      <text x="40" y="465" class="font-secondary small-text text-secondary-color" text-anchor="middle">Module 1</text>
      <text x="40" y="100" class="font-primary chart-value" text-anchor="middle">70%</text>

      <!-- Bar 2 -->
      <rect x="150" y="180" width="80" height="220" rx="8" fill="url(#chartBarGradient)" />
      <text x="190" y="440" class="font-secondary chart-label" text-anchor="middle">模块二</text>
      <text x="190" y="465" class="font-secondary small-text text-secondary-color" text-anchor="middle">Module 2</text>
      <text x="190" y="160" class="font-primary chart-value" text-anchor="middle">55%</text>

      <!-- Bar 3 -->
      <rect x="300" y="80" width="80" height="320" rx="8" fill="url(#chartBarGradient)" />
      <text x="340" y="440" class="font-secondary chart-label" text-anchor="middle">模块三</text>
      <text x="340" y="465" class="font-secondary small-text text-secondary-color" text-anchor="middle">Module 3</text>
      <text x="340" y="60" class="font-primary chart-value" text-anchor="middle">80%</text>

      <!-- Bar 4 -->
      <rect x="450" y="140" width="80" height="260" rx="8" fill="url(#chartBarGradient)" />
      <text x="490" y="440" class="font-secondary chart-label" text-anchor="middle">模块四</text>
      <text x="490" y="465" class="font-secondary small-text text-secondary-color" text-anchor="middle">Module 4</text>
      <text x="490" y="120" class="font-primary chart-value" text-anchor="middle">65%</text>

      <!-- Bar 5 -->
      <rect x="600" y="50" width="80" height="350" rx="8" fill="url(#chartBarGradient)" />
      <text x="640" y="440" class="font-secondary chart-label" text-anchor="middle">模块五</text>
      <text x="640" y="465" class="font-secondary small-text text-secondary-color" text-anchor="middle">Module 5</text>
      <text x="640" y="30" class="font-primary chart-value" text-anchor="middle">88%</text>
    </g>
    <!-- End Simulated Bar Chart -->
  </g>

  <!-- Right Section: Data Cards and Statistics -->
  <g transform="translate(1100, 280)">
    <!-- Top Row: Progress Overview Card -->
    <rect x="0" y="0" width="740" height="340" rx="12" fill="#FFFFFF" class="card-border" filter="url(#dropshadow)" />
    <text x="50" y="60" class="font-primary content-title text-primary-color">
      <tspan>总体学习进度</tspan>
      <tspan class="font-secondary small-text text-secondary-color" x="50" dy="30">Overall Learning Progress</tspan>
    </text>

    <!-- Progress Circle 1 -->
    <g transform="translate(180, 200)">
      <circle cx="0" cy="0" r="80" stroke="#E0F2FE" stroke-width="20" fill="none" />
      <!-- Circumference for r=80 is 2 * PI * 80 = 502.6544 -->
      <!-- 75% progress: dashoffset = 502.6544 * (1 - 0.75) = 125.6636 -->
      <circle cx="0" cy="0" r="80" stroke="url(#progressCircleGradient)" stroke-width="20" fill="none"
              stroke-dasharray="502.6544" stroke-dashoffset="125.6636" stroke-linecap="round" />
      <text x="0" y="10" text-anchor="middle" class="font-primary progress-circle-text">75%</text>
      <text x="0" y="70" text-anchor="middle" class="font-secondary small-text text-secondary-color">完成率</text>
    </g>

    <!-- Progress Circle 2 -->
    <g transform="translate(560, 200)">
      <circle cx="0" cy="0" r="80" stroke="#E0F2FE" stroke-width="20" fill="none" />
      <!-- 60% progress: dashoffset = 502.6544 * (1 - 0.60) = 201.06176 -->
      <circle cx="0" cy="0" r="80" stroke="url(#progressCircleGradient)" stroke-width="20" fill="none"
              stroke-dasharray="502.6544" stroke-dashoffset="201.06176" stroke-linecap="round" />
      <text x="0" y="10" text-anchor="middle" class="font-primary progress-circle-text">60%</text>
      <text x="0" y="70" text-anchor="middle" class="font-secondary small-text text-secondary-color">平均得分</text>
    </g>

    <!-- Bottom Row: Key Statistics -->
    <rect x="0" y="360" width="740" height="340" rx="12" fill="#FFFFFF" class="card-border" filter="url(#dropshadow)" />
    <text x="50" y="410" class="font-primary content-title text-primary-color">
      <tspan>关键统计数据</tspan>
      <tspan class="font-secondary small-text text-secondary-color" x="50" dy="30">Key Statistics</tspan>
    </text>

    <!-- Stat 1 -->
    <g transform="translate(80, 520)">
      <text x="0" y="0" class="font-primary data-stat-number">120</text>
      <text x="0" y="50" class="font-primary data-stat-label">总课程数</text>
      <text x="0" y="80" class="font-secondary small-text text-secondary-color">Total Courses</text>
    </g>

    <!-- Stat 2 -->
    <g transform="translate(300, 520)">
      <text x="0" y="0" class="font-primary data-stat-number">95%</text>
      <text x="0" y="50" class="font-primary data-stat-label">通过率</text>
      <text x="0" y="80" class="font-secondary small-text text-secondary-color">Pass Rate</text>
    </g>

    <!-- Stat 3 -->
    <g transform="translate(520, 520)">
      <text x="0" y="0" class="font-primary data-stat-number">8.5</text>
      <text x="0" y="50" class="font-primary data-stat-label">平均互动分</text>
      <text x="0" y="80" class="font-secondary small-text text-secondary-color">Avg. Interaction Score</text>
    </g>

    <!-- Placeholder for content -->
    <text x="50" y="660" class="font-secondary body-text text-secondary-color">
      <tspan>{content}</tspan>
    </text>
  </g>

  <!-- Author and Date Placeholder -->
  <g transform="translate(1600, 1020)">
    <text x="0" y="0" text-anchor="end" class="font-secondary caption-text text-secondary-color">
      <tspan>日期: {date}</tspan>
      <tspan x="0" dy="20">作者: {author}</tspan>
    </text>
  </g>

</svg>