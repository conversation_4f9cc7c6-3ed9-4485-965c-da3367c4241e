<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#1E40AF"/>
      <stop offset="100%" stop-color="#475569"/>
    </linearGradient>
    <linearGradient id="accentGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#1E40AF"/>
    </linearGradient>
    <linearGradient id="progressGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#7DD3FC"/>
    </linearGradient>

    <!-- 图标定义 - 统一的勾线风格 -->
    <!-- Chapter 1 (Current Chapter) - 目标/成就图标 -->
    <symbol id="iconTarget" viewBox="0 0 24 24">
      <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z" fill="none" stroke="#3B82F6" stroke-width="2"/>
      <circle cx="12" cy="12" r="4" fill="none" stroke="#3B82F6" stroke-width="2"/>
    </symbol>

    <!-- Chapter 2 - 数据/图表图标 -->
    <symbol id="iconChart" viewBox="0 0 24 24">
      <path d="M4 20h16V4H4v16zm2-2v-6h2v6H6zm4 0v-8h2v8h-2zm4 0v-4h2v4h-2z" fill="none" stroke="#64748B" stroke-width="2"/>
    </symbol>

    <!-- Chapter 3 - 想法/洞察图标 -->
    <symbol id="iconIdea" viewBox="0 0 24 24">
      <path d="M12 2C8.134 2 5 5.134 5 9c0 2.386 1.191 4.502 3 5.732V17h8v-2.268c1.809-1.23 3-3.346 3-5.732 0-3.866-3.134-7-7-7zM8 19h8v2H8v-2z" fill="none" stroke="#64748B" stroke-width="2"/>
    </symbol>

    <!-- Chapter 4 - 规划/启动图标 -->
    <symbol id="iconRocket" viewBox="0 0 24 24">
      <path d="M12 2c-1.1 0-2 .9-2 2v2H8v2h2v4H8v2h2v2c0 1.1.9 2 2 2s2-.9 2-2v-2h2v-2h-2v-4h2V8h-2V4c0-1.1-.9-2-2-2zM9 20v-2h6v2H9zm0-14h6V4H9v2z" fill="none" stroke="#64748B" stroke-width="2"/>
    </symbol>

    <!-- Chapter 5 - 总结/文档图标 -->
    <symbol id="iconDocument" viewBox="0 0 24 24">
      <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm0 4.5L17.5 9H14V6.5z" fill="none" stroke="#64748B" stroke-width="2"/>
    </symbol>

    <!-- 阴影滤镜定义 -->
    <filter id="shadowFilter" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset result="offOut" in="SourceAlpha" dx="0" dy="4"/>
      <feGaussianBlur result="blurOut" in="offOut" stdDeviation="3"/>
      <feColorMatrix result="matrixOut" in="blurOut" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.1 0"/>
      <feOffset result="offOut2" in="SourceAlpha" dx="0" dy="2"/>
      <feGaussianBlur result="blurOut2" in="offOut2" stdDeviation="2"/>
      <feColorMatrix result="matrixOut2" in="blurOut2" type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.06 0"/>
      <feMerge>
        <feMergeNode in="matrixOut"/>
        <feMergeNode in="matrixOut2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <style>
    /* 全局字体定义 */
    .font-primary { font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif; }
    .font-secondary { font-family: 'Source Han Sans CN', 'Noto Sans CJK SC', sans-serif; }
    .font-accent { font-family: 'Times New Roman', serif; }

    /* 颜色定义 */
    .fill-background { fill: #F8FAFC; }
    .fill-primary { fill: #1E40AF; }
    .fill-secondary { fill: #475569; }
    .fill-accent { fill: #3B82F6; }
    .fill-text-primary { fill: #1E293B; }
    .fill-text-secondary { fill: #64748B; }
    .fill-text-light { fill: #94A3B8; }
    .fill-card-background { fill: #FFFFFF; }
    .stroke-card-border { stroke: #BAE6FD; }

    /* 字体大小定义 */
    .text-hero-title { font-size: 72px; }
    .text-main-title { font-size: 56px; }
    .text-section-title { font-size: 36px; }
    .text-content-title { font-size: 28px; }
    .text-body-text { font-size: 22px; }
    .text-small-text { font-size: 16px; }
    .text-caption { font-size: 14px; }

    /* 字体粗细定义 */
    .font-normal { font-weight: 400; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }

    /* 卡片样式基础 */
    .card-base {
      fill: #FFFFFF;
      stroke: #BAE6FD;
      stroke-width: 1px;
      rx: 12; /* border-radius */
      ry: 12; /* border-radius */
      filter: url(#shadowFilter); /* 应用阴影 */
    }
  </style>

  <!-- 背景 -->
  <rect x="0" y="0" width="1920" height="1080" class="fill-background"/>

  <!-- 装饰性几何元素 - 右上角，使用强调色透明度渐变 -->
  <path d="M1920 0L1720 0C1720 0 1800 100 1920 200V0Z" class="fill-accent" opacity="0.1"/>
  <path d="M1920 0L1680 0C1680 0 1780 150 1920 250V0Z" class="fill-primary" opacity="0.05"/>

  <!-- 顶部区域 -->
  <g id="header">
    <!-- Logo占位符 -->
    <rect x="80" y="60" width="120" height="40" class="fill-primary" rx="8" ry="8"/>
    <text x="140" y="87" text-anchor="middle" class="font-primary text-small-text font-bold" fill="#FFFFFF">
      {logo_url}
    </text>

    <!-- 主标题和副标题 -->
    <text x="960" y="180" text-anchor="middle" class="font-primary text-main-title font-bold fill-text-primary">
      <tspan x="960" y="180">{title}</tspan>
      <tspan x="960" y="240" class="text-content-title font-normal fill-text-secondary">
        {subtitle}
      </tspan>
    </text>
  </g>

  <!-- 主内容区域 - 目录结构和核心要点展示 -->
  <g id="main-content">
    <!-- 目录列表 - 左侧 -->
    <g id="directory-list">
      <text x="160" y="380" class="font-primary text-section-title font-semibold fill-text-primary">
        <tspan>目录导航</tspan>
      </text>

      <!-- 章节 1 (当前章节) -->
      <g id="chapter-1">
        <use xlink:href="#iconTarget" x="160" y="450" width="32" height="32"/>
        <text x="210" y="475" class="font-primary text-content-title font-bold fill-accent">
          <tspan>1. 成果回顾</tspan>
          <tspan x="210" y="505" class="text-small-text font-normal fill-text-secondary">
            Performance Review
          </tspan>
        </text>
      </g>

      <!-- 章节 2 -->
      <g id="chapter-2">
        <use xlink:href="#iconChart" x="160" y="555" width="32" height="32"/>
        <text x="210" y="580" class="font-primary text-content-title font-normal fill-text-secondary">
          <tspan>2. 数据分析</tspan>
          <tspan x="210" y="610" class="text-small-text font-normal fill-text-light">
            Data Analysis
          </tspan>
        </text>
      </g>

      <!-- 章节 3 -->
      <g id="chapter-3">
        <use xlink:href="#iconIdea" x="160" y="660" width="32" height="32"/>
        <text x="210" y="685" class="font-primary text-content-title font-normal fill-text-secondary">
          <tspan>3. 挑战和机遇</tspan>
          <tspan x="210" y="715" class="text-small-text font-normal fill-text-light">
            Challenges and Opportunities
          </tspan>
        </text>
      </g>

      <!-- 章节 4 -->
      <g id="chapter-4">
        <use xlink:href="#iconRocket" x="160" y="765" width="32" height="32"/>
        <text x="210" y="790" class="font-primary text-content-title font-normal fill-text-secondary">
          <tspan>4. 未来规划</tspan>
          <tspan x="210" y="820" class="text-small-text font-normal fill-text-light">
            Future Planning
          </tspan>
        </text>
      </g>

      <!-- 章节 5 -->
      <g id="chapter-5">
        <use xlink:href="#iconDocument" x="160" y="870" width="32" height="32"/>
        <text x="210" y="895" class="font-primary text-content-title font-normal fill-text-secondary">
          <tspan>5. 总结和展望</tspan>
          <tspan x="210" y="925" class="text-small-text font-normal fill-text-light">
            Summary and Outlook
          </tspan>
        </text>
      </g>
    </g>

    <!-- 核心视觉元素 / 数据亮点 - 右侧 -->
    <g id="key-visual">
      <!-- 大型数字突出核心要点 -->
      <text x="1400" y="600" text-anchor="middle" class="font-primary text-hero-title font-bold" fill="url(#accentGradient)">
        <tspan>50%</tspan>
      </text>
      <text x="1400" y="680" text-anchor="middle" class="font-primary text-section-title font-normal fill-text-secondary">
        <tspan>目标达成</tspan>
      </text>
      <text x="1400" y="720" text-anchor="middle" class="font-primary text-body-text font-normal fill-text-light">
        <tspan>Target Achieved</tspan>
      </text>

      <!-- 简洁的勾线图形化作为数据可视化占位符 -->
      <rect x="1100" y="380" width="600" height="200" class="card-base" />
      <text x="1400" y="480" text-anchor="middle" class="font-primary text-content-title font-semibold fill-text-secondary">
        <tspan>核心指标概览</tspan>
      </text>
      <text x="1400" y="520" text-anchor="middle" class="font-primary text-small-text font-normal fill-text-light">
        <tspan>Key Performance Indicators Overview</tspan>
      </text>
      <!-- 简单的柱状图模拟 -->
      <rect x="1150" y="420" width="80" height="80" fill="#BAE6FD" rx="5" ry="5"/>
      <rect x="1250" y="450" width="80" height="50" fill="#D0EEFF" rx="5" ry="5"/>
      <rect x="1350" y="400" width="80" height="100" fill="#A7D9F7" rx="5" ry="5"/>
      <rect x="1450" y="430" width="80" height="70" fill="#80C3ED" rx="5" ry="5"/>
      <rect x="1550" y="460" width="80" height="40" fill="#59AEE3" rx="5" ry="5"/>
    </g>
  </g>

  <!-- 底部区域 -->
  <g id="footer">
    <!-- 进度指示器 -->
    <rect x="80" y="1000" width="1760" height="8" rx="4" ry="4" class="fill-secondary" opacity="0.2"/>
    <!-- 当前进度 (2/10 = 20%) -->
    <rect x="80" y="1000" width="352" height="8" rx="4" ry="4" fill="url(#progressGradient)"/>

    <!-- 页码信息 -->
    <text x="960" y="1040" text-anchor="middle" class="font-primary text-caption font-normal fill-text-secondary">
      <tspan>页面 2 / 10</tspan>
    </text>

    <!-- 日期和作者信息 -->
    <text x="80" y="1040" class="font-primary text-caption font-normal fill-text-secondary">
      <tspan>{date}</tspan>
    </text>
    <text x="1840" y="1040" text-anchor="end" class="font-primary text-caption font-normal fill-text-secondary">
      <tspan>{author}</tspan>
    </text>
  </g>
</svg>