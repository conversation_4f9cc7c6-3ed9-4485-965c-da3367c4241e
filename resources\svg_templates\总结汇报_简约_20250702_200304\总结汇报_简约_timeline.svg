<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- 定义特斯拉红色系的透明度渐变，用于高亮和科技感效果 -->
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#E31937" stop-opacity="0.9"/>
      <stop offset="100%" stop-color="#E31937" stop-opacity="0.3"/>
    </linearGradient>
    <linearGradient id="redGradientSubtle" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#E31937" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#E31937" stop-opacity="0.1"/>
    </linearGradient>
  </defs>

  <style>
    /* 全局样式定义 */
    svg {
      background-color: black; /* 纯黑色背景 */
      font-family: 'Inter', 'Helvetica', 'Arial', sans-serif;
      color: white; /* 默认文本颜色 */
    }

    /* 字体大小和权重定义，参考设计规范，并根据Bento Grid风格调整 */
    .title-large {
      font-size: 56px; /* main_title */
      font-weight: 700; /* bold */
      fill: white;
      text-anchor: middle;
    }

    .subtitle-small {
      font-size: 28px; /* content_title, 调整为副标题大小 */
      font-weight: 400; /* normal */
      fill: #D1D5DB; /* 浅灰色，用于次要文本 */
      text-anchor: middle;
    }

    .section-title {
      font-size: 36px; /* section_title */
      font-weight: 700; /* bold */
      fill: white;
    }

    .content-title {
      font-size: 28px; /* content_title */
      font-weight: 600; /* semibold */
      fill: white;
    }

    .body-text {
      font-size: 22px; /* body_text */
      font-weight: 400; /* normal */
      fill: #D1D5DB; /* 浅灰色，用于正文 */
      line-height: 1.4; /* 标准行高 */
    }

    .small-text {
      font-size: 16px; /* small_text */
      font-weight: 400; /* normal */
      fill: #9CA3AF; /* 更浅的灰色，用于小字点缀 */
    }

    .caption-text {
      font-size: 14px; /* caption */
      font-weight: 400; /* normal */
      fill: #6B7280; /* 深灰色，用于说明文字 */
    }

    /* 时间轴线条样式 */
    .timeline-line {
      stroke: #4B5563; /* 深灰色，用于微妙的线条 */
      stroke-width: 2px;
    }

    /* 时间轴节点样式 */
    .timeline-node {
      fill: #E31937; /* 特斯拉红色，用于普通节点 */
      stroke: none;
    }

    /* 里程碑节点样式，使用渐变强调 */
    .timeline-node-milestone {
      fill: url(#redGradient); /* 渐变填充 */
      stroke: #E31937;
      stroke-width: 3px;
    }

    /* 内容卡片背景样式，模仿Bento Grid */
    .card-background {
      fill: rgba(255, 255, 255, 0.05); /* 半透明白色，营造磨砂感 */
      stroke: #4B5563; /* 微妙边框 */
      stroke-width: 1px;
      rx: 8px; /* 圆角 */
      ry: 8px;
    }

    /* 文本行间距，确保不重叠 */
    .text-line-spacing-dy {
      dy: 30; /* 最小30px的行间距 */
    }
  </style>

  <!-- 背景和微妙的高亮效果 -->
  <rect x="0" y="0" width="1920" height="1080" fill="black"/>
  <!-- 背景叠加一层红色渐变，增加科技感 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#redGradientSubtle)" opacity="0.05"/>

  <!-- 页面标题和副标题 -->
  <text x="960" y="100" class="title-large">
    <tspan>{title}</tspan>
  </text>
  <text x="960" y="155" class="subtitle-small">
    <tspan>{subtitle}</tspan>
  </text>

  <!-- 时间轴主线 (居中) -->
  <line x1="960" y1="220" x2="960" y2="1000" class="timeline-line"/>

  <!-- 时间轴事件节点和描述 -->

  <!-- 事件 1: 2021年 Q1 - 项目启动 -->
  <circle cx="960" cy="270" r="10" class="timeline-node"/>
  <rect x="1020" y="230" width="400" height="120" class="card-background"/>
  <text x="1040" y="260" class="content-title">
    <tspan>2021年 Q1</tspan>
    <tspan x="1040" dy="30" class="body-text">项目启动和需求调研</tspan>
    <tspan x="1040" dy="30" class="small-text">Project Kick-off and Requirement Analysis</tspan>
  </text>
  <line x1="970" y1="270" x2="1020" y2="270" stroke="#E31937" stroke-width="1"/>

  <!-- 事件 2: 2021年 Q3 - 技术选型完成 -->
  <circle cx="960" cy="420" r="10" class="timeline-node"/>
  <rect x="500" y="380" width="400" height="120" class="card-background"/>
  <text x="520" y="410" class="content-title">
    <tspan>2021年 Q3</tspan>
    <tspan x="520" dy="30" class="body-text">核心技术选型完成</tspan>
    <tspan x="520" dy="30" class="small-text">Core Technology Stack Finalized</tspan>
  </text>
  <line x1="950" y1="420" x2="900" y2="420" stroke="#E31937" stroke-width="1"/>

  <!-- 事件 3: 2022年 Q1 - 里程碑：原型发布 (特殊标注) -->
  <circle cx="960" cy="570" r="15" class="timeline-node-milestone"/>
  <rect x="1020" y="530" width="400" height="150" class="card-background"/>
  <text x="1040" y="560" class="section-title" fill="#E31937">
    <tspan>2022年 Q1</tspan>
  </text>
  <text x="1040" y="595" class="body-text">
    <tspan>里程碑：内部原型发布</tspan>
    <tspan x="1040" dy="30" class="small-text">Milestone: Internal Prototype Release</tspan>
    <tspan x="1040" dy="30" class="caption-text">功能验证和用户反馈收集</tspan>
  </text>
  <line x1="975" y1="570" x2="1020" y2="570" stroke="#E31937" stroke-width="2"/>

  <!-- 事件 4: 2022年 Q4 - Beta测试启动 -->
  <circle cx="960" cy="720" r="10" class="timeline-node"/>
  <rect x="500" y="680" width="400" height="120" class="card-background"/>
  <text x="520" y="710" class="content-title">
    <tspan>2022年 Q4</tspan>
    <tspan x="520" dy="30" class="body-text">封闭Beta测试启动</tspan>
    <tspan x="520" dy="30" class="small-text">Closed Beta Testing Initiated</tspan>
  </text>
  <line x1="950" y1="720" x2="900" y2="720" stroke="#E31937" stroke-width="1"/>

  <!-- 事件 5: 2023年 Q2 - 正式发布 (特殊标注) -->
  <circle cx="960" cy="870" r="15" class="timeline-node-milestone"/>
  <rect x="1020" y="830" width="400" height="150" class="card-background"/>
  <text x="1040" y="860" class="section-title" fill="#E31937">
    <tspan>2023年 Q2</tspan>
  </text>
  <text x="1040" y="895" class="body-text">
    <tspan>里程碑：产品正式发布</tspan>
    <tspan x="1040" dy="30" class="small-text">Milestone: Official Product Launch</tspan>
    <tspan x="1040" dy="30" class="caption-text">市场推广和用户增长</tspan>
  </text>
  <line x1="975" y1="870" x2="1020" y2="870" stroke="#E31937" stroke-width="2"/>

  <!-- Logo占位符 (左上角) -->
  <rect x="80" y="60" width="150" height="40" fill="rgba(255, 255, 255, 0.1)" rx="5" ry="5"/>
  <text x="155" y="85" text-anchor="middle" class="small-text" fill="#D1D5DB">
    <tspan>{logo_url}</tspan>
  </text>

  <!-- 作者和日期占位符 (右下角) -->
  <text x="1840" y="1020" text-anchor="end" class="caption-text">
    <tspan>{author} | {date}</tspan>
  </text>

  <!-- 装饰性元素和数据强调 -->
  <line x1="80" y1="180" x2="250" y2="180" stroke="#4B5563" stroke-width="1"/>
  <line x1="1670" y1="180" x2="1840" y2="180" stroke="#4B5563" stroke-width="1"/>

  <!-- 超大数字强调核心要点 -->
  <text x="150" y="380" class="title-large" fill="url(#redGradient)">
    <tspan>2023</tspan>
  </text>
  <text x="150" y="420" class="body-text" fill="#D1D5DB">
    <tspan>成果之年</tspan>
  </text>
  <text x="150" y="450" class="small-text" fill="#9CA3AF">
    <tspan>Year of Achievements</tspan>
  </text>

  <!-- 另一个Bento Grid风格的卡片，用于未来展望或总结 -->
  <rect x="1500" y="750" width="340" height="230" class="card-background"/>
  <text x="1520" y="780" class="content-title">
    <tspan>未来展望</tspan>
    <tspan x="1520" dy="30" class="small-text">Future Outlook</tspan>
  </text>
  <text x="1520" y="830" class="body-text">
    <tspan>持续创新和市场拓展</tspan>
    <tspan x="1520" dy="30" class="body-text">优化用户体验和性能</tspan>
    <tspan x="1520" dy="30" class="body-text">构建生态系统和伙伴关系</tspan>
  </text>
  <!-- 叠加一层渐变，增加科技感 -->
  <rect x="1500" y="750" width="340" height="230" fill="url(#redGradientSubtle)" opacity="0.1" rx="8" ry="8"/>

</svg>