<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Global Styles */
    :root {
      --background-dark: #1A1A1A; /* Very dark grey for background */
      --text-light: #F0F0F0; /* Light grey for primary text */
      --text-medium: #A0A0A0; /* Medium grey for secondary text */
      --text-red: #E31937; /* Tesla Red for accents and highlights */
      --accent-red-light: #E3193730; /* Red with transparency for subtle glows */
      --accent-red-lighter: #E3193710; /* Red with more transparency */
      --border-color: #333333; /* Dark grey for card borders */
      --card-background: #222222; /* Slightly lighter dark grey for card fill */
      --font-primary: 'Inter', Helvetica, Arial, sans-serif;
      --font-secondary: 'SF Pro Display', system-ui, sans-serif;
      --font-accent: 'Poppins', sans-serif;
    }

    /* General text styles */
    .text-light { fill: var(--text-light); }
    .text-medium { fill: var(--text-medium); }
    .text-red { fill: var(--text-red); }

    /* Font sizes based on design norms, adjusted for dark background */
    .main-title { font-size: 56px; font-weight: 700; font-family: var(--font-primary); }
    .section-title { font-size: 36px; font-weight: 700; font-family: var(--font-primary); }
    .content-title { font-size: 28px; font-weight: 600; font-family: var(--font-primary); }
    .body-text { font-size: 22px; font-weight: 400; font-family: var(--font-primary); line-height: 1.4; }
    .small-text { font-size: 16px; font-weight: 400; font-family: var(--font-primary); }
    .caption-text { font-size: 14px; font-weight: 300; font-family: var(--font-secondary); }

    /* Specific styles for this template */
    .contrast-card {
      fill: var(--card-background);
      stroke: var(--border-color);
      stroke-width: 1px;
    }

    .key-metric-number {
      font-size: 80px; /* Extra large for emphasis */
      font-weight: 900; /* Black weight for maximum impact */
      font-family: var(--font-accent);
      fill: var(--text-red);
    }

    .chinese-bold {
      font-weight: 700; /* Bold for Chinese text */
    }

    .red-gradient-fade {
      opacity: 0.2; /* Base opacity for decorative elements */
      fill: url(#redGradient);
    }

    /* Important: All x, y, dx, dy values are explicit numbers to prevent overlap */
    /* tspan dy values are set to 30px to ensure sufficient line spacing for body text */

  </style>

  <defs>
    <!-- Red gradient for tech feel (Tesla Red transparency) -->
    <!-- Used for subtle background elements to enhance tech aesthetic -->
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="var(--text-red)" stop-opacity="0" />
      <stop offset="50%" stop-color="var(--text-red)" stop-opacity="0.5" />
      <stop offset="100%" stop-color="var(--text-red)" stop-opacity="0" />
    </linearGradient>

    <!-- Simple outline icon for improvement/check -->
    <symbol id="icon-check" viewBox="0 0 24 24">
      <path d="M20 6L9 17L4 12" stroke="var(--text-red)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

    <!-- Simple outline icon for alert/difference -->
    <symbol id="icon-alert" viewBox="0 0 24 24">
      <path d="M10.29 3.86L1.82 18a2 2 0 001.71 3h16.94a2 2 0 001.71-3L13.71 3.86a2 2 0 00-3.42 0z" stroke="var(--text-red)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="12" y1="9" x2="12" y2="13" stroke="var(--text-red)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="12" y1="17" x2="12.01" y2="17" stroke="var(--text-red)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </symbol>

  </defs>

  <!-- Background -->
  <rect x="0" y="0" width="1920" height="1080" fill="var(--background-dark)" />

  <!-- Decorative Gradient Elements (mimicking tech feel) -->
  <rect x="0" y="100" width="1920" height="50" class="red-gradient-fade" />
  <rect x="0" y="900" width="1920" height="50" class="red-gradient-fade" />

  <!-- Header Section -->
  <g id="header">
    <text x="960" y="120" text-anchor="middle" class="main-title text-light chinese-bold">项目阶段性总结汇报</text>
    <text x="960" y="165" text-anchor="middle" class="body-text text-medium">Project Phase Summary Report</text>
  </g>

  <!-- Main Content Area: Left vs Right Comparison -->
  <g id="comparison-section">
    <!-- Left Card: Current Status -->
    <rect x="120" y="240" width="780" height="600" rx="0" ry="0" class="contrast-card" />
    <text x="510" y="290" text-anchor="middle" class="section-title text-light chinese-bold">现状分析</text>
    <text x="510" y="325" text-anchor="middle" class="small-text text-medium">Current State Analysis</text>

    <!-- Left Card Content -->
    <g id="left-content">
      <text x="180" y="390" class="content-title text-light">执行情况 Summary</text>
      <text x="180" y="440" class="body-text text-medium">
        <tspan x="180" dy="0">· 关键里程碑达成率高，项目进度符合预期。</tspan>
        <tspan x="180" dy="30">· 资源利用效率良好，团队协作紧密。</tspan>
        <tspan x="180" dy="30">· 用户反馈积极，产品功能稳定。</tspan>
      </text>

      <text x="180" y="590" class="content-title text-light">核心指标 Key Metrics</text>
      <text x="180" y="660" class="key-metric-number">95%</text>
      <text x="350" y="650" class="body-text text-medium">
        <tspan x="350" dy="0">任务完成率</tspan>
        <tspan x="350" dy="30">Task Completion Rate</tspan>
      </text>

      <text x="180" y="750" class="key-metric-number">8.5</text>
      <text x="350" y="740" class="body-text text-medium">
        <tspan x="350" dy="0">用户满意度（满分10）</tspan>
        <tspan x="350" dy="30">User Satisfaction (out of 10)</tspan>
      </text>
    </g>

    <!-- Right Card: Problem Analysis and Improvement -->
    <rect x="1020" y="240" width="780" height="600" rx="0" ry="0" class="contrast-card" />
    <text x="1410" y="290" text-anchor="middle" class="section-title text-light chinese-bold">问题分析和改进</text>
    <text x="1410" y="325" text-anchor="middle" class="small-text text-medium">Problem Analysis and Improvement</text>

    <!-- Right Card Content -->
    <g id="right-content">
      <text x="1080" y="390" class="content-title text-light">存在问题 Identified Problems</text>
      <text x="1080" y="440" class="body-text text-medium">
        <tspan x="1080" dy="0">· 部分模块性能有待优化。</tspan>
        <tspan x="1080" dy="30">· 跨部门沟通效率仍需提升。</tspan>
        <tspan x="1080" dy="30">· 风险预警机制不够完善。</tspan>
      </text>

      <text x="1080" y="590" class="content-title text-light">改进建议 Improvement Suggestions</text>
      <text x="1080" y="640" class="body-text text-medium">
        <tspan x="1080" dy="0">· <use href="#icon-check" x="1050" y="620" width="24" height="24" /> 引入性能监控工具，定期进行压力测试。</tspan>
        <tspan x="1080" dy="30">· <use href="#icon-check" x="1050" y="650" width="24" height="24" /> 建立定期跨部门例会机制，强化信息共享。</tspan>
        <tspan x="1080" dy="30">· <use href="#icon-check" x="1050" y="680" width="24" height="24" /> 完善风险矩阵，定期评估潜在风险。</tspan>
      </text>

      <!-- Difference Emphasis Element (Bento Grid style) -->
      <g id="difference-emphasis">
        <rect x="900" y="440" width="120" height="120" fill="var(--accent-red-lighter)" />
        <text x="960" y="480" text-anchor="middle" class="section-title text-red chinese-bold">差异</text>
        <text x="960" y="515" text-anchor="middle" class="small-text text-medium">Difference</text>
        <use href="#icon-alert" x="948" y="540" width="24" height="24" />
      </g>
    </g>
  </g>

  <!-- Conclusion Area -->
  <g id="conclusion-section">
    <rect x="120" y="870" width="1680" height="150" fill="var(--text-red)" opacity="0.1" />
    <rect x="120" y="870" width="1680" height="150" stroke="var(--text-red)" stroke-width="2" rx="0" ry="0" />
    <text x="960" y="930" text-anchor="middle" class="section-title text-light chinese-bold">结论和展望 Conclusion and Outlook</text>
    <text x="960" y="970" text-anchor="middle" class="body-text text-light">
      <tspan x="960" dy="0">项目整体表现良好，但在细节优化和风险管理方面仍有提升空间。</tspan>
      <tspan x="960" dy="30">团队将持续改进，确保项目高质量完成。</tspan>
    </text>
  </g>

  <!-- Footer (Page Info) -->
  <g id="footer">
    <text x="1800" y="1050" text-anchor="end" class="caption-text text-medium">页面 7/10</text>
    <text x="120" y="1050" class="caption-text text-medium">日期: {date}</text>
    <text x="300" y="1050" class="caption-text text-medium">作者: {author}</text>
  </g>

  <!-- Placeholder for Logo (Top Left) -->
  <g id="logo-placeholder">
    <!-- A simple geometric shape as placeholder for logo -->
    <rect x="80" y="60" width="60" height="60" fill="var(--text-red)" />
    <text x="110" y="100" text-anchor="middle" class="small-text" fill="var(--text-light)">Logo</text>
    <!-- To use an actual image, replace the rect and text with: -->
    <!-- <image href="{logo_url}" x="80" y="60" width="80" height="80" /> -->
  </g>

</svg>