"""
Tavily搜索API模块
"""
import logging
import json
import httpx
import asyncio
import re
import base64
from typing import Dict, Any, Optional, List, Union, Tuple
from backend.utils.key_manager import KeyManager

# QR码检测相关导入
try:
    import cv2
    import numpy as np
    from pyzbar import pyzbar
    QR_DETECTION_AVAILABLE = True
except ImportError:
    QR_DETECTION_AVAILABLE = False

# 添加官方TavilyClient导入
try:
    from tavily import TavilyClient
    TAVILY_CLIENT_AVAILABLE = True
except ImportError:
    TAVILY_CLIENT_AVAILABLE = False
    TavilyClient = None
    logger = logging.getLogger(__name__)
    logger.warning("官方tavily包未安装，将使用自定义API调用方式")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Tavily API配置
TAVILY_API_URL = "https://api.tavily.com/search"

# Tavily Extract API配置
TAVILY_EXTRACT_API_URL = "https://api.tavily.com/extract"

# 搜索参数类型定义
class TavilySearchParams:
    """Tavily搜索参数"""
    def __init__(
        self,
        query: str,
        search_depth: str = "basic",
        include_domains: Optional[List[str]] = None,
        exclude_domains: Optional[List[str]] = None,
        max_results: int = 10,
        include_answer: bool = False,
        include_images: bool = False,
        include_raw_content: bool = False,
        safesearch: bool = True,
    ):
        self.query = query
        self.search_depth = search_depth
        self.include_domains = include_domains
        self.exclude_domains = exclude_domains
        self.max_results = max_results
        self.include_answer = include_answer
        self.include_images = include_images
        self.include_raw_content = include_raw_content
        self.safesearch = safesearch
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        params = {
            "query": self.query,
            "search_depth": self.search_depth,
            "max_results": self.max_results,
            "include_answer": self.include_answer,
            "include_images": self.include_images,
            "include_raw_content": self.include_raw_content,
            "safesearch": self.safesearch,
        }
        
        if self.include_domains:
            params["include_domains"] = self.include_domains
            
        if self.exclude_domains:
            params["exclude_domains"] = self.exclude_domains
            
        return params


async def get_tavily_api_key() -> str:
    """
    从远程服务获取Tavily API密钥（按需获取）
    
    Returns:
        str: Tavily API密钥
    """
    logger.info("正在按需获取Tavily API密钥（仅在执行研究任务时调用）")
    try:
        key_manager = KeyManager()
        api_key = await key_manager.get_key_from_api("tavily")
        
        if not api_key:
            logger.error("从KeyManager获取Tavily API密钥失败")
            raise Exception("获取Tavily API密钥失败")
        
        logger.info("Tavily API密钥按需获取成功")
        # 可选：打印完整密钥用于调试
        # logger.info(f"完整API密钥值: {api_key}")
        # print(f"[DEBUG] Tavily完整API密钥: {api_key}")
        
        return api_key
            
    except Exception as e:
        logger.error(f"获取Tavily API密钥时出错: {str(e)}")
        raise


async def tavily_search(params: Union[Dict[str, Any], TavilySearchParams]) -> Dict[str, Any]:
    """
    调用Tavily API执行搜索
    
    Args:
        params: 搜索参数，可以是字典或TavilySearchParams对象
        
    Returns:
        Dict[str, Any]: 搜索结果
    """
    try:
        # 转换参数为字典格式
        if isinstance(params, TavilySearchParams):
            search_params = params.to_dict()
        else:
            search_params = params
        
        # 按需获取API密钥（只在实际执行搜索时获取，非后端启动时）
        api_key = await get_tavily_api_key()
        
        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        logger.info(f"执行Tavily搜索: {search_params['query']}")
        
        # 发送请求
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                TAVILY_API_URL,
                headers=headers,
                json=search_params
            )
            
            if response.status_code != 200:
                logger.error(f"Tavily搜索请求失败: HTTP状态码 {response.status_code}")
                return {
                    "error": True,
                    "message": f"搜索请求失败: HTTP状态码 {response.status_code}",
                    "data": None
                }
                
            result = response.json()
            logger.info(f"Tavily搜索完成: 找到 {len(result.get('results', []))} 个结果")
            
            # 搜索成功后，增加API使用次数
            key_manager = KeyManager()
            usage_result = await key_manager.increase_key_usage_api(api_key, "tavily")
            if not usage_result.get("success"):
                logger.warning(f"增加Tavily API使用次数失败: {usage_result.get('message')}")
            else:
                logger.info("Tavily API使用次数增加成功")
            
            return {
                "error": False,
                "data": result
            }
    except Exception as e:
        logger.error(f"执行Tavily搜索时出错: {str(e)}")
        return {
            "error": True,
            "message": f"搜索失败: {str(e)}",
            "data": None
        }


def format_search_results(results: Dict[str, Any]) -> str:
    """
    格式化搜索结果为字符串
    
    Args:
        results: 搜索结果字典
        
    Returns:
        str: 格式化后的搜索结果
    """
    if results.get("error", False):
        return f"搜索失败: {results.get('message', '未知错误')}"
    
    data = results.get("data", {})
    query = data.get("query", "")
    search_results = data.get("results", [])
    answer = data.get("answer", "")
    
    # 构建格式化结果
    formatted = f"## 搜索结果: {query}\n\n"
    
    # 如果有AI答案，添加到结果中
    if answer:
        formatted += f"### AI答案\n{answer}\n\n"
    
    # 添加搜索结果
    formatted += "### 内容源\n\n"
    
    for i, result in enumerate(search_results, 1):
        title = result.get("title", "无标题")
        url = result.get("url", "")
        content = result.get("content", "无内容")
        
        formatted += f"{i}. **{title}**\n"
        formatted += f"   URL: {url}\n"
        formatted += f"   摘要: {content}\n\n"
    
    return formatted


# Tavily搜索工具提示词模板
TAVILY_TOOL_PROMPT = """
## tavily_search
Description: Search the web for real-time information using Tavily search engine.
Parameters:
- query: (required) The search query to look up on the web
- search_depth: (optional) The depth of search, can be "basic" (default) or "advanced"
- max_results: (optional) Maximum number of results to return, default is a reasonable number
Usage:
```xml
<tavily_search>
<query>search query here</query>
<search_depth>basic</search_depth>
<max_results>10</max_results>
</tavily_search>
```

When you need to search for current information, use this tool. 
After receiving search results, analyze them and provide a comprehensive answer based on the information found.
Always cite your sources when providing information from the search results.
"""

# Tavily搜索工具使用示例
TAVILY_TOOL_EXAMPLE = """
## Example: Searching for current information
```xml
<tavily_search>
<query>latest developments in AI technology</query>
<search_depth>basic</search_depth>
<max_results>5</max_results>
</tavily_search>
```
"""

def get_tavily_prompt() -> str:
    """
    获取Tavily搜索工具提示词
    
    Returns:
        str: 提示词字符串
    """
    return f"""
WEB SEARCH CAPABILITY

You have access to web search functionality through the Tavily search engine.
This allows you to find current and real-time information about any topic.

# How to Use Web Search

{TAVILY_TOOL_PROMPT}

# Example of Using Web Search

{TAVILY_TOOL_EXAMPLE}

Remember to always cite your sources when providing information from search results.
"""


async def handle_tavily_search_call(tool_call_content: str) -> Dict[str, Any]:
    """
    处理Tavily搜索工具调用
    
    Args:
        tool_call_content: 工具调用内容，XML格式
        
    Returns:
        Dict[str, Any]: 搜索处理结果
    """
    try:
        # 解析XML格式的搜索请求
        search_params = parse_tavily_search_xml(tool_call_content)
        
        if not search_params or "query" not in search_params:
            return {
                "error": True,
                "message": "搜索参数解析失败或缺少查询内容",
                "data": None
            }
        
        # 执行搜索
        result = await tavily_search(search_params)
        
        # 如果搜索失败，返回错误信息
        if result.get("error", False):
            return result
        
        # 获取原始数据
        raw_data = result.get("data", {})
        query = raw_data.get("query", "")
        search_results = raw_data.get("results", [])
        answer = raw_data.get("answer", "")
        
        # 格式化搜索结果为Markdown（用于备用显示）
        formatted_results = format_search_results(result)
        
        # 处理搜索结果，确保每个结果都有必要的字段
        processed_results = []
        for item in search_results:
            processed_item = {
                "title": item.get("title", "无标题"),
                "url": item.get("url", ""),
                "content": item.get("content", item.get("snippet", "无内容")),
                "score": item.get("score", 0.0),
            }
            
            # 为每个结果添加favicon
            if processed_item["url"]:
                try:
                    from urllib.parse import urlparse
                    parsed_url = urlparse(processed_item["url"])
                    domain = parsed_url.netloc
                    processed_item["favicon"] = f"https://www.google.com/s2/favicons?domain={domain}&sz=32"
                except:
                    processed_item["favicon"] = "https://www.google.com/s2/favicons?domain=example.com&sz=32"
            else:
                processed_item["favicon"] = "https://www.google.com/s2/favicons?domain=example.com&sz=32"
                
            processed_results.append(processed_item)
        
        # 返回结构化结果，同时包含前端所需的格式和备用格式
        return {
            "error": False,
            "query": query,
            "results": processed_results,  # 前端WebSearchView直接使用的数组
            "answer": answer,
            "formatted": formatted_results,  # Markdown格式备用
            "raw_data": raw_data  # 原始数据备用
        }
    except Exception as e:
        logger.error(f"处理Tavily搜索调用时出错: {str(e)}")
        return {
            "error": True,
            "message": f"搜索处理失败: {str(e)}",
            "data": None
        }


def parse_tavily_search_xml(xml_content: str) -> Dict[str, Any]:
    """
    解析XML格式的Tavily搜索请求
    
    Args:
        xml_content: XML格式的搜索请求内容
        
    Returns:
        Dict[str, Any]: 解析后的搜索参数
    """
    try:
        # 提取查询内容
        query_match = re.search(r'<query>(.*?)</query>', xml_content, re.DOTALL)
        if not query_match:
            logger.error("未找到查询内容")
            return {}
        
        query = query_match.group(1).strip()
        
        # 提取搜索深度
        search_depth = "basic"  # 默认值
        depth_match = re.search(r'<search_depth>(.*?)</search_depth>', xml_content, re.DOTALL)
        if depth_match:
            depth = depth_match.group(1).strip().lower()
            if depth in ["basic", "advanced"]:
                search_depth = depth
        
        # 提取最大结果数量
        max_results = 10  # 默认值
        max_results_match = re.search(r'<max_results>(\d+)</max_results>', xml_content)
        if max_results_match:
            try:
                max_results = int(max_results_match.group(1).strip())
            except ValueError:
                pass
        
        # 构建搜索参数
        search_params = {
            "query": query,
            "search_depth": search_depth,
            "max_results": max_results,
            "include_answer": True,  # 默认包含AI答案
            "safesearch": True       # 默认启用安全搜索
        }
        
        return search_params
    except Exception as e:
        logger.error(f"解析Tavily搜索XML时出错: {str(e)}")
        return {}


def is_tavily_search_call(content: str) -> bool:
    """
    检查内容是否为Tavily搜索工具调用
    
    Args:
        content: 消息内容
        
    Returns:
        bool: 是否为Tavily搜索调用
    """
    # 检查是否包含tavily_search标签
    search_tag_pattern = r'<tavily_search>.*?</tavily_search>'
    return bool(re.search(search_tag_pattern, content, re.DOTALL))


def extract_tavily_search_call(content: str) -> Tuple[str, str]:
    """
    从内容中提取Tavily搜索调用和其余内容
    
    Args:
        content: 完整消息内容
        
    Returns:
        Tuple[str, str]: (搜索调用内容, 其余内容)
    """
    # 提取tavily_search标签内容
    search_pattern = r'(<tavily_search>.*?</tavily_search>)'
    match = re.search(search_pattern, content, re.DOTALL)
    
    if not match:
        return "", content
    
    search_call = match.group(1)
    
    # 从原始内容中移除搜索调用部分
    remaining = content.replace(search_call, "").strip()
    
    return search_call, remaining


async def extract_images_from_result(research_result: str) -> Dict[str, Any]:
    """
    从研究结果中提取URL，使用Tavily Extract API获取网页中的图片URL，
    并将图片信息追加到原始结果中
    
    Args:
        research_result: 研究结果文本，包含markdown格式和URL链接
        
    Returns:
        Dict[str, Any]: 包含原始结果和提取到的图片信息
    """
    try:
        logger.info("开始从研究结果中提取URL和图片")
        
        # 提取URL（排除图片和PDF）
        urls = extract_urls_from_text(research_result)
        
        if not urls:
            logger.info("未找到可处理的URL")
            return {
                "success": True,
                "original_result": research_result,
                "extracted_images": [],
                "message": "未找到可处理的URL"
            }
        
        # 限制最多提取3个链接的图片
        urls = urls[:5]
        
        logger.info(f"找到 {len(urls)} 个URL（限制前5个），开始提取图片")
        
        # 从每个URL提取图片
        all_images = []
        successful_extractions = 0
        qr_filtered_count = 0
        
        for url in urls:
            try:
                images = await extract_images_from_url(url)
                if images:
                    # 过滤掉二维码图片
                    filtered_images = []
                    for img_url in images:
                        try:
                            is_qr = await is_qr_code_image(img_url)
                            if not is_qr:
                                filtered_images.append({
                                    "source_url": url,
                                    "image_url": img_url,
                                    "description": f"来自 {url} 的图片"
                                })
                            else:
                                qr_filtered_count += 1
                                logger.info(f"过滤掉二维码图片: {img_url}")
                        except Exception as e:
                            # 如果QR检测失败，默认保留图片
                            logger.debug(f"QR检测失败，保留图片 {img_url}: {str(e)}")
                            filtered_images.append({
                                "source_url": url,
                                "image_url": img_url,
                                "description": f"来自 {url} 的图片"
                            })
                    
                    all_images.extend(filtered_images)
                    successful_extractions += 1
            except Exception as e:
                logger.warning(f"从URL {url} 提取图片失败: {str(e)}")
                continue
        
        # 处理markdown格式的图片链接
        processed_result = process_markdown_images(research_result, all_images)
        
        logger.info(f"图片提取完成：从 {successful_extractions} 个URL中提取到 {len(all_images)} 张图片，过滤掉 {qr_filtered_count} 个二维码")
        
        return {
            "success": True,
            "original_result": research_result,
            "processed_result": processed_result,
            "extracted_images": all_images,
            "stats": {
                "total_urls": len(urls),
                "successful_extractions": successful_extractions,
                "total_images": len(all_images),
                "qr_filtered_count": qr_filtered_count
            }
        }
        
    except Exception as e:
        logger.error(f"提取图片过程中发生错误: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "original_result": research_result,
            "extracted_images": []
        }


def extract_urls_from_text(text: str) -> List[str]:
    """
    从文本中提取URL，排除图片和PDF类型的URL
    
    Args:
        text: 包含URL的文本
        
    Returns:
        List[str]: 过滤后的URL列表
    """
    # URL匹配正则表达式
    url_pattern = r'https?://[^\s\)]+(?:\([^\)]*\))?[^\s\)\]]*'
    urls = re.findall(url_pattern, text)
    
    # 过滤掉图片和PDF类型的URL
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico'}
    document_extensions = {'.pdf'}
    
    filtered_urls = []
    for url in urls:
        # 移除URL末尾的标点符号
        clean_url = url.rstrip('.,;:!?)')
        
        # 检查URL是否以图片或文档扩展名结尾
        url_lower = clean_url.lower()
        is_image = any(url_lower.endswith(ext) for ext in image_extensions)
        is_document = any(url_lower.endswith(ext) for ext in document_extensions)
        
        if not is_image and not is_document:
            filtered_urls.append(clean_url)
    
    # 去重并保持顺序
    seen = set()
    unique_urls = []
    for url in filtered_urls:
        if url not in seen:
            seen.add(url)
            unique_urls.append(url)
    
    return unique_urls


def _simple_qr_pattern_check(image_url: str) -> bool:
    """
    简单的URL模式检查，用于初步判断是否可能是二维码
    
    Args:
        image_url: 图片URL
        
    Returns:
        bool: 是否可能是二维码（通过URL模式判断）
    """
    url_lower = image_url.lower()
    qr_keywords = ['qr', 'qrcode', 'barcode', 'code', 'scan']
    
    # 检查URL中是否包含二维码相关关键词
    for keyword in qr_keywords:
        if keyword in url_lower:
            return True
    
    return False


async def is_qr_code_image(image_url: str) -> bool:
    """
    检测图片是否为二维码
    
    Args:
        image_url: 图片URL
        
    Returns:
        bool: 是否为二维码
    """
    # 首先进行简单的URL模式检查
    if _simple_qr_pattern_check(image_url):
        logger.info(f"通过URL模式识别为二维码: {image_url}")
        return True
    
    if not QR_DETECTION_AVAILABLE:
        logger.debug("QR码检测功能不可用，仅使用URL模式检查")
        return False
    
    try:
        # 下载图片
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(image_url, follow_redirects=True)
            if response.status_code != 200:
                logger.debug(f"无法下载图片进行QR码检测: {image_url}")
                return False
            
            # 将图片数据转换为numpy数组
            image_data = response.content
            nparr = np.frombuffer(image_data, np.uint8)
            
            # 使用OpenCV读取图片
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            if img is None:
                logger.debug(f"无法解析图片: {image_url}")
                return False
            
            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 使用pyzbar检测二维码
            decoded_objects = pyzbar.decode(gray)
            
            # 如果检测到二维码，返回True
            if decoded_objects:
                logger.info(f"检测到二维码: {image_url}")
                return True
            
            # 使用OpenCV的QRCodeDetector作为备选方法
            detector = cv2.QRCodeDetector()
            data, bbox, _ = detector.detectAndDecode(gray)
            
            if data:
                logger.info(f"通过OpenCV检测到二维码: {image_url}")
                return True
                
            return False
            
    except Exception as e:
        logger.debug(f"QR码检测过程中出错 {image_url}: {str(e)}")
        return False


async def extract_images_from_url(url: str) -> List[str]:
    """
    使用Tavily Extract API从指定URL提取图片URL
    
    Args:
        url: 要提取图片的网页URL
        
    Returns:
        List[str]: 提取到的图片URL列表
    """
    try:
        # 按需获取API密钥
        api_key = await get_tavily_api_key()
        
        logger.debug(f"正在从URL提取内容: {url}")
        
        # 使用官方TavilyClient（如果可用）
        if TAVILY_CLIENT_AVAILABLE and TavilyClient:
            try:
                # 使用官方客户端
                tavily_client = TavilyClient(api_key=api_key)
                result = tavily_client.extract(url, include_images=True)
                
                # 提取图片URL
                images = []
                for extraction_result in result.get("results", []):
                    if extraction_result.get("url") == url:
                        images.extend(extraction_result.get("images", []))
                        break
                
                logger.debug(f"从 {url} 提取到 {len(images)} 张图片（使用官方客户端）")
                return images
                
            except Exception as e:
                logger.warning(f"使用官方TavilyClient提取图片失败，回退到自定义API调用: {str(e)}")
                # 回退到自定义API调用
        
        # 自定义API调用（回退方案）
        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 请求数据
        payload = {"urls": [url]}
        
        # 发送请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                TAVILY_EXTRACT_API_URL,
                headers=headers,
                json=payload
            )
            
            if response.status_code != 200:
                logger.warning(f"Tavily Extract请求失败: HTTP状态码 {response.status_code}, URL: {url}")
                return []
                
            result = response.json()
            
            # 提取图片URL
            images = []
            for extraction_result in result.get("results", []):
                if extraction_result.get("url") == url:
                    images.extend(extraction_result.get("images", []))
                    break
            
            logger.debug(f"从 {url} 提取到 {len(images)} 张图片（使用自定义API调用）")
            return images
            
    except Exception as e:
        logger.warning(f"从URL {url} 提取图片时出错: {str(e)}")
        return []


def process_markdown_images(text: str, extracted_images: List[Dict[str, Any]]) -> str:
    """
    处理markdown中的图片链接，补充![]中括号的内容
    
    Args:
        text: 原始markdown文本
        extracted_images: 提取到的图片信息列表
        
    Returns:
        str: 处理后的markdown文本
    """
    if not extracted_images:
        return text
    
    # 匹配markdown图片语法 ![alt](url)
    img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
    
    def replace_image(match):
        alt_text = match.group(1)
        img_url = match.group(2)
        
        # 如果alt文本为空，尝试为其添加描述
        if not alt_text.strip():
            # 查找对应的图片信息
            for img_info in extracted_images:
                if img_info["image_url"] == img_url:
                    alt_text = f"来自 {img_info['source_url']} 的图片"
                    break
            
            # 如果还是没有找到，使用默认描述
            if not alt_text.strip():
                alt_text = "网页图片"
        
        return f"![{alt_text}]({img_url})"
    
    # 替换所有图片引用
    processed_text = re.sub(img_pattern, replace_image, text)
    
    # 如果有额外的图片没有在原文中引用，可以选择添加到文末
    # 这里暂时不自动添加，避免干扰原始内容结构
    
    return processed_text


def append_extracted_images_to_result(research_result: str, extracted_images: List[Dict[str, Any]]) -> str:
    """
    将提取到的图片信息追加到研究结果中
    
    Args:
        research_result: 原始研究结果
        extracted_images: 提取到的图片信息列表
        
    Returns:
        str: 包含图片信息的完整结果
    """
    if not extracted_images:
        return research_result
    
    # 在结果末尾添加图片部分
    result_with_images = research_result
    
    # 按来源URL分组图片
    images_by_source = {}
    for img_info in extracted_images:
        source_url = img_info["source_url"]
        if source_url not in images_by_source:
            images_by_source[source_url] = []
        images_by_source[source_url].append(img_info)
    
    # 添加图片部分
    result_with_images += "\n\n---\n\n## 相关图片\n\n"
    
    for source_url, images in images_by_source.items():
        result_with_images += f"### 来源：{source_url}\n\n"
        for i, img_info in enumerate(images, 1):
            result_with_images += f"{i}. ![{img_info['description']}]({img_info['image_url']})\n\n"
        result_with_images += "\n"
    
    return result_with_images 