{"set_name": "商业计划_商务_20250702_202325", "scenario": "商业计划", "style": "商务", "created_at": "2025-07-02T20:23:25.101044", "template_count": 9, "templates": [{"template_id": "商业计划_商务_cover", "type": "封面页", "filename": "商业计划_商务_cover.svg", "page_number": 1}, {"template_id": "商业计划_商务_agenda", "type": "目录页", "filename": "商业计划_商务_agenda.svg", "page_number": 2}, {"template_id": "商业计划_商务_section_divider", "type": "章节分隔页", "filename": "商业计划_商务_section_divider.svg", "page_number": 3}, {"template_id": "商业计划_商务_title_content", "type": "标题内容页", "filename": "商业计划_商务_title_content.svg", "page_number": 4}, {"template_id": "商业计划_商务_image_text", "type": "图文混排页", "filename": "商业计划_商务_image_text.svg", "page_number": 5}, {"template_id": "商业计划_商务_data_display", "type": "数据展示页", "filename": "商业计划_商务_data_display.svg", "page_number": 6}, {"template_id": "商业计划_商务_comparison", "type": "对比分析页", "filename": "商业计划_商务_comparison.svg", "page_number": 7}, {"template_id": "商业计划_商务_quote", "type": "引用页", "filename": "商业计划_商务_quote.svg", "page_number": 9}, {"template_id": "商业计划_商务_conclusion", "type": "总结页", "filename": "商业计划_商务_conclusion.svg", "page_number": 10}], "combination_config": {"scenario": {"scenario_type": "商业计划", "display_name": "商业计划", "description": "商业计划书、投资提案、战略规划", "visual_characteristics": {"emphasis_on": "逻辑严密、数据支撑", "layout_style": "商务正式", "decorative_elements": "商业图表、财务数据、流程图"}, "content_focus": ["市场分析", "财务预测", "风险评估"], "target_audience": "投资人、合作伙伴", "tone": "professional"}, "style": {"style_type": "商务", "display_name": "商务", "description": "专业正式，体现权威和可信度", "design_principles": {"layout": "规范布局、对称平衡", "elements": "正式图形、商务图标", "emphasis": "专业性、权威性"}, "visual_elements": {"shapes": "矩形、稳重几何形", "lines": "直线、规整边框", "decorations": "商务图标、数据图表"}, "typography": {"font_style": "正式字体", "weight": "中粗", "spacing": "标准间距"}}, "colors": {"primary": "#1E40AF", "secondary": "#475569", "accent": "#3B82F6", "background": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B"}, "fusion_rules": {"layout_priority": "scenario", "color_adaptation": "balanced", "typography_blend": "harmonious", "visual_emphasis": []}, "aesthetic_enhancements": {"typography_refinements": {"font_pairing": "complementary", "hierarchy_enhancement": true, "readability_optimization": true}, "layout_improvements": {"golden_ratio_application": true, "visual_balance_optimization": true, "spacing_harmonization": true}, "color_harmony": {"contrast_optimization": true, "accessibility_compliance": true, "emotional_resonance": true}, "visual_elements": {"icon_style_consistency": true, "decorative_element_coordination": true, "brand_element_integration": true}}}, "design_specification": {"theme_selection": {"primary_style": "商务风格", "scenario_adaptation": "商业计划场景优化", "visual_theme": "黑底特斯拉红高亮现代商务风格商业计划模板", "design_philosophy": "结合投资人、合作伙伴需求的专业正式、简洁有力，体现权威和可信度，并融入科技感与视觉冲击力。", "fusion_strategy": "场景优先，风格融合，色彩强调高对比度与品牌识别。"}, "color_palette": {"primary_color": "#1E40AF", "secondary_color": "#475569", "accent_color": "#3B82F6", "background_color": "#F8FAFC", "text_primary": "#1E293B", "text_secondary": "#64748B", "text_light": "#94A3B8", "success_color": "#10B981", "warning_color": "#F59E0B", "error_color": "#EF4444", "info_color": "#3B82F6", "gradient_primary": "linear-gradient(135deg, #1E40AF, #475569)", "gradient_accent": "linear-gradient(45deg, #3B82F6, #1E40AF)", "card_background": "#FFFFFF", "card_border": "#BAE6FD", "container_background": "#E0F2FE", "hover_color": "#7DD3FC", "active_color": "#1E40AF", "disabled_color": "#64748B"}, "layout_principles": {"canvas_size": {"width": 1920, "height": 1080}, "golden_ratio": 1.618, "grid_system": {"columns": 12, "gutter": 24, "margin": 80}, "spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "2xl": 48, "3xl": 64, "4xl": 96}, "page_margins": {"horizontal": 80, "vertical": 60, "content_max_width": 1760}, "content_spacing": {"module_gap": 32, "section_gap": 48, "element_gap": 16, "text_gap": 12}, "visual_hierarchy": {"z_index_background": 0, "z_index_content": 10, "z_index_overlay": 20, "z_index_modal": 30}, "alignment": {"text_align": "left", "content_align": "center", "title_align": "center"}}, "typography_system": {"primary_font": "Microsoft YaHei, Segoe UI, sans-serif", "secondary_font": "Source <PERSON> CN, Noto Sans CJK SC, sans-serif", "accent_font": "Times New Roman, serif", "font_sizes": {"hero_title": 72, "main_title": 56, "section_title": 36, "content_title": 28, "body_text": 22, "small_text": 16, "caption": 14}, "font_weights": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "black": 900}, "line_heights": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6, "loose": 1.8}, "letter_spacing": {"tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em"}}, "visual_elements": {"card_style": {"background": "#FFFFFF", "border_radius": 12, "border": "1px solid #BAE6FD", "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "hover_shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "decorative_elements": ["几何图形装饰", "渐变背景", "图标点缀", "分割线", "阴影效果"], "gradients": {"primary_gradient": "linear-gradient(135deg, #4A86E8, #3B82F6)", "accent_gradient": "linear-gradient(45deg, #0EA5E9, #06B6D4)", "background_gradient": "linear-gradient(180deg, #F8FAFC, #E0F2FE)", "text_gradient": "linear-gradient(135deg, #1E3A8A, #1E40AF)"}, "image_effects": {"border_radius": 8, "shadow_style": "0 4px 8px rgba(0, 0, 0, 0.1)", "overlay_style": "linear-gradient(135deg, #4A86E820, #3B82F620)", "hover_transform": "scale(1.02)", "filter_effects": "brightness(1.05) contrast(1.05)"}, "logo_effects": {"positioning": "左上角或页面顶部中央", "size_guidelines": "最大高度80px，宽度自适应", "background_treatment": "透明背景或浅色背景", "shadow": "subtle drop shadow", "hover_effect": "slight glow"}, "icon_system": {"style": "outline", "stroke_width": 2, "size_sm": 16, "size_md": 24, "size_lg": 32, "color": "#4A86E8"}, "dividers": {"horizontal_line": "1px solid #BAE6FD", "vertical_line": "1px solid #BAE6FD", "decorative_divider": "波浪线或装饰性分割", "gradient_divider": "linear-gradient(90deg, transparent, #7DD3FC, transparent)"}}, "template_consistency": {"scenario_coherence": "确保所有模板从内容结构、视觉元素到信息传达，都高度契合商业计划的场景需求和投资人预期。", "style_unity": "在所有模板中严格保持商务风格的一致性，包括字体、色彩、间距和模块化布局。", "color_harmony": "黑底特斯拉红高亮作为核心配色体系，辅以蓝色系，确保所有元素在色彩上和谐统一，形成强烈的品牌识别。", "visual_rhythm": "通过Bento Grid的模块化布局、超大字体的应用和精细的间距控制，创造协调且富有冲击力的视觉节奏和层次感。", "brand_consistency": "通过统一的Logo处理、色彩应用和视觉语言，确保整个演示文稿集合具有一致的品牌形象和专业识别度。"}, "created_at": "2025-07-02T20:18:01.376724", "scenario": "商业计划", "style": "商务", "canvas_size": {"width": 1920, "height": 1080}, "raw_response": "```json\n{\n    \"theme_selection\": {\n        \"primary_style\": \"商务风格\",\n        \"scenario_adaptation\": \"商业计划场景优化\",\n        \"visual_theme\": \"黑底特斯拉红高亮现代商务风格商业计划模板\",\n        \"design_philosophy\": \"结合投资人、合作伙伴需求的专业正式、简洁有力，体现权威和可信度，并融入科技感与视觉冲击力。\",\n        \"fusion_strategy\": \"场景优先，风格融合，色彩强调高对比度与品牌识别。\"\n    },\n    \"color_palette\": {\n        \"background_color\": \"#000000\",\n        \"highlight_color\": \"#E31937\",\n        \"highlight_transparent\": \"rgba(227, 25, 55, 0.7)\",\n        \"highlight_gradient_start\": \"#E31937\",\n        \"highlight_gradient_end\": \"rgba(227, 25, 55, 0.3)\",\n        \"primary_color\": \"#1E40AF\",\n        \"secondary_color\": \"#475569\",\n        \"text_primary\": \"#FFFFFF\",\n        \"text_secondary\": \"#86868B\",\n        \"text_subtle\": \"#F5F5F7\",\n        \"success_color\": \"#10B981\",\n        \"warning_color\": \"#F59E0B\",\n        \"error_color\": \"#EF4444\",\n        \"card_background\": \"#0D0D0D\",\n        \"card_border\": \"#1D1D1F\",\n        \"hover_color\": \"#E31937\",\n        \"note\": \"特斯拉红为主要高亮色，#1E40AF蓝色系作为辅助色，用于背景渐变或次要装饰，保持整体的现代感和深度。\"\n    },\n    \"layout_principles\": {\n        \"canvas_size\": {\"width\": 1920, \"height\": 1080},\n        \"golden_ratio_application\": true,\n        \"grid_system\": {\"columns\": 12, \"gutter\": 24, \"margin\": 80},\n        \"page_margins\": {\"horizontal\": 80, \"vertical\": 60},\n        \"content_spacing\": {\"module_gap\": 32, \"section_gap\": 48, \"element_gap\": 16},\n        \"visual_hierarchy\": \"通过Bento Grid和超大字体构建清晰、强烈的视觉层次结构，引导投资人快速聚焦核心信息。\",\n        \"alignment_system\": \"采用左对齐和居中对齐为主，确保内容的专业性和阅读流畅性。\"\n    },\n    \"typography_system\": {\n        \"primary_font\": \"system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'\",\n        \"font_sizes\": {\n            \"hero_title\": 120,\n            \"main_title\": 72,\n            \"section_title\": 48,\n            \"content_title\": 32,\n            \"body_text\": 24,\n            \"small_text\": 16,\n            \"accent_number\": 180\n        },\n        \"font_weights\": {\n            \"title\": \"bold\",\n            \"content\": \"normal\",\n            \"emphasis\": \"600\",\n            \"chinese\": \"bold\",\n            \"english\": \"300\"\n        },\n        \"line_heights\": {\n            \"title\": 1.1,\n            \"content\": 1.5,\n            \"dense\": 1.3\n        },\n        \"mixed_typography\": {\n            \"chinese_style\": \"大号粗体，作为视觉主导和概念表达\",\n            \"english_style\": \"小号细体，作为辅助说明、专业术语或点缀\",\n            \"number_style\": \"超大号突出，配合特斯拉红作为核心数据焦点\"\n        },\n        \"readability_optimization\": \"针对投资人、合作伙伴优化的可读性，确保在深色背景下文字对比度高，信息传达高效明确。\"\n    },\n    \"visual_elements\": {\n        \"scenario_specific_elements\": \"图表（简洁线条、面积图）、关键绩效指标（KPI）展示、市场趋势线、财务增长箭头、风险矩阵简化图形。\",\n        \"style_characteristics\": \"极简主义、高对比度、科技感、精确对齐、模块化结构。\",\n        \"bento_grid_layout\": \"采用类似Apple官网的Bento Grid网格布局，通过不同大小的区块组合（如2x1、1x1、1x2等），实现信息的分组和视觉趣味性，强调核心内容。\",\n        \"black_red_theme\": \"纯黑色背景(#000000)作为主调，特斯拉红色(#E31937)作为唯一的突出和高亮色，创造强烈的视觉冲击和品牌识别。\",\n        \"oversized_typography\": \"关键数字、百分比、核心标题或概念使用超大字号（如120px+），结合特斯拉红高亮，形成强大的视觉焦点和信息传达力。\",\n        \"decorative_elements\": [\n            \"简洁线条图形元素：用于数据可视化（如折线图、条形图的边框）、区块分割或背景装饰，保持极简和科技感。\",\n            \"特斯拉红色透明度渐变元素：仅使用特斯拉红色自身的透明度渐变（从高透明度到低透明度），营造科技感和深度，避免多色渐变。\",\n            \"中英文混排排版元素：中文大号粗体，英文小号细体，形成独特的国际化视觉效果。\",\n            \"符合商务风格的抽象几何形状：作为背景的低透明度纹理或角落的装饰，增强专业感。\",\n            \"微光效果：在关键高亮元素周围添加微弱的光晕或内阴影，增强立体感和高级感。\"\n        ],\n        \"card_style\": {\n            \"background\": \"#0D0D0D\",\n            \"border_radius\": \"24px\",\n            \"shadow\": \"极简的内阴影或柔和的外阴影（如：0 4px 8px rgba(0,0,0,0.4)），增强层次感而非突兀。\",\n            \"border\": \"1px 特斯拉红色细边框，或使用 highlight_gradient_start 到 highlight_gradient_end 的渐变边框，强化Bento Grid的边界感。\"\n        },\n        \"image_integration\": {\n            \"border_radius\": \"0px (或与卡片border_radius一致)\",\n            \"shadow_style\": \"无阴影或极简的内阴影，保持图片清晰。\",\n            \"overlay_style\": \"可选择性地使用特斯拉红色半透明遮罩（opacity 0.1-0.3），用于品牌统一或特殊强调。\",\n            \"bento_grid_placement\": \"图片作为Bento Grid中的一个模块，根据网格系统精确放置和裁剪，与文字和数据模块和谐共存。\"\n        },\n        \"logo_treatment\": {\n            \"positioning\": \"在封面页居中或左上角，内页左上角或右上角，保持统一且不干扰核心内容。\",\n            \"size_guidelines\": \"根据画布尺寸和页面内容，确保Logo清晰可见且比例协调，不过大或过小。\",\n            \"integration_style\": \"优先使用白色或单色版本的Logo，与黑底红高亮主题完美融合。\",\n            \"animation_hint\": \"考虑在滚动或页面切换时，Logo可以有微小的淡入淡出或缩放动效，增加交互感和高级感。\"\n        }\n    },\n    \"template_consistency\": {\n        \"scenario_coherence\": \"确保所有模板从内容结构、视觉元素到信息传达，都高度契合商业计划的场景需求和投资人预期。\",\n        \"style_unity\": \"在所有模板中严格保持商务风格的一致性，包括字体、色彩、间距和模块化布局。\",\n        \"color_harmony\": \"黑底特斯拉红高亮作为核心配色体系，辅以蓝色系，确保所有元素在色彩上和谐统一，形成强烈的品牌识别。\",\n        \"visual_rhythm\": \"通过Bento Grid的模块化布局、超大字体的应用和精细的间距控制，创造协调且富有冲击力的视觉节奏和层次感。\",\n        \"brand_consistency\": \"通过统一的Logo处理、色彩应用和视觉语言，确保整个演示文稿集合具有一致的品牌形象和专业识别度。\"\n    }\n}\n```"}}